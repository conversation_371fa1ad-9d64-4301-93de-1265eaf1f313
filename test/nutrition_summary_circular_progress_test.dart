import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/features/meal_planning/presentation/widgets/nutrition_summary_card.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';

void main() {
  group('Nutrition Summary Circular Progress Tests', () {
    late DayMealPlan testDay;

    setUp(() {
      // Create test data
      const testNutrition = NutritionInfo(
        calories: 2000,
        protein: 100.0,
        carbs: 250.0,
        fat: 70.0,
        fiber: 25.0,
      );

      testDay = DayMealPlan(
        date: DateTime.now(),
        meals: [],
        totalNutrition: testNutrition,
      );
    });

    testWidgets('CircularProgressIndicator should fill entire container', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the SizedBox that contains the circular progress
      final sizedBoxFinder = find.byType(SizedBox);
      expect(sizedBoxFinder, findsWidgets);

      // Find the specific SizedBox with 50x50 dimensions (the container)
      final containerSizedBox = tester.widgetList<SizedBox>(sizedBoxFinder)
          .firstWhere((sizedBox) => sizedBox.width == 50 && sizedBox.height == 50);
      
      expect(containerSizedBox.width, 50);
      expect(containerSizedBox.height, 50);

      // Find the SizedBox.expand that wraps the CircularProgressIndicator
      final expandedSizedBox = tester.widgetList<SizedBox>(sizedBoxFinder)
          .firstWhere((sizedBox) => sizedBox.width == double.infinity && sizedBox.height == double.infinity);
      
      expect(expandedSizedBox.width, double.infinity);
      expect(expandedSizedBox.height, double.infinity);
    });

    testWidgets('CircularProgressIndicator should maintain proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the CircularProgressIndicator
      final circularProgressFinder = find.byType(CircularProgressIndicator);
      expect(circularProgressFinder, findsOneWidget);

      final circularProgress = tester.widget<CircularProgressIndicator>(circularProgressFinder);
      
      // Verify styling properties
      expect(circularProgress.strokeWidth, 4);
      expect(circularProgress.backgroundColor, Colors.white.withOpacity(0.3));
      expect(circularProgress.valueColor, isA<AlwaysStoppedAnimation<Color>>());
      
      final valueColor = circularProgress.valueColor as AlwaysStoppedAnimation<Color>;
      expect(valueColor.value, Colors.white);
    });

    testWidgets('Background container should fill entire space', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the background Container
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsWidgets);

      // Find the container with circular shape (background circle)
      final backgroundContainers = tester.widgetList<Container>(containerFinder)
          .where((container) {
            final decoration = container.decoration;
            return decoration is BoxDecoration && decoration.shape == BoxShape.circle;
          });

      expect(backgroundContainers.length, greaterThanOrEqualTo(1));

      final backgroundContainer = backgroundContainers.first;

      final decoration = backgroundContainer.decoration as BoxDecoration;
      expect(decoration.color, Colors.white.withOpacity(0.2));
      expect(decoration.shape, BoxShape.circle);
    });

    testWidgets('Progress text should remain centered', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the Center widget that contains the percentage text
      final centerFinder = find.byType(Center);
      expect(centerFinder, findsWidgets);

      // Find the percentage text
      final textFinder = find.textContaining('%');
      expect(textFinder, findsOneWidget);

      // Verify the text is inside a Center widget
      final centerWidget = tester.widget<Center>(
        find.ancestor(
          of: textFinder,
          matching: find.byType(Center),
        ),
      );
      
      expect(centerWidget, isNotNull);
    });

    testWidgets('Stack should contain all required elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the Stack widget that contains the circular progress
      final stackFinder = find.byType(Stack);
      expect(stackFinder, findsWidgets);

      // Find the specific stack that contains our circular progress elements
      final stacks = tester.widgetList<Stack>(stackFinder);
      final progressStack = stacks.firstWhere((stack) => stack.children.length == 3);

      expect(progressStack.children.length, 3); // Background container, progress indicator, text

      // Verify the order of children in the stack
      expect(progressStack.children[0], isA<Container>()); // Background circle
      expect(progressStack.children[1], isA<SizedBox>()); // Progress indicator wrapper
      expect(progressStack.children[2], isA<Center>()); // Centered text
    });

    testWidgets('Progress indicator should display correct progress value', (WidgetTester tester) async {
      // Create a day with specific nutrition for testing progress calculation
      const halfNutrition = NutritionInfo(
        calories: 1000, // Half of the total
        protein: 50.0,
        carbs: 125.0,
        fat: 35.0,
        fiber: 12.5,
      );

      final testDayWithProgress = DayMealPlan(
        date: DateTime.now(),
        meals: [],
        totalNutrition: halfNutrition,
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDayWithProgress),
            ),
          ),
        ),
      );

      // Find the CircularProgressIndicator
      final circularProgressFinder = find.byType(CircularProgressIndicator);
      expect(circularProgressFinder, findsOneWidget);

      final circularProgress = tester.widget<CircularProgressIndicator>(circularProgressFinder);
      
      // Since no meals are consumed, progress should be 0
      expect(circularProgress.value, 0.0);
    });

    testWidgets('Container dimensions should be maintained', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NutritionSummaryCard(day: testDay),
            ),
          ),
        ),
      );

      // Find the outer SizedBox that defines the container size
      final outerSizedBoxFinder = find.byType(SizedBox);
      final outerSizedBox = tester.widgetList<SizedBox>(outerSizedBoxFinder)
          .firstWhere((sizedBox) => sizedBox.width == 50 && sizedBox.height == 50);

      expect(outerSizedBox.width, 50);
      expect(outerSizedBox.height, 50);

      // Verify that the progress indicator fills this space
      final expandedSizedBox = tester.widgetList<SizedBox>(outerSizedBoxFinder)
          .firstWhere((sizedBox) => sizedBox.width == double.infinity && sizedBox.height == double.infinity);

      expect(expandedSizedBox.width, double.infinity);
      expect(expandedSizedBox.height, double.infinity);
    });
  });
}
