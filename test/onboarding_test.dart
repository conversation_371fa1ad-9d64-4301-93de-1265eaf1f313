import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easydietai/features/onboarding/providers/onboarding_provider.dart';
import 'package:easydietai/features/onboarding/presentation/widgets/personal_info_step.dart';
import 'package:easydietai/features/onboarding/presentation/widgets/physical_info_step.dart';
import 'package:easydietai/features/onboarding/presentation/widgets/goals_step.dart';

void main() {
  group('Onboarding Pre-fill Test', () {
    testWidgets('PersonalInfoStep should update when preFillWithTestData is called', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Scaffold(
              body: PersonalInfoStep(),
            ),
          ),
        ),
      );

      // Get the notifier
      final notifier = container.read(onboardingNotifierProvider.notifier);
      
      // Pre-fill with test data
      notifier.preFillWithTestData();
      
      // Pump the widget to trigger rebuilds
      await tester.pump();
      
      // Check if the text fields are populated
      final firstNameField = find.byType(TextFormField).first;
      final lastNameField = find.byType(TextFormField).at(1);
      
      expect(firstNameField, findsOneWidget);
      expect(lastNameField, findsOneWidget);
      
      // The text fields should have some content after pre-filling
      final firstNameWidget = tester.widget<TextFormField>(firstNameField);
      final lastNameWidget = tester.widget<TextFormField>(lastNameField);
      
      expect(firstNameWidget.controller?.text.isNotEmpty, true);
      expect(lastNameWidget.controller?.text.isNotEmpty, true);
      
      container.dispose();
    });

    testWidgets('PhysicalInfoStep should update when preFillWithTestData is called', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Get the notifier
      final notifier = container.read(onboardingNotifierProvider.notifier);
      
      // Pre-fill with test data
      notifier.preFillWithTestData();
      
      // Pump the widget to trigger rebuilds
      await tester.pump();
      
      // Check if the text fields are populated
      final heightField = find.byType(TextFormField).first;
      final weightField = find.byType(TextFormField).at(1);
      
      expect(heightField, findsOneWidget);
      expect(weightField, findsOneWidget);
      
      // The text fields should have some content after pre-filling
      final heightWidget = tester.widget<TextFormField>(heightField);
      final weightWidget = tester.widget<TextFormField>(weightField);
      
      expect(heightWidget.controller?.text.isNotEmpty, true);
      expect(weightWidget.controller?.text.isNotEmpty, true);
      
      container.dispose();
    });

    testWidgets('GoalsStep should update when preFillWithTestData is called', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Scaffold(
              body: GoalsStep(),
            ),
          ),
        ),
      );

      // Get the notifier
      final notifier = container.read(onboardingNotifierProvider.notifier);
      
      // Pre-fill with test data
      notifier.preFillWithTestData();
      
      // Pump the widget to trigger rebuilds
      await tester.pump();
      
      // Check if the calorie goal field is populated
      final calorieField = find.byType(TextFormField).last;
      
      expect(calorieField, findsOneWidget);
      
      // The text field should have some content after pre-filling
      final calorieWidget = tester.widget<TextFormField>(calorieField);
      
      expect(calorieWidget.controller?.text.isNotEmpty, true);
      
      container.dispose();
    });
  });
}
