import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:easydietai/shared/providers/app_state_provider.dart';
import 'package:easydietai/core/services/storage_service.dart';

void main() {
  group('Guest User Status Integration Tests', () {
    late ProviderContainer container;
    late StorageService storageService;

    setUpAll(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      // Initialize storage service once for all tests
      storageService = StorageService.instance;
      await storageService.init();
    });

    setUp(() async {
      // Clear storage before each test
      await storageService.clear();

      container = ProviderContainer(
        overrides: [
          storageServiceProvider.overrideWithValue(storageService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('setGuestUser(false) should clear is_guest_user flag from storage', () async {
      // Set initial guest user status in storage
      await storageService.setBool('is_guest_user', true);
      
      // Verify initial state
      expect(storageService.getBool('is_guest_user'), isTrue);

      // Get the app state notifier
      final appStateNotifier = container.read(appStateNotifierProvider.notifier);

      // Call setGuestUser(false) to clear guest status
      await appStateNotifier.setGuestUser(false);

      // Verify that is_guest_user flag has been cleared from storage
      expect(storageService.getBool('is_guest_user'), isFalse);
      
      // Verify app state reflects user is no longer a guest
      final appState = container.read(appStateNotifierProvider);
      expect(appState.isGuestUser, isFalse);
    });

    test('storage.remove should completely remove is_guest_user key', () async {
      // Set initial guest user status in storage
      await storageService.setBool('is_guest_user', true);
      
      // Verify initial state
      expect(storageService.getBool('is_guest_user'), isTrue);
      expect(storageService.containsKey('is_guest_user'), isTrue);

      // Remove the key completely
      await storageService.remove('is_guest_user');

      // Verify that key has been completely removed
      expect(storageService.containsKey('is_guest_user'), isFalse);
      expect(storageService.getBool('is_guest_user'), isNull);
    });

    test('app state should initialize with correct guest user status from storage', () async {
      // Set guest user status in storage before initializing app state
      await storageService.setBool('is_guest_user', true);

      // Wait for app state to initialize
      await container.read(appStateNotifierProvider.notifier).initialize();

      // Verify app state loaded the guest user status from storage
      final appState = container.read(appStateNotifierProvider);
      expect(appState.isGuestUser, isTrue);
    });
  });
}
