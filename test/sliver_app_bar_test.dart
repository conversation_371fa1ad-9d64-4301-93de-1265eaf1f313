import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/features/meal_planning/presentation/widgets/home_app_bar.dart';
import 'package:easydietai/features/meal_planning/presentation/widgets/meal_plan_card_view.dart';
import 'package:easydietai/features/meal_planning/presentation/widgets/day_navigation_header.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';

void main() {
  group('SliverAppBar Integration Tests', () {
    late List<DayMealPlan> testDays;

    setUp(() {
      // Create test data
      const testNutrition = NutritionInfo(
        calories: 2000,
        protein: 100.0,
        carbs: 250.0,
        fat: 70.0,
        fiber: 25.0,
      );

      testDays = [
        DayMealPlan(
          date: DateTime.now().subtract(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now(),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now().add(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
      ];
    });

    testWidgets('SliverAppBar should be included when includeAppBar is true', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify SliverAppBar is present
      expect(find.byType(SliverAppBar), findsOneWidget);
      expect(find.text('خطة الوجبات'), findsOneWidget);
    });

    testWidgets('SliverAppBar should not be included when includeAppBar is false', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: false,
              ),
            ),
          ),
        ),
      );

      // Verify SliverAppBar is not present
      expect(find.byType(SliverAppBar), findsNothing);
    });

    testWidgets('SliverAppBar should have floating and snap properties', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Find the SliverAppBar widget
      final sliverAppBar = tester.widget<SliverAppBar>(find.byType(SliverAppBar));
      
      // Verify floating and snap properties for hide/show on scroll behavior
      expect(sliverAppBar.floating, true);
      expect(sliverAppBar.snap, true);
    });

    testWidgets('SliverAppBar should contain all expected elements', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify app bar title
      expect(find.text('خطة الوجبات'), findsOneWidget);
      
      // Verify notification icon button (leading)
      expect(find.byType(GestureDetector), findsWidgets);
      
      // Verify profile avatar (trailing)
      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    testWidgets('SliverAppBar should work with CustomScrollView', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify CustomScrollView is present
      expect(find.byType(CustomScrollView), findsOneWidget);
      
      // Verify SliverAppBar is the first sliver
      final customScrollView = tester.widget<CustomScrollView>(find.byType(CustomScrollView));
      expect(customScrollView.slivers.first, isA<SliverAppBar>());
    });

    testWidgets('SliverAppBar should work with sticky day navigation header', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify both SliverAppBar and SliverPersistentHeader are present
      expect(find.byType(SliverAppBar), findsOneWidget);
      expect(find.byType(SliverPersistentHeader), findsNWidgets(2)); // One for SliverAppBar, one for sticky header
      
      // Verify the day navigation header is still present
      expect(find.byType(DayNavigationHeader), findsOneWidget);
    });
  });
}
