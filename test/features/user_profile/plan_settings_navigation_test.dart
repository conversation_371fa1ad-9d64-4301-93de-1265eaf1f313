import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:easydietai/features/user_profile/presentation/pages/plan_settings_page.dart';
import 'package:easydietai/features/onboarding/presentation/pages/settings_onboarding_page.dart';
import 'package:easydietai/features/onboarding/providers/onboarding_provider.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/shared/models/user_profile.dart';
import 'package:easydietai/generated/l10n/app_localizations.dart';

void main() {
  group('Plan Settings Navigation Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('should navigate to edit mode without crashing', (WidgetTester tester) async {
      // Create a mock router
      final router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder: (context, state) => const PlanSettingsPage(),
          ),
          GoRoute(
            path: '/profile/settings/personal',
            builder: (context, state) {
              final stepParam = state.uri.queryParameters['step'];
              final editModeParam = state.uri.queryParameters['edit'];
              OnboardingStep? initialStep;
              bool isEditMode = editModeParam == 'true';

              if (stepParam != null) {
                try {
                  initialStep = OnboardingStep.values.firstWhere(
                    (step) => step.name == stepParam,
                  );
                } catch (e) {
                  initialStep = null;
                }
              }

              return SettingsOnboardingPage(
                initialStep: initialStep,
                isEditMode: isEditMode,
              );
            },
          ),
        ],
      );

      // Build the widget with provider scope
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp.router(
            routerConfig: router,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the plan settings page loads
      expect(find.byType(PlanSettingsPage), findsOneWidget);
    });

    testWidgets('should handle edit button navigation gracefully', (WidgetTester tester) async {
      // Pre-populate some test data in the provider
      final notifier = container.read(onboardingNotifierProvider.notifier);
      notifier.updatePersonalInfo(
        dateOfBirth: DateTime(1990, 1, 1),
        gender: Gender.male,
      );
      notifier.updatePhysicalInfo(
        height: 175.0,
        weight: 70.0,
        activityLevel: ActivityLevel.moderate,
      );

      // Create a test widget that simulates the navigation
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const SettingsOnboardingPage(
              initialStep: OnboardingStep.personalInfo,
              isEditMode: true,
            ),
          ),
        ),
      );

      // Wait for the widget to build and data to load
      await tester.pumpAndSettle();

      // Verify that the settings onboarding page loads without crashing
      expect(find.byType(SettingsOnboardingPage), findsOneWidget);
      
      // Verify that the page shows edit mode content
      expect(find.text('تعديل المعلومات الشخصية'), findsOneWidget);
      
      // Verify that the save button is present
      expect(find.text('حفظ التغييرات'), findsOneWidget);
    });

    testWidgets('should handle missing data gracefully in edit mode', (WidgetTester tester) async {
      // Don't pre-populate any data to test empty state handling
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const SettingsOnboardingPage(
              initialStep: OnboardingStep.personalInfo,
              isEditMode: true,
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the page loads even with no data
      expect(find.byType(SettingsOnboardingPage), findsOneWidget);
      
      // The page should still show the edit form
      expect(find.text('تعديل المعلومات الشخصية'), findsOneWidget);
    });

    testWidgets('should handle different onboarding steps in edit mode', (WidgetTester tester) async {
      final steps = [
        OnboardingStep.personalInfo,
        OnboardingStep.physicalInfo,
        OnboardingStep.healthInfo,
        OnboardingStep.goals,
        OnboardingStep.preferences,
      ];

      for (final step in steps) {
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              home: SettingsOnboardingPage(
                initialStep: step,
                isEditMode: true,
              ),
            ),
          ),
        );

        // Wait for the widget to build
        await tester.pumpAndSettle();

        // Verify that each step loads without crashing
        expect(find.byType(SettingsOnboardingPage), findsOneWidget);
        
        // Clean up for next iteration
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/navigation',
          null,
          (data) {},
        );
      }
    });
  });
}
