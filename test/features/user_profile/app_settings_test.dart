import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:easydietai/features/user_profile/presentation/pages/app_settings_page.dart';
import 'package:easydietai/features/user_profile/presentation/pages/faq_page.dart';
import 'package:easydietai/features/user_profile/presentation/pages/subscription_page.dart';
import 'package:easydietai/features/user_profile/presentation/pages/help_support_page.dart';
import 'package:easydietai/features/user_profile/presentation/pages/about_app_page.dart';
import 'package:easydietai/shared/providers/app_state_provider.dart';

void main() {
  group('App Settings Pages Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [
          // Mock the app state provider
          appStateNotifierProvider.overrideWith(() => MockAppStateNotifier()),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('AppSettingsPage displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: const AppSettingsPage(),
          ),
        ),
      );

      // Verify page title
      expect(find.text('إعدادات التطبيق'), findsOneWidget);
      
      // Verify language section
      expect(find.text('اللغة والمظهر'), findsOneWidget);
      expect(find.text('اللغة'), findsOneWidget);
      expect(find.text('المظهر'), findsOneWidget);
      
      // Verify help section
      expect(find.text('المساعدة والدعم'), findsOneWidget);
      expect(find.text('الأسئلة الشائعة'), findsOneWidget);
      
      // Verify danger zone
      expect(find.text('المنطقة الخطرة'), findsOneWidget);
      expect(find.text('حذف الحساب'), findsOneWidget);
    });

    testWidgets('FaqPage displays FAQ items', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const FaqPage(),
        ),
      );

      // Verify page title appears (may appear multiple times in app bar and header)
      expect(find.text('الأسئلة الشائعة'), findsAtLeastNWidgets(1));

      // Verify some FAQ questions
      expect(find.text('كيف يعمل تطبيق EasyDietAI؟'), findsOneWidget);
      expect(find.text('كيف يمكنني تغيير خطة الوجبات؟'), findsOneWidget);
    });

    testWidgets('SubscriptionPage displays premium features', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: const SubscriptionPage(),
          ),
        ),
      );

      // Verify page title
      expect(find.text('الاشتراك المميز'), findsOneWidget);
      
      // Verify features section
      expect(find.text('المزايا المتاحة'), findsOneWidget);
      expect(find.text('خطط وجبات غير محدودة'), findsOneWidget);
      expect(find.text('تحليل غذائي متقدم'), findsOneWidget);
    });

    testWidgets('HelpSupportPage displays contact methods', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const HelpSupportPage(),
        ),
      );

      // Verify page title
      expect(find.text('المساعدة والدعم'), findsOneWidget);
      
      // Verify contact methods
      expect(find.text('طرق التواصل'), findsOneWidget);
      expect(find.text('البريد الإلكتروني'), findsOneWidget);
      expect(find.text('واتساب'), findsOneWidget);
    });

    testWidgets('AboutAppPage displays app information', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const AboutAppPage(),
        ),
      );

      // Verify page title
      expect(find.text('حول التطبيق'), findsOneWidget);
      
      // Verify app name and version
      expect(find.text('EasyDietAI'), findsOneWidget);
      expect(find.text('الإصدار 1.0.0'), findsOneWidget);
      
      // Verify features section
      expect(find.text('المزايا الرئيسية'), findsOneWidget);
      expect(find.text('خطط وجبات ذكية'), findsOneWidget);
    });
  });
}

// Mock AppStateNotifier for testing
class MockAppStateNotifier extends AppStateNotifier {
  @override
  AppState build() {
    return const AppState(
      isInitialized: true,
      currentLanguage: 'ar',
      currentTheme: 'system',
    );
  }

  @override
  Future<void> changeLanguage(String languageCode) async {
    state = state.copyWith(currentLanguage: languageCode);
  }

  @override
  Future<void> changeTheme(String theme) async {
    state = state.copyWith(currentTheme: theme);
  }
}
