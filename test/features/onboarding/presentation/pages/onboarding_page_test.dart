import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:easydietai/features/onboarding/presentation/pages/onboarding_page.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/generated/l10n/app_localizations.dart';

void main() {
  group('OnboardingPage PageController Initialization', () {
    late ProviderContainer container;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      final prefs = await SharedPreferences.getInstance();
      
      container = ProviderContainer(
        overrides: [
          localStorageServiceProvider.overrideWith(
            (ref) async => LocalStorageService(prefs),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('should not throw LateInitializationError when opening onboarding page', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const OnboardingPage(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the widget builds without throwing LateInitializationError
      expect(find.byType(OnboardingPage), findsOneWidget);
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('should initialize PageController with initialPage 0 by default', (WidgetTester tester) async {
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const OnboardingPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the PageView widget
      final pageViewFinder = find.byType(PageView);
      expect(pageViewFinder, findsOneWidget);

      // Verify PageView is on the first page (index 0)
      final PageView pageView = tester.widget(pageViewFinder);
      expect(pageView.controller?.page, equals(0.0));
    });

    testWidgets('should handle initialStepIndex parameter correctly', (WidgetTester tester) async {
      const int initialStep = 2; // Third step (healthInfo)
      
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            localizationsDelegates: AppLocalizations.localizationsDelegates,
            supportedLocales: AppLocalizations.supportedLocales,
            home: const OnboardingPage(initialStepIndex: initialStep),
          ),
        ),
      );

      // Wait for initial build
      await tester.pump();
      
      // Wait for async initialization to complete
      await tester.pumpAndSettle();

      // Verify that the widget builds without errors
      expect(find.byType(OnboardingPage), findsOneWidget);
      expect(find.byType(PageView), findsOneWidget);
    });
  });
}
