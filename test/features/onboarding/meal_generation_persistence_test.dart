import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/features/onboarding/providers/onboarding_provider.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Meal Generation Loading State Persistence', () {
    late LocalStorageService localStorageService;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      final prefs = await SharedPreferences.getInstance();
      
      // Initialize LocalStorageService
      localStorageService = LocalStorageService(prefs);
    });

    tearDown(() {
      // No cleanup needed for LocalStorageService
    });

    test('should save and load meal generation loading state with timestamp', () async {
      // Save loading state
      final success = await localStorageService.saveMealGenerationLoading(true);
      expect(success, isTrue);

      // Load loading state
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNotNull);
      expect(loadingData!['isLoading'], isTrue);
      expect(loadingData['timestamp'], isA<int>());
      expect(loadingData['timestamp'], greaterThan(0));
    });

    test('should clear meal generation loading state', () async {
      // Save loading state first
      await localStorageService.saveMealGenerationLoading(true);
      expect(localStorageService.loadMealGenerationLoading(), isNotNull);

      // Clear loading state
      final success = await localStorageService.clearMealGenerationLoading();
      expect(success, isTrue);

      // Verify it's cleared
      expect(localStorageService.loadMealGenerationLoading(), isNull);
    });

    test('should detect recent meal generation loading (< 5 minutes)', () async {
      // Save loading state
      await localStorageService.saveMealGenerationLoading(true);
      
      // Load and check timestamp
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNotNull);
      
      final timestamp = loadingData!['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - timestamp;
      
      // Should be very recent (less than 1 second)
      expect(timeDifference, lessThan(1000));
      
      // Should be considered recent (< 5 minutes)
      const fiveMinutesInMs = 5 * 60 * 1000;
      expect(timeDifference, lessThan(fiveMinutesInMs));
    });

    test('should detect expired meal generation loading (>= 5 minutes)', () async {
      // Manually create expired loading data
      final fiveMinutesAgo = DateTime.now().millisecondsSinceEpoch - (5 * 60 * 1000);
      final expiredLoadingData = {
        'isLoading': true,
        'timestamp': fiveMinutesAgo,
      };
      
      // Save expired data directly to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('meal_generation_loading', 
          '{"isLoading":true,"timestamp":$fiveMinutesAgo}');
      
      // Load and check
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNotNull);
      
      final timestamp = loadingData!['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - timestamp;
      
      // Should be 5 minutes or older
      const fiveMinutesInMs = 5 * 60 * 1000;
      expect(timeDifference, greaterThanOrEqualTo(fiveMinutesInMs));
    });

    test('should handle missing meal generation loading data', () async {
      // Ensure no loading data exists
      await localStorageService.clearMealGenerationLoading();
      
      // Load should return null
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNull);
    });

    test('should handle corrupted meal generation loading data', () async {
      // Save corrupted data directly to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('meal_generation_loading', 'invalid_json');
      
      // Load should return null for corrupted data
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNull);
    });

    test('OnboardingStep.complete should be at index 6', () {
      // Verify the complete step is at the expected index
      final completeStepIndex = OnboardingStep.values.indexOf(OnboardingStep.complete);
      expect(completeStepIndex, equals(6));
      
      // Verify all steps are in expected order
      expect(OnboardingStep.values, equals([
        OnboardingStep.personalInfo,    // 0
        OnboardingStep.physicalInfo,    // 1
        OnboardingStep.healthInfo,      // 2
        OnboardingStep.goals,           // 3
        OnboardingStep.preferences,     // 4
        OnboardingStep.notifications,   // 5
        OnboardingStep.complete,        // 6
      ]));
    });

    test('should simulate onboarding navigation to complete step for active meal generation', () async {
      // Simulate active meal generation
      await localStorageService.saveMealGenerationLoading(true);
      
      // Verify loading data exists and is recent
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNotNull);
      expect(loadingData!['isLoading'], isTrue);
      
      final timestamp = loadingData['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = now - timestamp;
      
      // Should be recent (< 5 minutes)
      const fiveMinutesInMs = 5 * 60 * 1000;
      expect(timeDifference, lessThan(fiveMinutesInMs));
      
      // This simulates what onboarding_page.dart should do:
      // Navigate to complete step (index 6)
      final targetPage = OnboardingStep.values.indexOf(OnboardingStep.complete);
      expect(targetPage, equals(6));
    });

    test('should simulate complete step handling of existing meal generation', () async {
      // Simulate recent meal generation (< 5 minutes)
      await localStorageService.saveMealGenerationLoading(true);
      
      final loadingData = localStorageService.loadMealGenerationLoading();
      expect(loadingData, isNotNull);
      
      final isLoading = loadingData!['isLoading'] as bool? ?? false;
      final timestamp = loadingData['timestamp'] as int? ?? 0;
      
      expect(isLoading, isTrue);
      expect(timestamp, greaterThan(0));
      
      final now = DateTime.now().millisecondsSinceEpoch;
      final fiveMinutesInMs = 5 * 60 * 1000;
      final timeDifference = now - timestamp;
      
      if (timeDifference < fiveMinutesInMs) {
        // Should set to generating mode without calling function
        // This simulates the complete_step.dart behavior
        expect(timeDifference, lessThan(fiveMinutesInMs));
      } else {
        // Should restart generation
        expect(timeDifference, greaterThanOrEqualTo(fiveMinutesInMs));
      }
    });
  });
}
