import 'package:flutter_test/flutter_test.dart';
import 'package:easydietai/features/weight_tracking/data/models/weight_tracking_models.dart';

void main() {
  group('Weight Entry Delete Restriction Tests', () {
    test('should identify oldest entry correctly', () {
      // Create test weight entries with different dates
      final entries = [
        WeightEntry(
          id: 'entry1',
          weight: 70.0,
          date: DateTime(2024, 1, 1), // This should be the oldest
          createdAt: DateTime(2024, 1, 1),
        ),
        WeightEntry(
          id: 'entry2',
          weight: 72.0,
          date: DateTime(2024, 1, 15), // This is the latest
          createdAt: DateTime(2024, 1, 15),
        ),
        WeightEntry(
          id: 'entry3',
          weight: 71.0,
          date: DateTime(2024, 1, 8), // This is in the middle
          createdAt: DateTime(2024, 1, 8),
        ),
      ];

      final weightData = WeightTrackingData(entries: entries);

      // Test that the correct entry is identified as oldest (should not be deletable)
      expect(weightData.isOldestEntry('entry1'), isTrue);
      expect(weightData.isOldestEntry('entry2'), isFalse);
      expect(weightData.isOldestEntry('entry3'), isFalse);

      // Test that the correct entry is identified as latest (for plan preferences sync)
      expect(weightData.isLatestEntry('entry2'), isTrue);
      expect(weightData.isLatestEntry('entry1'), isFalse);
      expect(weightData.isLatestEntry('entry3'), isFalse);
    });

    test('should handle single entry as both oldest and latest', () {
      final entries = [
        WeightEntry(
          id: 'single_entry',
          weight: 75.0,
          date: DateTime(2024, 1, 1),
          createdAt: DateTime(2024, 1, 1),
        ),
      ];

      final weightData = WeightTrackingData(entries: entries);

      // Single entry should be both oldest and latest (and therefore not deletable)
      expect(weightData.isOldestEntry('single_entry'), isTrue);
      expect(weightData.isLatestEntry('single_entry'), isTrue);
    });

    test('should handle empty entries list', () {
      final weightData = WeightTrackingData(entries: []);

      // No entries means no oldest or latest entry
      expect(weightData.isOldestEntry('any_id'), isFalse);
      expect(weightData.isLatestEntry('any_id'), isFalse);
    });

    test('should handle entries with same date', () {
      final sameDate = DateTime(2024, 1, 1);
      final entries = [
        WeightEntry(
          id: 'entry1',
          weight: 70.0,
          date: sameDate,
          createdAt: DateTime(2024, 1, 1, 10, 0), // Earlier creation time
        ),
        WeightEntry(
          id: 'entry2',
          weight: 72.0,
          date: sameDate,
          createdAt: DateTime(2024, 1, 1, 12, 0), // Later creation time
        ),
      ];

      final weightData = WeightTrackingData(entries: entries);

      // When dates are the same, the reduce function will pick one consistently
      // The exact behavior depends on the reduce implementation, but it should be consistent
      final oldestEntryId = weightData.oldestEntry?.id;
      final latestEntryId = weightData.latestEntry?.id;
      expect(oldestEntryId, isNotNull);
      expect(latestEntryId, isNotNull);
      expect(weightData.isOldestEntry(oldestEntryId!), isTrue);
      expect(weightData.isLatestEntry(latestEntryId!), isTrue);
    });

    test('should correctly identify oldest entry after updates', () {
      // Start with initial entries
      var entries = [
        WeightEntry(
          id: 'entry1',
          weight: 70.0,
          date: DateTime(2024, 1, 8), // Middle date
          createdAt: DateTime(2024, 1, 8),
        ),
        WeightEntry(
          id: 'entry2',
          weight: 72.0,
          date: DateTime(2024, 1, 15), // Latest date
          createdAt: DateTime(2024, 1, 15),
        ),
      ];

      var weightData = WeightTrackingData(entries: entries);
      expect(weightData.isOldestEntry('entry1'), isTrue); // entry1 is oldest
      expect(weightData.isLatestEntry('entry2'), isTrue); // entry2 is latest

      // Add an even older entry
      entries = [
        WeightEntry(
          id: 'entry0',
          weight: 68.0,
          date: DateTime(2024, 1, 1), // Oldest date
          createdAt: DateTime(2024, 1, 1),
        ),
        ...entries,
      ];

      weightData = WeightTrackingData(entries: entries);

      // Now entry0 should be the oldest (protected from deletion)
      expect(weightData.isOldestEntry('entry0'), isTrue);
      expect(weightData.isOldestEntry('entry1'), isFalse);
      expect(weightData.isOldestEntry('entry2'), isFalse);

      // entry2 should still be the latest (used for plan preferences)
      expect(weightData.isLatestEntry('entry2'), isTrue);
      expect(weightData.isLatestEntry('entry1'), isFalse);
      expect(weightData.isLatestEntry('entry0'), isFalse);
    });
  });
}
