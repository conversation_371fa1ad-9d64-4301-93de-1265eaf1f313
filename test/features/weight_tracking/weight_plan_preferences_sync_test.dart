import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easydietai/features/weight_tracking/presentation/providers/weight_tracking_provider.dart';
import 'package:easydietai/shared/providers/app_state_provider.dart';
import 'package:easydietai/shared/models/plan_preferences.dart';
import 'package:easydietai/shared/models/user_profile.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/services/firestore_service.dart';

void main() {
  group('Weight Tracking Plan Preferences Sync Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [
          // Mock the local storage service
          localStorageServiceProvider.overrideWith((ref) async {
            return MockLocalStorageService();
          }),
          // Mock the firestore service
          firestoreServiceProvider.overrideWith((ref) {
            return MockFirestoreService();
          }),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('should update plan preferences weight when adding latest weight entry', (tester) async {
      // Setup initial plan preferences with a weight
      final initialPreferences = PlanPreferences(
        weight: 70.0,
        height: 175.0,
        gender: Gender.male,
      );

      // Mock auth state with plan preferences
      container.read(appStateNotifierProvider.notifier).state = AppState(
        authStatus: AuthStatus.authenticated,
        user: null, // Mock user would be here in real test
        planPreferences: initialPreferences,
      );

      // Add a weight entry that should be the latest
      final weightNotifier = container.read(weightTrackingNotifierProvider.notifier);
      
      await weightNotifier.addWeightEntry(
        weight: 72.5,
        date: DateTime.now(),
        notes: 'Test weight entry',
      );

      // Verify that the plan preferences weight was updated
      final updatedPreferences = container.read(currentPlanPreferencesProvider);
      expect(updatedPreferences?.weight, equals(72.5));
    });

    testWidgets('should not update plan preferences weight when adding older weight entry', (tester) async {
      // Setup initial plan preferences
      final initialPreferences = PlanPreferences(
        weight: 70.0,
        height: 175.0,
        gender: Gender.male,
      );

      container.read(appStateNotifierProvider.notifier).state = AppState(
        authStatus: AuthStatus.authenticated,
        user: null,
        planPreferences: initialPreferences,
      );

      final weightNotifier = container.read(weightTrackingNotifierProvider.notifier);
      
      // Add a future weight entry first (this will be the latest)
      await weightNotifier.addWeightEntry(
        weight: 75.0,
        date: DateTime.now().add(Duration(days: 1)),
        notes: 'Future weight entry',
      );

      // Add an older weight entry (this should not update plan preferences)
      await weightNotifier.addWeightEntry(
        weight: 68.0,
        date: DateTime.now().subtract(Duration(days: 1)),
        notes: 'Older weight entry',
      );

      // Verify that the plan preferences weight was not updated to the older entry
      final updatedPreferences = container.read(currentPlanPreferencesProvider);
      expect(updatedPreferences?.weight, equals(75.0)); // Should still be the latest entry
    });

    testWidgets('should update plan preferences weight when updating the latest weight entry', (tester) async {
      // Setup initial plan preferences
      final initialPreferences = PlanPreferences(
        weight: 70.0,
        height: 175.0,
        gender: Gender.male,
      );

      container.read(appStateNotifierProvider.notifier).state = AppState(
        authStatus: AuthStatus.authenticated,
        user: null,
        planPreferences: initialPreferences,
      );

      final weightNotifier = container.read(weightTrackingNotifierProvider.notifier);
      
      // Add initial weight entry
      await weightNotifier.addWeightEntry(
        weight: 72.0,
        date: DateTime.now(),
        notes: 'Initial weight entry',
      );

      // Get the entry ID (in real implementation, this would be available)
      final weightData = container.read(weightTrackingDataProvider);
      final entryId = weightData.entries.first.id;

      // Update the weight entry
      await weightNotifier.updateWeightEntry(
        entryId: entryId,
        weight: 73.5,
        date: DateTime.now(),
        notes: 'Updated weight entry',
      );

      // Verify that the plan preferences weight was updated
      final updatedPreferences = container.read(currentPlanPreferencesProvider);
      expect(updatedPreferences?.weight, equals(73.5));
    });
  });
}

// Mock classes for testing
class MockLocalStorageService extends LocalStorageService {
  final Map<String, dynamic> _storage = {};

  @override
  Future<bool> saveWeightTrackingData(Map<String, dynamic> data) async {
    _storage['weight_tracking_data'] = data;
    return true;
  }

  @override
  Map<String, dynamic>? loadWeightTrackingData() {
    return _storage['weight_tracking_data'] as Map<String, dynamic>?;
  }

  @override
  Future<bool> savePlanPreferences(Map<String, dynamic> preferences) async {
    _storage['plan_preferences'] = preferences;
    return true;
  }

  @override
  Map<String, dynamic>? loadPlanPreferences() {
    return _storage['plan_preferences'] as Map<String, dynamic>?;
  }
}

class MockFirestoreService extends FirestoreService {
  final Map<String, dynamic> _firestoreData = {};

  @override
  String? get currentUserId => 'test_user_id';

  @override
  String generateWeightEntryId() {
    return 'test_entry_${DateTime.now().millisecondsSinceEpoch}';
  }

  @override
  Future<String> saveWeightEntry(Map<String, dynamic> weightEntry) async {
    final entryId = weightEntry['id'] as String;
    _firestoreData['weight_entries'] ??= <String, dynamic>{};
    _firestoreData['weight_entries'][entryId] = weightEntry;
    return entryId;
  }

  @override
  Future<void> updatePlanPreferencesField(String fieldName, dynamic value) async {
    _firestoreData['plan_preferences'] ??= <String, dynamic>{};
    _firestoreData['plan_preferences'][fieldName] = value;
  }

  @override
  Future<void> savePlanPreferences(Map<String, dynamic> planPreferences) async {
    _firestoreData['plan_preferences'] = planPreferences;
  }
}
