import 'package:flutter_test/flutter_test.dart';
import 'package:easydietai/shared/models/diet_type.dart';

void main() {
  group('DietType Tests', () {
    test('should have correct predefined diet types', () {
      expect(DietTypes.predefined.length, 6);
      
      final dietTypes = DietTypes.predefined.map((d) => d.type).toList();
      expect(dietTypes, contains(DietType.balanced));
      expect(dietTypes, contains(DietType.highProtein));
      expect(dietTypes, contains(DietType.ketogenic));
      expect(dietTypes, contains(DietType.lowCarb));
      expect(dietTypes, contains(DietType.mediterranean));
      expect(dietTypes, contains(DietType.plantBased));
    });

    test('should get diet type data by type', () {
      final ketoData = DietTypes.getByType(DietType.ketogenic);
      expect(ketoData, isNotNull);
      expect(ketoData!.type, DietType.ketogenic);
      expect(ketoData.nameAr, 'الكيتو');
      expect(ketoData.macroDistribution.carbsPercentage, 5.0);
      expect(ketoData.macroDistribution.proteinPercentage, 20.0);
      expect(ketoData.macroDistribution.fatPercentage, 75.0);
    });

    test('should get macro distribution for diet type', () {
      final balancedMacros = DietTypes.getMacroDistribution(DietType.balanced);
      expect(balancedMacros, isNotNull);
      expect(balancedMacros!.carbsPercentage, 50.0);
      expect(balancedMacros.proteinPercentage, 20.0);
      expect(balancedMacros.fatPercentage, 30.0);
    });

    test('should validate macro distribution correctly', () {
      // Valid distribution
      final validDistribution = MacroDistribution(
        carbsPercentage: 50.0,
        proteinPercentage: 20.0,
        fatPercentage: 30.0,
      );
      expect(DietTypes.isValidMacroDistribution(validDistribution), true);

      // Invalid distribution (doesn't add up to 100)
      final invalidDistribution = MacroDistribution(
        carbsPercentage: 40.0,
        proteinPercentage: 20.0,
        fatPercentage: 30.0,
      );
      expect(DietTypes.isValidMacroDistribution(invalidDistribution), false);

      // Edge case - exactly 100%
      final exactDistribution = MacroDistribution(
        carbsPercentage: 33.3,
        proteinPercentage: 33.3,
        fatPercentage: 33.4,
      );
      expect(DietTypes.isValidMacroDistribution(exactDistribution), true);
    });

    test('should provide health warnings for extreme distributions', () {
      // Very low carbs
      final lowCarbDistribution = MacroDistribution(
        carbsPercentage: 5.0,
        proteinPercentage: 20.0,
        fatPercentage: 75.0,
      );
      final lowCarbWarnings = DietTypes.getHealthWarnings(lowCarbDistribution);
      expect(lowCarbWarnings, isNotEmpty);
      expect(lowCarbWarnings.any((w) => w.contains('الكربوهيدرات منخفضة')), true);

      // Very high protein
      final highProteinDistribution = MacroDistribution(
        carbsPercentage: 25.0,
        proteinPercentage: 40.0,
        fatPercentage: 35.0,
      );
      final highProteinWarnings = DietTypes.getHealthWarnings(highProteinDistribution);
      expect(highProteinWarnings, isNotEmpty);
      expect(highProteinWarnings.any((w) => w.contains('البروتين عالية')), true);

      // Very high fat
      final highFatDistribution = MacroDistribution(
        carbsPercentage: 5.0,
        proteinPercentage: 20.0,
        fatPercentage: 75.0,
      );
      final highFatWarnings = DietTypes.getHealthWarnings(highFatDistribution);
      expect(highFatWarnings, isNotEmpty);
      expect(highFatWarnings.any((w) => w.contains('الدهون عالية')), true);

      // Very low fat
      final lowFatDistribution = MacroDistribution(
        carbsPercentage: 75.0,
        proteinPercentage: 15.0,
        fatPercentage: 10.0,
      );
      final lowFatWarnings = DietTypes.getHealthWarnings(lowFatDistribution);
      expect(lowFatWarnings, isNotEmpty);
      expect(lowFatWarnings.any((w) => w.contains('الدهون منخفضة')), true);

      // Balanced distribution should have no warnings
      final balancedDistribution = MacroDistribution(
        carbsPercentage: 50.0,
        proteinPercentage: 20.0,
        fatPercentage: 30.0,
      );
      final balancedWarnings = DietTypes.getHealthWarnings(balancedDistribution);
      expect(balancedWarnings, isEmpty);
    });

    test('should have correct macro distributions for all predefined diets', () {
      for (final dietData in DietTypes.predefined) {
        final distribution = dietData.macroDistribution;
        final total = distribution.carbsPercentage + 
                      distribution.proteinPercentage + 
                      distribution.fatPercentage;
        
        expect(total, closeTo(100.0, 0.1), 
               reason: '${dietData.nameEn} macro distribution should add up to 100%');
        
        // All percentages should be positive
        expect(distribution.carbsPercentage, greaterThan(0));
        expect(distribution.proteinPercentage, greaterThan(0));
        expect(distribution.fatPercentage, greaterThan(0));
      }
    });

    test('should have Arabic and English names for all diets', () {
      for (final dietData in DietTypes.predefined) {
        expect(dietData.nameAr, isNotEmpty, 
               reason: '${dietData.type} should have Arabic name');
        expect(dietData.nameEn, isNotEmpty, 
               reason: '${dietData.type} should have English name');
        expect(dietData.descriptionAr, isNotEmpty, 
               reason: '${dietData.type} should have Arabic description');
        expect(dietData.descriptionEn, isNotEmpty, 
               reason: '${dietData.type} should have English description');
        expect(dietData.iconName, isNotEmpty, 
               reason: '${dietData.type} should have icon name');
      }
    });

    test('should serialize and deserialize MacroDistribution correctly', () {
      final original = MacroDistribution(
        carbsPercentage: 45.0,
        proteinPercentage: 25.0,
        fatPercentage: 30.0,
      );

      final json = original.toJson();
      final deserialized = MacroDistribution.fromJson(json);

      expect(deserialized.carbsPercentage, original.carbsPercentage);
      expect(deserialized.proteinPercentage, original.proteinPercentage);
      expect(deserialized.fatPercentage, original.fatPercentage);
    });

    test('should serialize and deserialize DietTypeData correctly', () {
      final original = DietTypes.predefined.first;

      final json = original.toJson();
      final deserialized = DietTypeData.fromJson(json);

      expect(deserialized.type, original.type);
      expect(deserialized.nameAr, original.nameAr);
      expect(deserialized.nameEn, original.nameEn);
      expect(deserialized.macroDistribution.carbsPercentage, 
             original.macroDistribution.carbsPercentage);
    });
  });
}
