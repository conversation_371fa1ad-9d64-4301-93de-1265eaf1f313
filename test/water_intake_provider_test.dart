import 'package:flutter_test/flutter_test.dart';
import 'package:easydietai/features/water_tracking/data/models/water_intake_models.dart';
import 'package:easydietai/features/water_tracking/providers/water_intake_provider.dart';

void main() {
  group('Water Intake Provider Tests', () {
    test('WaterIntakeState should initialize with default values', () {
      const state = WaterIntakeState();
      
      expect(state.dailyIntakes, isEmpty);
      expect(state.settings, equals(const WaterIntakeSettings()));
      expect(state.isLoading, equals(false));
      expect(state.error, isNull);
    });

    test('WaterIntakeState should handle copyWith correctly', () {
      const initialState = WaterIntakeState();
      
      final updatedState = initialState.copyWith(
        isLoading: true,
        error: 'Test error',
      );
      
      expect(updatedState.isLoading, equals(true));
      expect(updatedState.error, equals('Test error'));
      expect(updatedState.dailyIntakes, isEmpty); // Should remain unchanged
      expect(updatedState.settings, equals(const WaterIntakeSettings())); // Should remain unchanged
    });

    test('WaterIntakeState should handle daily intakes updates', () {
      const initialState = WaterIntakeState();
      
      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
        entries: [
          WaterIntakeEntry(
            id: '1',
            amount: 250.0,
            timestamp: DateTime(2024, 1, 15, 8, 0),
          ),
        ],
        totalIntake: 250.0,
      );
      
      final updatedState = initialState.copyWith(
        dailyIntakes: {'2024-01-15': dailyIntake},
      );
      
      expect(updatedState.dailyIntakes.length, equals(1));
      expect(updatedState.dailyIntakes['2024-01-15'], equals(dailyIntake));
      expect(updatedState.dailyIntakes['2024-01-15']?.totalIntake, equals(250.0));
    });

    test('WaterIntakeSettings should have correct default values', () {
      const settings = WaterIntakeSettings();
      
      expect(settings.dailyGoal, equals(2500.0));
      expect(settings.quickAddAmounts, equals([250.0, 500.0, 750.0, 1000.0]));
      expect(settings.enableNotifications, equals(true));
      expect(settings.reminderIntervalMinutes, equals(60));
    });

    test('WaterIntakeSettings should serialize and deserialize correctly', () {
      const settings = WaterIntakeSettings(
        dailyGoal: 3000.0,
        quickAddAmounts: [200.0, 400.0, 600.0],
        enableNotifications: false,
        reminderIntervalMinutes: 90,
      );
      
      final json = settings.toJson();
      final deserializedSettings = WaterIntakeSettings.fromJson(json);
      
      expect(deserializedSettings.dailyGoal, equals(3000.0));
      expect(deserializedSettings.quickAddAmounts, equals([200.0, 400.0, 600.0]));
      expect(deserializedSettings.enableNotifications, equals(false));
      expect(deserializedSettings.reminderIntervalMinutes, equals(90));
    });

    test('Provider should handle error states gracefully', () {
      const initialState = WaterIntakeState();
      
      final errorState = initialState.copyWith(
        isLoading: false,
        error: 'Network error',
      );
      
      expect(errorState.isLoading, equals(false));
      expect(errorState.error, equals('Network error'));
      
      // Clear error
      final clearedState = errorState.copyWith(error: null);
      expect(clearedState.error, isNull);
      expect(clearedState.isLoading, equals(false)); // Should remain unchanged
    });

    test('Provider should handle loading states correctly', () {
      const initialState = WaterIntakeState();
      
      // Set loading
      final loadingState = initialState.copyWith(isLoading: true);
      expect(loadingState.isLoading, equals(true));
      expect(loadingState.error, isNull);
      
      // Complete loading with data
      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
      );
      
      final completedState = loadingState.copyWith(
        isLoading: false,
        dailyIntakes: {'2024-01-15': dailyIntake},
      );
      
      expect(completedState.isLoading, equals(false));
      expect(completedState.dailyIntakes.length, equals(1));
      expect(completedState.error, isNull);
    });
  });
}
