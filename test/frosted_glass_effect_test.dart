import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/features/meal_planning/presentation/widgets/home_app_bar.dart';
import 'package:easydietai/features/meal_planning/presentation/widgets/day_navigation_header.dart';
import 'package:easydietai/features/meal_planning/presentation/widgets/meal_plan_card_view.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';

void main() {
  group('Frosted Glass Effect Tests', () {
    late List<DayMealPlan> testDays;

    setUp(() {
      // Create test data
      const testNutrition = NutritionInfo(
        calories: 2000,
        protein: 100.0,
        carbs: 250.0,
        fat: 70.0,
        fiber: 25.0,
      );

      testDays = [
        DayMealPlan(
          date: DateTime.now().subtract(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now(),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now().add(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
      ];
    });

    testWidgets('SliverAppBar should have BackdropFilter with blur effect', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify SliverAppBar is present
      expect(find.byType(SliverAppBar), findsOneWidget);

      // Verify BackdropFilter is present in the SliverAppBar
      expect(find.byType(BackdropFilter), findsWidgets);

      // Find the BackdropFilter widget and verify its properties
      final backdropFilters = tester.widgetList<BackdropFilter>(find.byType(BackdropFilter));
      
      // Should have at least one BackdropFilter (for SliverAppBar)
      expect(backdropFilters.length, greaterThanOrEqualTo(1));

      // Verify the blur filter properties
      final appBarBackdropFilter = backdropFilters.first;
      expect(appBarBackdropFilter.filter, isA<ImageFilter>());
    });

    testWidgets('Sticky day navigation header should have BackdropFilter with blur effect', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify SliverPersistentHeader is present (for sticky day navigation)
      expect(find.byType(SliverPersistentHeader), findsNWidgets(2)); // One for SliverAppBar, one for sticky header

      // Verify BackdropFilter is present
      expect(find.byType(BackdropFilter), findsNWidgets(2)); // One for SliverAppBar, one for sticky header

      // Verify ClipRect is present (wrapping the BackdropFilter)
      expect(find.byType(ClipRect), findsWidgets);
    });

    testWidgets('Both elements should have semi-transparent backgrounds', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Find containers with semi-transparent backgrounds
      final containers = tester.widgetList<Container>(find.byType(Container));
      
      // Should have containers with semi-transparent backgrounds
      expect(containers.length, greaterThan(0));

      // Check for containers with BoxDecoration that have semi-transparent colors
      bool foundSemiTransparentContainer = false;
      for (final container in containers) {
        if (container.decoration is BoxDecoration) {
          final decoration = container.decoration as BoxDecoration;
          if (decoration.color != null && decoration.color!.opacity < 1.0 && decoration.color!.opacity > 0.0) {
            foundSemiTransparentContainer = true;
            break;
          }
        }
      }
      
      expect(foundSemiTransparentContainer, true);
    });

    testWidgets('SliverAppBar should have transparent background color', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Find the SliverAppBar widget
      final sliverAppBar = tester.widget<SliverAppBar>(find.byType(SliverAppBar));
      
      // Verify the background color is transparent
      expect(sliverAppBar.backgroundColor, Colors.transparent);
      
      // Verify elevation is 0 (since we're using backdrop filter)
      expect(sliverAppBar.elevation, 0);
    });

    testWidgets('Both elements should maintain text readability', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify app bar title is visible
      expect(find.text('خطة الوجبات'), findsOneWidget);
      
      // Verify day navigation elements are visible
      expect(find.text('اليوم'), findsWidgets); // Today text appears in multiple places
      
      // Verify navigation icons are present
      expect(find.byIcon(Icons.arrow_back_ios), findsWidgets);
      expect(find.byIcon(Icons.arrow_forward_ios), findsWidgets);
    });

    testWidgets('Backdrop filters should work during scroll animations', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify initial state
      expect(find.byType(BackdropFilter), findsNWidgets(2));

      // Simulate scroll down
      await tester.drag(find.byType(CustomScrollView), const Offset(0, -300));
      await tester.pumpAndSettle();

      // BackdropFilter should still be present (sticky header remains)
      expect(find.byType(BackdropFilter), findsAtLeastNWidgets(1));

      // Simulate scroll up
      await tester.drag(find.byType(CustomScrollView), const Offset(0, 100));
      await tester.pumpAndSettle();

      // Both BackdropFilters should be present again
      expect(find.byType(BackdropFilter), findsNWidgets(2));
    });
  });
}
