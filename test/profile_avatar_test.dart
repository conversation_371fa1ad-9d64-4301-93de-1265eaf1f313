import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:easydietai/features/meal_planning/presentation/widgets/home_app_bar.dart';
import 'package:easydietai/features/meal_planning/presentation/widgets/meal_plan_card_view.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';
import 'package:easydietai/shared/models/user_profile.dart';
import 'package:easydietai/shared/providers/app_state_provider.dart';

void main() {
  group('Profile Avatar Tests', () {
    late List<DayMealPlan> testDays;

    setUp(() {
      // Create test data
      const testNutrition = NutritionInfo(
        calories: 2000,
        protein: 100.0,
        carbs: 250.0,
        fat: 70.0,
        fiber: 25.0,
      );

      testDays = [
        DayMealPlan(
          date: DateTime.now(),
          meals: [],
          totalNutrition: testNutrition,
        ),
      ];
    });

    testWidgets('Profile avatar should show person icon when no profile picture exists', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            // Override providers to return null user and profile
            currentUserProvider.overrideWith((ref) => null),
            currentUserProfileProvider.overrideWith((ref) => null),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify person icon is displayed
      expect(find.byIcon(Icons.person), findsOneWidget);
      
      // Verify CircleAvatar is present
      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    testWidgets('Profile avatar should show profile picture when available from user profile', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      final testUserProfile = UserProfile(
        id: 'test-user-id',
        email: '<EMAIL>',
        displayName: 'Test User',
        photoUrl: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentUserProvider.overrideWith((ref) => null),
            currentUserProfileProvider.overrideWith((ref) => testUserProfile),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify CircleAvatar is present
      expect(find.byType(CircleAvatar), findsOneWidget);
      
      // Verify the CircleAvatar has a backgroundImage
      final circleAvatar = tester.widget<CircleAvatar>(find.byType(CircleAvatar));
      expect(circleAvatar.backgroundImage, isA<NetworkImage>());
      
      // Verify no person icon is shown (since we have a profile picture)
      expect(circleAvatar.child, isNull);
    });

    testWidgets('Profile avatar should have proper spacing with left and right padding', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Find the Padding widget that wraps the profile avatar
      final paddingFinder = find.ancestor(
        of: find.byType(CircleAvatar),
        matching: find.byType(Padding),
      );
      
      expect(paddingFinder, findsOneWidget);
      
      final padding = tester.widget<Padding>(paddingFinder);
      expect(padding.padding, const EdgeInsets.only(left: 16, right: 16));
    });

    testWidgets('Profile avatar should maintain 18px radius', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify CircleAvatar has correct radius
      final circleAvatar = tester.widget<CircleAvatar>(find.byType(CircleAvatar));
      expect(circleAvatar.radius, 18);
    });

    testWidgets('Profile avatar should be tappable and navigate to profile', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Find the GestureDetector that wraps the CircleAvatar
      final gestureDetectorFinder = find.ancestor(
        of: find.byType(CircleAvatar),
        matching: find.byType(GestureDetector),
      );
      
      expect(gestureDetectorFinder, findsOneWidget);
      
      // Verify it's tappable
      await tester.tap(gestureDetectorFinder);
      await tester.pumpAndSettle();
      
      // Note: In a real test, you would verify navigation occurred
      // For this test, we just verify the GestureDetector exists and is tappable
    });

    testWidgets('Profile avatar should handle image loading errors gracefully', (WidgetTester tester) async {
      final testPlan = MealPlanResponse(
        success: true,
        mealPlan: GeneratedMealPlan(days: testDays),
        message: 'Test plan',
      );

      final testUserProfile = UserProfile(
        id: 'test-user-id',
        email: '<EMAIL>',
        displayName: 'Test User',
        photoUrl: 'https://invalid-url.com/nonexistent.jpg',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentUserProvider.overrideWith((ref) => null),
            currentUserProfileProvider.overrideWith((ref) => testUserProfile),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: MealPlanCardView(
                plan: testPlan,
                onMealTap: (meal, day) {},
                includeAppBar: true,
              ),
            ),
          ),
        ),
      );

      // Verify CircleAvatar is present
      expect(find.byType(CircleAvatar), findsOneWidget);
      
      // Verify the CircleAvatar has error handling
      final circleAvatar = tester.widget<CircleAvatar>(find.byType(CircleAvatar));
      expect(circleAvatar.onBackgroundImageError, isNotNull);
    });

    testWidgets('Profile avatar should work with regular AppBar', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              appBar: const HomeAppBar(),
              body: Container(),
            ),
          ),
        ),
      );

      // Verify CircleAvatar is present in regular AppBar
      expect(find.byType(CircleAvatar), findsOneWidget);
    });
  });
}
