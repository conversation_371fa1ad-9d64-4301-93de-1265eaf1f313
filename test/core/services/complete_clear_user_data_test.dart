import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:easydietai/core/services/local_storage_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Complete Clear User Data Tests', () {
    late LocalStorageService localStorageService;
    late SharedPreferences prefs;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
      
      // Initialize LocalStorageService
      localStorageService = LocalStorageService(prefs);
    });

    tearDown(() {
      // No cleanup needed
    });

    test('should clear ALL keys from SharedPreferences except has_seen_intro_wizard', () async {
      // Set up various data including Flutter-prefixed keys and custom keys
      await localStorageService.saveBool('has_seen_intro_wizard', true);
      await localStorageService.setOnboardingCompleted(true);
      await localStorageService.saveUserProfile({'name': 'Test User'});
      await localStorageService.savePlanPreferences({'diet': 'balanced'});
      await localStorageService.saveMealGenerationLoading(true);
      
      // Add some Flutter-prefixed keys directly to SharedPreferences
      await prefs.setBool('flutter.onboarding_completed', true);
      await prefs.setBool('flutter.is_guest_user', true);
      await prefs.setString('flutter.language', 'en');
      await prefs.setString('flutter.theme', 'dark');
      
      // Add some custom keys
      await prefs.setString('custom_key_1', 'value1');
      await prefs.setInt('custom_key_2', 42);
      await prefs.setStringList('custom_key_3', ['item1', 'item2']);
      
      // Verify initial state - all keys should exist
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isTrue);
      expect(localStorageService.isOnboardingCompleted(), isTrue);
      expect(localStorageService.loadUserProfile(), isNotNull);
      expect(localStorageService.loadPlanPreferences(), isNotNull);
      expect(localStorageService.loadMealGenerationLoading(), isNotNull);
      expect(prefs.getBool('flutter.onboarding_completed'), isTrue);
      expect(prefs.getBool('flutter.is_guest_user'), isTrue);
      expect(prefs.getString('flutter.language'), equals('en'));
      expect(prefs.getString('flutter.theme'), equals('dark'));
      expect(prefs.getString('custom_key_1'), equals('value1'));
      expect(prefs.getInt('custom_key_2'), equals(42));
      expect(prefs.getStringList('custom_key_3'), equals(['item1', 'item2']));
      
      // Get all keys before clearing
      final allKeysBefore = prefs.getKeys();
      expect(allKeysBefore.length, greaterThan(10)); // Should have many keys
      
      // Clear user data
      final success = await localStorageService.clearUserData();
      expect(success, isTrue);
      
      // Verify has_seen_intro_wizard is preserved
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isTrue);
      
      // Verify ALL other keys are cleared
      expect(localStorageService.isOnboardingCompleted(), isFalse); // Default value
      expect(localStorageService.loadUserProfile(), isNull);
      expect(localStorageService.loadPlanPreferences(), isNull);
      expect(localStorageService.loadMealGenerationLoading(), isNull);
      expect(prefs.getBool('flutter.onboarding_completed'), isNull);
      expect(prefs.getBool('flutter.is_guest_user'), isNull);
      expect(prefs.getString('flutter.language'), isNull);
      expect(prefs.getString('flutter.theme'), isNull);
      expect(prefs.getString('custom_key_1'), isNull);
      expect(prefs.getInt('custom_key_2'), isNull);
      expect(prefs.getStringList('custom_key_3'), isNull);
      
      // Verify only has_seen_intro_wizard key remains
      final allKeysAfter = prefs.getKeys();
      expect(allKeysAfter.length, equals(1));
      expect(allKeysAfter.contains('has_seen_intro_wizard'), isTrue);
    });

    test('should clear ALL keys when has_seen_intro_wizard is false', () async {
      // Set up data with has_seen_intro_wizard as false
      await localStorageService.saveBool('has_seen_intro_wizard', false);
      await localStorageService.setOnboardingCompleted(true);
      await localStorageService.saveUserProfile({'name': 'Test User'});
      await prefs.setBool('flutter.is_guest_user', true);
      await prefs.setString('custom_key', 'value');
      
      // Verify initial state
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isFalse);
      expect(localStorageService.isOnboardingCompleted(), isTrue);
      expect(prefs.getBool('flutter.is_guest_user'), isTrue);
      expect(prefs.getString('custom_key'), equals('value'));
      
      final allKeysBefore = prefs.getKeys();
      expect(allKeysBefore.length, greaterThan(1));
      
      // Clear user data
      await localStorageService.clearUserData();
      
      // Verify has_seen_intro_wizard is preserved as false
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isFalse);
      
      // Verify all other keys are cleared
      expect(localStorageService.isOnboardingCompleted(), isFalse);
      expect(prefs.getBool('flutter.is_guest_user'), isNull);
      expect(prefs.getString('custom_key'), isNull);
      
      // Verify only has_seen_intro_wizard key remains
      final allKeysAfter = prefs.getKeys();
      expect(allKeysAfter.length, equals(1));
      expect(allKeysAfter.contains('has_seen_intro_wizard'), isTrue);
    });

    test('should clear ALL keys when has_seen_intro_wizard is not set', () async {
      // Set up data without has_seen_intro_wizard
      await localStorageService.setOnboardingCompleted(true);
      await localStorageService.saveUserProfile({'name': 'Test User'});
      await prefs.setBool('flutter.is_guest_user', true);
      await prefs.setString('flutter.language', 'ar');
      await prefs.setString('custom_key', 'value');
      
      // Verify has_seen_intro_wizard is not set
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isNull);
      expect(localStorageService.isOnboardingCompleted(), isTrue);
      expect(prefs.getBool('flutter.is_guest_user'), isTrue);
      expect(prefs.getString('flutter.language'), equals('ar'));
      expect(prefs.getString('custom_key'), equals('value'));
      
      final allKeysBefore = prefs.getKeys();
      expect(allKeysBefore.length, greaterThan(1));
      
      // Clear user data
      await localStorageService.clearUserData();
      
      // Verify has_seen_intro_wizard remains unset
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isNull);
      
      // Verify ALL keys are cleared
      expect(localStorageService.isOnboardingCompleted(), isFalse);
      expect(prefs.getBool('flutter.is_guest_user'), isNull);
      expect(prefs.getString('flutter.language'), isNull);
      expect(prefs.getString('custom_key'), isNull);
      
      // Verify no keys remain
      final allKeysAfter = prefs.getKeys();
      expect(allKeysAfter.length, equals(0));
    });

    test('should handle edge case with many Flutter-prefixed keys', () async {
      // Set up has_seen_intro_wizard
      await localStorageService.saveBool('has_seen_intro_wizard', true);
      
      // Add many Flutter-prefixed keys that might remain after selective clearing
      await prefs.setBool('flutter.onboarding_completed', true);
      await prefs.setBool('flutter.is_guest_user', false);
      await prefs.setString('flutter.language', 'en');
      await prefs.setString('flutter.theme', 'light');
      await prefs.setString('flutter.user_id', 'user123');
      await prefs.setString('flutter.auth_token', 'token123');
      await prefs.setStringList('flutter.recent_searches', ['search1', 'search2']);
      await prefs.setDouble('flutter.app_version', 1.0);
      await prefs.setInt('flutter.launch_count', 5);
      
      // Verify many keys exist
      final allKeysBefore = prefs.getKeys();
      expect(allKeysBefore.length, greaterThan(9));
      
      // Clear user data
      await localStorageService.clearUserData();
      
      // Verify only has_seen_intro_wizard remains
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isTrue);
      
      // Verify ALL Flutter-prefixed keys are cleared
      expect(prefs.getBool('flutter.onboarding_completed'), isNull);
      expect(prefs.getBool('flutter.is_guest_user'), isNull);
      expect(prefs.getString('flutter.language'), isNull);
      expect(prefs.getString('flutter.theme'), isNull);
      expect(prefs.getString('flutter.user_id'), isNull);
      expect(prefs.getString('flutter.auth_token'), isNull);
      expect(prefs.getStringList('flutter.recent_searches'), isNull);
      expect(prefs.getDouble('flutter.app_version'), isNull);
      expect(prefs.getInt('flutter.launch_count'), isNull);
      
      // Verify only one key remains
      final allKeysAfter = prefs.getKeys();
      expect(allKeysAfter.length, equals(1));
      expect(allKeysAfter.contains('has_seen_intro_wizard'), isTrue);
    });

    test('should work correctly when SharedPreferences is already empty', () async {
      // Ensure SharedPreferences is empty
      await prefs.clear();
      expect(prefs.getKeys().length, equals(0));
      
      // Clear user data on empty storage
      final success = await localStorageService.clearUserData();
      expect(success, isTrue);
      
      // Verify storage remains empty
      expect(prefs.getKeys().length, equals(0));
      expect(localStorageService.loadBool('has_seen_intro_wizard'), isNull);
    });
  });
}
