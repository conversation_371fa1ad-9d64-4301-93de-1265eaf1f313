import 'package:flutter_test/flutter_test.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';

void main() {
  group('Meal Plan Sync Tests', () {
    test('TimestampOrStringConverter should handle Firestore Timestamp', () {
      final converter = TimestampOrStringConverter();
      final timestamp = Timestamp.fromDate(DateTime(2024, 1, 15));

      final result = converter.fromJson(timestamp);
      expect(result, equals(DateTime(2024, 1, 15)));
    });

    test('TimestampOrStringConverter should handle ISO string', () {
      final converter = TimestampOrStringConverter();
      final isoString = '2024-01-15T00:00:00.000Z';

      final result = converter.fromJson(isoString);
      expect(result, equals(DateTime.parse(isoString)));
    });

    test('DayMealPlan should handle Firestore Timestamp correctly', () {
      // Create a simple meal using the compatibility method
      final mealData = {
        'name': 'Test Breakfast',
        'type': 'breakfast',
        'ingredients': [
          {'name': 'Eggs'}
        ],
        'nutrition': {
          'calories': 300,
          'protein': 20.0,
          'carbs': 10.0,
          'fat': 15.0,
          'fiber': 2.0,
        },
      };

      // Test that GeneratedMeal can be created with compatibility method
      final meal = GeneratedMeal.fromJsonWithCompatibility(mealData);
      expect(meal.name, equals('Test Breakfast'));

      // Now test DayMealPlan with the meal
      final firestoreData = {
        'date': Timestamp.fromDate(DateTime(2024, 1, 15)),
        'meals': [mealData],
        'total_nutrition': {
          'calories': 300,
          'protein': 20.0,
          'carbs': 10.0,
          'fat': 15.0,
          'fiber': 2.0,
        },
        'day_document_id': 'test_doc_id',
      };

      // Test that DayMealPlan can be created from Firestore data
      expect(() => DayMealPlan.fromJson(firestoreData), returnsNormally);

      final dayMealPlan = DayMealPlan.fromJson(firestoreData);
      expect(dayMealPlan.date, equals(DateTime(2024, 1, 15)));
      expect(dayMealPlan.meals.length, equals(1));
      expect(dayMealPlan.meals.first.name, equals('Test Breakfast'));
      expect(dayMealPlan.dayDocumentId, equals('test_doc_id'));
    });

    test('DayMealPlan should handle ISO string dates for backward compatibility', () {
      // Create a simple meal using the compatibility method
      final mealData = {
        'name': 'Test Lunch',
        'type': 'lunch',
        'ingredients': [
          {'name': 'Chicken'}
        ],
        'nutrition': {
          'calories': 400,
          'protein': 30.0,
          'carbs': 20.0,
          'fat': 20.0,
          'fiber': 3.0,
        },
      };

      // Simulate local storage data with ISO string
      final localStorageData = {
        'date': '2024-01-15T00:00:00.000Z',
        'meals': [mealData],
        'total_nutrition': {
          'calories': 400,
          'protein': 30.0,
          'carbs': 20.0,
          'fat': 20.0,
          'fiber': 3.0,
        },
      };

      // Test that DayMealPlan can be created from local storage data
      expect(() => DayMealPlan.fromJson(localStorageData), returnsNormally);

      final dayMealPlan = DayMealPlan.fromJson(localStorageData);
      expect(dayMealPlan.date, equals(DateTime.parse('2024-01-15T00:00:00.000Z')));
      expect(dayMealPlan.meals.length, equals(1));
      expect(dayMealPlan.meals.first.name, equals('Test Lunch'));
    });

    test('GeneratedMeal should handle Firestore Timestamp for consumedAt', () {
      final consumedTime = DateTime(2024, 1, 15, 12, 30);
      final mealData = {
        'name': 'Test Meal',
        'type': 'breakfast',
        'ingredients': [
          {'name': 'Toast'}
        ],
        'nutrition': {
          'calories': 200,
          'protein': 8.0,
          'carbs': 30.0,
          'fat': 5.0,
          'fiber': 2.0,
        },
        'consumed_at': Timestamp.fromDate(consumedTime),
        'is_consumed': true,
        'preparation_time': 10,
        'difficulty': 'easy',
        'replacement_history': [],
        'replacement_count': 0,
      };

      expect(() => GeneratedMeal.fromJson(mealData), returnsNormally);

      final meal = GeneratedMeal.fromJson(mealData);
      expect(meal.consumedAt, equals(consumedTime));
      expect(meal.isConsumed, isTrue);
    });

    test('GeneratedMealPlan should serialize and deserialize correctly', () {
      final originalPlan = GeneratedMealPlan(
        days: [
          DayMealPlan(
            date: DateTime(2024, 1, 15),
            meals: [
              GeneratedMeal(
                name: 'Test Breakfast',
                type: 'breakfast',
                ingredients: [MealIngredient(name: 'Eggs')],
                nutrition: NutritionInfo(
                  calories: 300,
                  protein: 20.0,
                  carbs: 10.0,
                  fat: 15.0,
                  fiber: 2.0,
                ),
                consumedAt: DateTime(2024, 1, 15, 8, 0),
                isConsumed: true,
              ),
            ],
            totalNutrition: NutritionInfo(
              calories: 300,
              protein: 20.0,
              carbs: 10.0,
              fat: 15.0,
              fiber: 2.0,
            ),
            dayDocumentId: 'test_doc_id',
          ),
        ],
      );

      // Test serialization
      final json = originalPlan.toJson();
      expect(json['days'], isA<List>());
      expect((json['days'] as List).length, equals(1));

      // Test deserialization
      final deserializedPlan = GeneratedMealPlan.fromJson(json);
      expect(deserializedPlan.days.length, equals(1));
      expect(deserializedPlan.days.first.date, equals(DateTime(2024, 1, 15)));
      expect(deserializedPlan.days.first.meals.length, equals(1));
      expect(deserializedPlan.days.first.meals.first.name, equals('Test Breakfast'));
      expect(deserializedPlan.days.first.meals.first.isConsumed, isTrue);
      expect(deserializedPlan.days.first.dayDocumentId, equals('test_doc_id'));
    });

    test('Timestamp conversion helper should handle nested data correctly', () {
      // Simulate the _convertTimestampsToStrings method behavior
      Map<String, dynamic> convertTimestampsToStrings(Map<String, dynamic> data) {
        final result = <String, dynamic>{};

        for (final entry in data.entries) {
          final key = entry.key;
          final value = entry.value;

          if (value is Timestamp) {
            result[key] = value.toDate().toIso8601String();
          } else if (value is Map<String, dynamic>) {
            result[key] = convertTimestampsToStrings(value);
          } else if (value is List) {
            result[key] = value.map((item) {
              if (item is Map<String, dynamic>) {
                return convertTimestampsToStrings(item);
              } else if (item is Timestamp) {
                return item.toDate().toIso8601String();
              }
              return item;
            }).toList();
          } else {
            result[key] = value;
          }
        }

        return result;
      }

      final testData = {
        'updatedAt': Timestamp.fromDate(DateTime(2024, 1, 15, 10, 30)),
        'preferences': {
          'createdAt': Timestamp.fromDate(DateTime(2024, 1, 10)),
          'name': 'Test User',
          'settings': {
            'lastModified': Timestamp.fromDate(DateTime(2024, 1, 12)),
            'value': 'test'
          }
        },
        'notifications': [
          {
            'sentAt': Timestamp.fromDate(DateTime(2024, 1, 14)),
            'message': 'Test notification'
          }
        ],
        'regularField': 'test value'
      };

      final converted = convertTimestampsToStrings(testData);

      // Check that Timestamps are converted to ISO strings
      expect(converted['updatedAt'], isA<String>());
      expect(converted['updatedAt'], equals('2024-01-15T10:30:00.000'));

      // Check nested object conversion
      final preferences = converted['preferences'] as Map<String, dynamic>;
      expect(preferences['createdAt'], isA<String>());
      expect(preferences['createdAt'], equals('2024-01-10T00:00:00.000'));
      expect(preferences['name'], equals('Test User'));

      // Check deeply nested conversion
      final settings = preferences['settings'] as Map<String, dynamic>;
      expect(settings['lastModified'], isA<String>());
      expect(settings['lastModified'], equals('2024-01-12T00:00:00.000'));
      expect(settings['value'], equals('test'));

      // Check array conversion
      final notifications = converted['notifications'] as List;
      expect(notifications.length, equals(1));
      final notification = notifications[0] as Map<String, dynamic>;
      expect(notification['sentAt'], isA<String>());
      expect(notification['sentAt'], equals('2024-01-14T00:00:00.000'));
      expect(notification['message'], equals('Test notification'));

      // Check regular fields are preserved
      expect(converted['regularField'], equals('test value'));
    });
  });
}
