import 'package:flutter_test/flutter_test.dart';
import 'package:easydietai/features/water_tracking/data/models/water_intake_models.dart';

void main() {
  group('Water Intake Sync Tests', () {
    test('DailyWaterIntake should serialize and deserialize correctly', () {
      // Create a sample daily water intake
      final entry1 = WaterIntakeEntry(
        id: '1',
        amount: 250.0,
        timestamp: DateTime(2024, 1, 15, 8, 0),
        note: 'Morning water',
      );

      final entry2 = WaterIntakeEntry(
        id: '2',
        amount: 500.0,
        timestamp: DateTime(2024, 1, 15, 12, 0),
      );

      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
        entries: [entry1, entry2],
        totalIntake: 750.0,
      );

      // Serialize to JSON
      final json = dailyIntake.toJson();

      // Verify JSON structure
      expect(json['date'], equals('2024-01-15'));
      expect(json['daily_goal'], equals(2500.0));
      expect(json['total_intake'], equals(750.0));
      expect(json['entries'], isA<List>());
      expect(json['entries'].length, equals(2));

      // Deserialize from JSON
      final deserializedIntake = DailyWaterIntake.fromJson(json);

      // Verify deserialized object
      expect(deserializedIntake.date, equals('2024-01-15'));
      expect(deserializedIntake.dailyGoal, equals(2500.0));
      expect(deserializedIntake.totalIntake, equals(750.0));
      expect(deserializedIntake.entries.length, equals(2));
      expect(deserializedIntake.entries[0].id, equals('1'));
      expect(deserializedIntake.entries[0].amount, equals(250.0));
      expect(deserializedIntake.entries[1].id, equals('2'));
      expect(deserializedIntake.entries[1].amount, equals(500.0));
    });

    test('WaterIntakeEntry should handle optional note correctly', () {
      // Entry without note
      final entryWithoutNote = WaterIntakeEntry(
        id: '1',
        amount: 250.0,
        timestamp: DateTime(2024, 1, 15, 8, 0),
      );

      final jsonWithoutNote = entryWithoutNote.toJson();
      expect(jsonWithoutNote['note'], isNull);

      final deserializedWithoutNote = WaterIntakeEntry.fromJson(jsonWithoutNote);
      expect(deserializedWithoutNote.note, isNull);

      // Entry with note
      final entryWithNote = WaterIntakeEntry(
        id: '2',
        amount: 500.0,
        timestamp: DateTime(2024, 1, 15, 12, 0),
        note: 'Lunch water',
      );

      final jsonWithNote = entryWithNote.toJson();
      expect(jsonWithNote['note'], equals('Lunch water'));

      final deserializedWithNote = WaterIntakeEntry.fromJson(jsonWithNote);
      expect(deserializedWithNote.note, equals('Lunch water'));
    });

    test('WaterIntakeSettings should serialize and deserialize correctly', () {
      final settings = WaterIntakeSettings(
        dailyGoal: 3000.0,
        quickAddAmounts: [200.0, 400.0, 600.0, 800.0],
        enableNotifications: false,
        reminderIntervalMinutes: 90,
      );

      final json = settings.toJson();
      expect(json['daily_goal'], equals(3000.0));
      expect(json['quick_add_amounts'], equals([200.0, 400.0, 600.0, 800.0]));
      expect(json['enable_notifications'], equals(false));
      expect(json['reminder_interval_minutes'], equals(90));

      final deserializedSettings = WaterIntakeSettings.fromJson(json);
      expect(deserializedSettings.dailyGoal, equals(3000.0));
      expect(deserializedSettings.quickAddAmounts, equals([200.0, 400.0, 600.0, 800.0]));
      expect(deserializedSettings.enableNotifications, equals(false));
      expect(deserializedSettings.reminderIntervalMinutes, equals(90));
    });

    test('DailyWaterIntake extension methods should work correctly', () {
      final entry1 = WaterIntakeEntry(
        id: '1',
        amount: 300.0,
        timestamp: DateTime.now(),
      );

      final entry2 = WaterIntakeEntry(
        id: '2',
        amount: 450.0,
        timestamp: DateTime.now(),
      );

      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2000.0,
        entries: [entry1, entry2],
        totalIntake: 750.0, // This should be recalculated by extension
      );

      // Test calculated total intake
      expect(dailyIntake.calculatedTotalIntake, equals(750.0));

      // Test progress percentage
      expect(dailyIntake.progressPercentage, equals(0.375)); // 750/2000 = 0.375

      // Test goal achievement
      expect(dailyIntake.isGoalAchieved, equals(false));

      // Test remaining amount
      expect(dailyIntake.remainingAmount, equals(1250.0)); // 2000 - 750 = 1250

      // Test with goal achieved
      final achievedIntake = dailyIntake.copyWith(
        entries: [
          entry1,
          entry2,
          WaterIntakeEntry(
            id: '3',
            amount: 1250.0,
            timestamp: DateTime.now(),
          ),
        ],
      );

      expect(achievedIntake.calculatedTotalIntake, equals(2000.0));
      expect(achievedIntake.progressPercentage, equals(1.0));
      expect(achievedIntake.isGoalAchieved, equals(true));
      expect(achievedIntake.remainingAmount, equals(0.0));
    });

    test('DailyWaterIntake should handle empty entries correctly', () {
      final emptyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
        entries: [],
        totalIntake: 0.0,
      );

      expect(emptyIntake.calculatedTotalIntake, equals(0.0));
      expect(emptyIntake.progressPercentage, equals(0.0));
      expect(emptyIntake.isGoalAchieved, equals(false));
      expect(emptyIntake.remainingAmount, equals(2500.0));

      final json = emptyIntake.toJson();
      final deserializedEmptyIntake = DailyWaterIntake.fromJson(json);

      expect(deserializedEmptyIntake.entries.isEmpty, equals(true));
      expect(deserializedEmptyIntake.totalIntake, equals(0.0));
    });

    test('Water intake data should prioritize local storage loading', () {
      // This test verifies that the data structure supports local-first loading
      final sampleData = {
        'date': '2024-01-15',
        'daily_goal': 2500.0,
        'entries': [
          {
            'id': '1',
            'amount': 250.0,
            'timestamp': DateTime(2024, 1, 15, 8, 0).toIso8601String(),
            'note': 'Morning water',
          }
        ],
        'total_intake': 250.0,
      };

      // Verify that we can create a DailyWaterIntake from local storage JSON
      final dailyIntake = DailyWaterIntake.fromJson(sampleData);

      expect(dailyIntake.date, equals('2024-01-15'));
      expect(dailyIntake.dailyGoal, equals(2500.0));
      expect(dailyIntake.entries.length, equals(1));
      expect(dailyIntake.entries.first.amount, equals(250.0));
      expect(dailyIntake.totalIntake, equals(250.0));

      // Verify that we can serialize it back to JSON for local storage
      final serializedData = dailyIntake.toJson();
      expect(serializedData['date'], equals('2024-01-15'));
      expect(serializedData['daily_goal'], equals(2500.0));
      expect(serializedData['total_intake'], equals(250.0));
    });

    test('Water intake entry removal should update totals correctly', () {
      final entry1 = WaterIntakeEntry(
        id: '1',
        amount: 300.0,
        timestamp: DateTime(2024, 1, 15, 8, 0),
        note: 'Morning water',
      );

      final entry2 = WaterIntakeEntry(
        id: '2',
        amount: 500.0,
        timestamp: DateTime(2024, 1, 15, 12, 0),
        note: 'Lunch water',
      );

      final entry3 = WaterIntakeEntry(
        id: '3',
        amount: 250.0,
        timestamp: DateTime(2024, 1, 15, 18, 0),
        note: 'Evening water',
      );

      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
        entries: [entry1, entry2, entry3],
        totalIntake: 1050.0,
      );

      // Remove entry2 (500ml)
      final updatedEntries = dailyIntake.entries.where((entry) => entry.id != '2').toList();
      final updatedDailyIntake = dailyIntake.copyWith(
        entries: updatedEntries,
        totalIntake: updatedEntries.fold(0.0, (sum, e) => sum + e.amount),
      );

      // Verify the entry was removed and totals updated
      expect(updatedDailyIntake.entries.length, equals(2));
      expect(updatedDailyIntake.totalIntake, equals(550.0)); // 300 + 250
      expect(updatedDailyIntake.calculatedTotalIntake, equals(550.0));
      expect(updatedDailyIntake.progressPercentage, equals(0.22)); // 550/2500 = 0.22

      // Verify the correct entries remain
      final remainingIds = updatedDailyIntake.entries.map((e) => e.id).toList();
      expect(remainingIds, containsAll(['1', '3']));
      expect(remainingIds, isNot(contains('2')));
    });

    test('Water intake entry removal should handle non-existent entries gracefully', () {
      final entry1 = WaterIntakeEntry(
        id: '1',
        amount: 300.0,
        timestamp: DateTime(2024, 1, 15, 8, 0),
      );

      final dailyIntake = DailyWaterIntake(
        date: '2024-01-15',
        dailyGoal: 2500.0,
        entries: [entry1],
        totalIntake: 300.0,
      );

      // Try to remove non-existent entry
      final updatedEntries = dailyIntake.entries.where((entry) => entry.id != 'non-existent').toList();
      final updatedDailyIntake = dailyIntake.copyWith(
        entries: updatedEntries,
        totalIntake: updatedEntries.fold(0.0, (sum, e) => sum + e.amount),
      );

      // Verify nothing changed
      expect(updatedDailyIntake.entries.length, equals(1));
      expect(updatedDailyIntake.totalIntake, equals(300.0));
      expect(updatedDailyIntake.entries.first.id, equals('1'));
    });
  });
}
