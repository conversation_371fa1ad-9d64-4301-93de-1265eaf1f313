import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easydietai/features/onboarding/presentation/widgets/physical_info_step.dart';
import 'package:easydietai/features/onboarding/providers/onboarding_provider.dart';
import 'package:easydietai/shared/models/user_profile.dart';

void main() {
  group('Enhanced Physical Info Step Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('should display height and weight picker cards', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Verify that picker cards are displayed
      expect(find.text('الطول'), findsOneWidget);
      expect(find.text('الوزن'), findsOneWidget);
      expect(find.text('سم'), findsOneWidget);
      expect(find.text('كغ'), findsOneWidget);

      // Verify default values are shown
      expect(find.text('170'), findsOneWidget); // Default height
      expect(find.text('70'), findsOneWidget);  // Default weight
    });

    testWidgets('should show height picker when height card is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Find and tap the height picker card
      final heightCard = find.ancestor(
        of: find.text('الطول'),
        matching: find.byType(GestureDetector),
      );
      
      await tester.tap(heightCard);
      await tester.pumpAndSettle();

      // Verify height picker modal is shown
      expect(find.text('اختر الطول'), findsOneWidget);
      expect(find.text('إلغاء'), findsOneWidget);
      expect(find.text('تم'), findsOneWidget);
    });

    testWidgets('should show weight picker when weight card is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Find and tap the weight picker card
      final weightCard = find.ancestor(
        of: find.text('الوزن'),
        matching: find.byType(GestureDetector),
      );
      
      await tester.tap(weightCard);
      await tester.pumpAndSettle();

      // Verify weight picker modal is shown
      expect(find.text('اختر الوزن'), findsOneWidget);
      expect(find.text('إلغاء'), findsOneWidget);
      expect(find.text('تم'), findsOneWidget);
    });

    testWidgets('should display calories slider when manual calories is enabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Enable manual calories mode
      final switchWidget = find.byType(Switch);
      await tester.tap(switchWidget);
      await tester.pumpAndSettle();

      // Verify calories slider is displayed
      expect(find.text('السعرات المحروقة يومياً'), findsOneWidget);
      expect(find.text('سعرة حرارية'), findsOneWidget);
      expect(find.byType(Slider), findsOneWidget);
      expect(find.text('1200'), findsOneWidget);
      expect(find.text('4000'), findsOneWidget);
    });

    testWidgets('should update calories value when slider is moved', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Scaffold(
              body: PhysicalInfoStep(),
            ),
          ),
        ),
      );

      // Enable manual calories mode
      final switchWidget = find.byType(Switch);
      await tester.tap(switchWidget);
      await tester.pumpAndSettle();

      // Find and interact with the slider
      final slider = find.byType(Slider);
      expect(slider, findsOneWidget);

      // Move slider to a different position
      await tester.drag(slider, const Offset(100, 0));
      await tester.pumpAndSettle();

      // Verify that the calories value has changed from default
      // The exact value depends on the slider position, but it should be different from 2000
      final caloriesText = tester.widget<Text>(
        find.descendant(
          of: find.ancestor(
            of: find.text('السعرات المحروقة يومياً'),
            matching: find.byType(Container),
          ),
          matching: find.byType(Text),
        ).at(1), // Get the second Text widget which should be the calories value
      );
      
      expect(caloriesText.data, isNot('2000'));
    });

    test('should update onboarding state when values change', () {
      final notifier = container.read(onboardingNotifierProvider.notifier);
      
      // Update height
      notifier.updatePhysicalInfo(height: 180.0);
      var state = container.read(onboardingNotifierProvider);
      expect(state.data.height, equals(180.0));

      // Update weight
      notifier.updatePhysicalInfo(weight: 80.0);
      state = container.read(onboardingNotifierProvider);
      expect(state.data.weight, equals(80.0));

      // Update calories
      notifier.updatePhysicalInfo(dailyBurnedCalories: 2500);
      state = container.read(onboardingNotifierProvider);
      expect(state.data.dailyBurnedCalories, equals(2500));
    });
  });
}
