import 'package:flutter_test/flutter_test.dart';
import 'package:easydietai/core/config/app_config.dart';

void main() {
  group('Firebase Functions Configuration', () {
    test('should return correct local URL in debug mode', () {
      // In test environment, isDebug should be true
      final url = AppConfig.functionsBaseUrl;
      
      expect(url, contains('localhost:5001'));
      expect(url, contains('easydiet-app'));
      expect(url, contains('us-central1'));
      expect(url, endsWith('/api'));
      
      print('Debug Functions URL: $url');
    });

    test('should construct correct production URL', () {
      // This test verifies the production URL format
      const expectedProdUrl = 'https://us-central1-easydiet-app.cloudfunctions.net/api';
      
      // We can't easily test the production URL directly since isDebug is determined at runtime
      // But we can verify the expected format
      expect(expectedProdUrl, contains('us-central1-easydiet-app.cloudfunctions.net'));
      expect(expectedProdUrl, endsWith('/api'));
      
      print('Expected Production Functions URL: $expectedProdUrl');
    });
  });
}
