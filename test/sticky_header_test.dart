import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/features/meal_planning/presentation/widgets/day_navigation_header.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';

void main() {
  group('Sticky Day Navigation Header Tests', () {
    late List<DayMealPlan> testDays;

    setUp(() {
      // Create test data
      const testNutrition = NutritionInfo(
        calories: 2000,
        protein: 100.0,
        carbs: 250.0,
        fat: 70.0,
        fiber: 25.0,
      );

      testDays = [
        DayMealPlan(
          date: DateTime.now().subtract(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now(),
          meals: [],
          totalNutrition: testNutrition,
        ),
        DayMealPlan(
          date: DateTime.now().add(const Duration(days: 1)),
          meals: [],
          totalNutrition: testNutrition,
        ),
      ];
    });

    testWidgets('StickyDayNavigationHeaderDelegate should build correctly', (WidgetTester tester) async {
      int selectedDay = 1;
      bool dayChanged = false;
      bool extraDaysGenerated = false;

      final delegate = StickyDayNavigationHeaderDelegate(
        totalDays: testDays.length,
        selectedDayIndex: selectedDay,
        days: testDays,
        onDayChanged: (index) {
          selectedDay = index;
          dayChanged = true;
        },
        onGenerateExtraDays: () {
          extraDaysGenerated = true;
        },
      );

      // Test delegate properties
      expect(delegate.minExtent, 80.0);
      expect(delegate.maxExtent, 80.0);

      // Test shouldRebuild
      final newDelegate = StickyDayNavigationHeaderDelegate(
        totalDays: testDays.length + 1,
        selectedDayIndex: selectedDay,
        days: testDays,
        onDayChanged: (index) {},
        onGenerateExtraDays: () {},
      );

      expect(delegate.shouldRebuild(newDelegate), true);

      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CustomScrollView(
                slivers: [
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: delegate,
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      height: 1000,
                      color: Colors.blue,
                      child: const Center(
                        child: Text('Scrollable Content'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify the header is rendered
      expect(find.byType(DayNavigationHeader), findsOneWidget);
      expect(find.text('اليوم'), findsWidgets); // Today text appears in multiple places
      
      // Verify the header has the correct styling
      final container = tester.widget<Container>(
        find.ancestor(
          of: find.byType(DayNavigationHeader),
          matching: find.byType(Container),
        ).first,
      );
      
      expect(container.decoration, isA<BoxDecoration>());
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, Colors.white);
      expect(decoration.boxShadow, isNotNull);
      expect(decoration.boxShadow!.length, 1);
    });

    testWidgets('Header should remain sticky during scroll', (WidgetTester tester) async {
      final delegate = StickyDayNavigationHeaderDelegate(
        totalDays: testDays.length,
        selectedDayIndex: 1,
        days: testDays,
        onDayChanged: (index) {},
        onGenerateExtraDays: () {},
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CustomScrollView(
                slivers: [
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: delegate,
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      height: 2000,
                      color: Colors.blue,
                      child: const Center(
                        child: Text('Scrollable Content'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Verify header is visible initially
      expect(find.byType(DayNavigationHeader), findsOneWidget);

      // Scroll down
      await tester.drag(find.byType(CustomScrollView), const Offset(0, -500));
      await tester.pumpAndSettle();

      // Header should still be visible (sticky)
      expect(find.byType(DayNavigationHeader), findsOneWidget);
      expect(find.text('اليوم'), findsWidgets); // Today text appears in multiple places
    });

    testWidgets('Header navigation should work correctly', (WidgetTester tester) async {
      int selectedDay = 1;
      bool extraDaysGenerated = false;

      final delegate = StickyDayNavigationHeaderDelegate(
        totalDays: testDays.length,
        selectedDayIndex: selectedDay,
        days: testDays,
        onDayChanged: (index) {
          selectedDay = index;
        },
        onGenerateExtraDays: () {
          extraDaysGenerated = true;
        },
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: CustomScrollView(
                slivers: [
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: delegate,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Test navigation buttons exist
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);

      // Test day indicator dots
      expect(find.byType(GestureDetector), findsWidgets);
    });
  });
}
