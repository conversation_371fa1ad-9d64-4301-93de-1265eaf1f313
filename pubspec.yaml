name: easydietai
description: "AI-powered diet planning application with personalized meal recommendations"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.20.3

  # HTTP Client
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Firebase & Authentication
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.8
  firebase_analytics: ^11.4.6
  firebase_crashlytics: ^4.3.6
  google_sign_in: ^6.3.0

  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # UI & Navigation
  go_router: ^12.1.3
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0

  # Notifications
  flutter_local_notifications: ^17.2.2

  # In-App Purchase
  in_app_purchase: ^3.1.11

  # Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utilities
  freezed_annotation: ^2.4.1
  logger: ^2.0.2+1
  uuid: ^4.2.1
  image_picker: ^1.0.4
  permission_handler: ^11.1.0
  url_launcher: ^6.2.2

  # Icons & Fonts
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  fl_chart: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  freezed: ^2.4.6
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^3.0.0
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
