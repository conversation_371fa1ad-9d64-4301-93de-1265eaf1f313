const {authenticateUser} = require('../../middleware/auth');
const {nutritionAnalysisSchema} = require('../../utils/validators');
const {analyzeNutrition} = require('../../services/nutritionService');

/**
 * POST /analyze-nutrition
 * Analyzes nutrition content of food items using OpenAI
 * Requires authentication
 */
async function analyzeNutritionContent(req, res) {
  try {
    // Validate request
    const {error, value} = nutritionAnalysisSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {foodItems, portion} = value;

    // Analyze nutrition
    const nutritionData = await analyzeNutrition(foodItems, portion);

    res.json({
      success: true,
      nutrition: nutritionData,
      message: 'Nutrition analysis completed',
    });
  } catch (error) {
    console.error('Error analyzing nutrition:', error);
    res.status(500).json({
      error: 'Failed to analyze nutrition',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/analyze-nutrition',
  middleware: [authenticateUser],
  handler: analyzeNutritionContent,
};
