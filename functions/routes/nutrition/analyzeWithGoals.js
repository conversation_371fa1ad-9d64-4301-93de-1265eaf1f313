const {authenticateUser} = require('../../middleware/auth');
const {nutritionAnalysisSchema} = require('../../utils/validators');
const {
  analyzeNutrition,
  calculateNutritionGoals,
  validateNutritionGoals,
} = require('../../services/nutritionService');

/**
 * POST /nutrition/analyze-with-goals
 * Analyzes nutrition and compares against user goals
 * Requires authentication
 */
async function analyzeNutritionWithGoals(req, res) {
  try {
    // Validate nutrition analysis request
    const {error: nutritionError} =
      nutritionAnalysisSchema.validate(req.body);

    if (nutritionError) {
      return res.status(400).json({
        error: nutritionError.details[0].message,
      });
    }

    const {foodItems, portion, userPreferences} = req.body;

    // Analyze nutrition
    const nutritionData = await analyzeNutrition(foodItems, portion);

    // Calculate nutrition goals if user preferences provided
    let goalComparison = null;
    if (userPreferences) {
      const goals = calculateNutritionGoals(userPreferences);
      goalComparison = validateNutritionGoals(nutritionData, goals);
    }

    res.json({
      success: true,
      nutrition: nutritionData,
      goalComparison,
      message: 'Nutrition analysis with goals completed',
    });
  } catch (error) {
    console.error('Error analyzing nutrition with goals:', error);
    res.status(500).json({
      error: 'Failed to analyze nutrition with goals',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/nutrition/analyze-with-goals',
  middleware: [authenticateUser],
  handler: analyzeNutritionWithGoals,
};
