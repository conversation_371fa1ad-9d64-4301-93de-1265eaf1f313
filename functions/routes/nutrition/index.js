const express = require('express');

// Import endpoint configurations
const analyze = require('./analyze');
const analyzeWithGoals = require('./analyzeWithGoals');
const calculateGoals = require('./calculateGoals');

const router = express.Router();

// Register routes
const endpoints = [
  analyze,
  analyzeWithGoals,
  calculateGoals,
];

endpoints.forEach((endpoint) => {
  const {method, path, middleware = [], handler} = endpoint;
  router[method](path, ...middleware, handler);
});

module.exports = router;
