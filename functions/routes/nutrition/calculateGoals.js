const {authenticateUser} = require('../../middleware/auth');
const {calculateNutritionGoals} = require('../../services/nutritionService');

/**
 * POST /nutrition/calculate-goals
 * Calculates nutrition goals based on user preferences
 * Requires authentication
 */
async function calculateGoals(req, res) {
  try {
    const {userPreferences} = req.body;

    if (!userPreferences || !userPreferences.calorieGoal) {
      return res.status(400).json({
        error: 'User preferences with calorie goal are required',
      });
    }

    const goals = calculateNutritionGoals(userPreferences);

    res.json({
      success: true,
      goals,
      message: 'Nutrition goals calculated successfully',
    });
  } catch (error) {
    console.error('Error calculating nutrition goals:', error);
    res.status(500).json({
      error: 'Failed to calculate nutrition goals',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/nutrition/calculate-goals',
  middleware: [authenticateUser],
  handler: calculateGoals,
};
