const express = require('express');

// Import domain routers
const userRoutes = require('./user');
const mealPlanRoutes = require('./mealPlan');
const nutritionRoutes = require('./nutrition');
const shoppingListRoutes = require('./shoppingList');

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'easydietai-functions',
  });
});

// Register domain routes
router.use('/', userRoutes);
router.use('/', mealPlanRoutes);
router.use('/', nutritionRoutes);
router.use('/', shoppingListRoutes);

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
  });
});

module.exports = router;
