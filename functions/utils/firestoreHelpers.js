const {initializeFirebase} = require('../config/firebase');
const {Timestamp} = require('firebase-admin/firestore');

/**
 * Generate a Firestore auto-generated document ID
 * @param {string} collectionPath - Optional collection path for context
 * @return {string} Auto-generated Firestore document ID
 */
function generateFirestoreDocumentId(collectionPath = 'temp') {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();
    
    // Use Firestore's auto-generated ID mechanism
    const docRef = db.collection(collectionPath).doc();
    return docRef.id;
  } catch (error) {
    console.error('Error generating Firestore document ID:', error);
    // Fallback to timestamp-based ID if Firestore fails
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Generate a document ID for a user's subcollection
 * @param {string} userId - User ID
 * @param {string} subcollectionName - Subcollection name
 * @return {string} Auto-generated document ID
 */
function generateUserSubcollectionDocumentId(userId, subcollectionName) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();
    
    // Generate ID using the actual collection path
    const collectionRef = db
      .collection('users')
      .doc(userId)
      .collection(subcollectionName);
    
    return collectionRef.doc().id;
  } catch (error) {
    console.error('Error generating user subcollection document ID:', error);
    // Fallback to generic document ID
    return generateFirestoreDocumentId();
  }
}

/**
 * Generate a document reference for a user's subcollection
 * @param {string} userId - User ID
 * @param {string} subcollectionName - Subcollection name
 * @param {string} documentId - Optional specific document ID
 * @return {Object} Firestore document reference
 */
function getUserSubcollectionDocumentRef(userId, subcollectionName, documentId = null) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();
    
    const collectionRef = db
      .collection('users')
      .doc(userId)
      .collection(subcollectionName);
    
    // Use provided ID or auto-generate
    return documentId ? collectionRef.doc(documentId) : collectionRef.doc();
  } catch (error) {
    console.error('Error creating user subcollection document reference:', error);
    throw error;
  }
}

/**
 * Check for existing background job and set new job tracker
 * @param {string} userId - User ID
 * @param {string} documentName - Background job document name
 * @param {string} jobType - Type of background job
 * @param {number} timeoutMinutes - Timeout in minutes (default: 5)
 * @param {string} userMessage - User-friendly error message template
 * @return {Promise<void>}
 */
async function checkAndSetBackgroundJob(userId, documentName, jobType, timeoutMinutes = 5, userMessage = null) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const backgroundJobRef = db
      .collection('users')
      .doc(userId)
      .collection('background_jobs')
      .doc(documentName);

    const currentServerTime = Timestamp.now();

    // Check if there's an existing background job
    const existingJob = await backgroundJobRef.get();

    if (existingJob.exists) {
      const jobData = existingJob.data();
      const startedAt = jobData.started_at;

      if (startedAt) {
        const startedAtDate = startedAt.toDate();
        const currentTime = currentServerTime.toDate();
        const timeDifferenceMinutes = (currentTime - startedAtDate) / (1000 * 60);

        // If job started less than timeout minutes ago, prevent new job
        if (timeDifferenceMinutes < timeoutMinutes) {
          const remainingMinutes = Math.ceil(timeoutMinutes - timeDifferenceMinutes);

          // Use custom message or default message
          const defaultMessage = `يوجد عملية ${jobType} قيد التنفيذ حالياً. يرجى الانتظار ${remainingMinutes} دقيقة أخرى قبل المحاولة مرة أخرى.`;
          const errorMessage = userMessage
            ? userMessage.replace('{remainingMinutes}', remainingMinutes)
            : defaultMessage;

          const error = new Error(errorMessage);
          error.code = 'BACKGROUND_JOB_IN_PROGRESS';
          error.details = {
            startedAt: startedAtDate.toISOString(),
            remainingMinutes,
            jobType,
            documentName,
            currentCounter: jobData.counter || 0,
          };
          throw error;
        }
      }

      // Job exists but timeout passed - update with incremented counter
      const currentCounter = jobData.counter || 0;
      await backgroundJobRef.update({
        started_at: currentServerTime,
        job_type: jobType,
        counter: currentCounter + 1,
        last_updated: currentServerTime,
      });

      console.log(`Background job tracker updated for user: ${userId}, job: ${jobType}, counter: ${currentCounter + 1}`);

    } else {
      // No existing job - create new one with counter = 1
      await backgroundJobRef.set({
        started_at: currentServerTime,
        job_type: jobType,
        user_id: userId,
        document_name: documentName,
        counter: 1,
        created_at: currentServerTime,
        last_updated: currentServerTime,
      });

      console.log(`Background job tracker created for user: ${userId}, job: ${jobType}, counter: 1`);
    }

  } catch (error) {
    console.error('Error checking/setting background job:', error);
    throw error;
  }
}

/**
 * Clear background job tracker (set started_at to null)
 * @param {string} userId - User ID
 * @param {string} documentName - Background job document name
 * @return {Promise<void>}
 */
async function clearBackgroundJob(userId, documentName) {
  try {
    const admin = initializeFirebase();
    const db = admin.firestore();

    const backgroundJobRef = db
      .collection('users')
      .doc(userId)
      .collection('background_jobs')
      .doc(documentName);

    // Check if document exists before updating
    const existingJob = await backgroundJobRef.get();

    if (existingJob.exists) {
      // Set started_at to null to indicate job is completed
      await backgroundJobRef.update({
        started_at: null,
        completed_at: Timestamp.now(),
        last_updated: Timestamp.now(),
      });

      console.log(`Background job tracker cleared for user: ${userId}, document: ${documentName}`);
    } else {
      console.log(`Background job tracker not found for user: ${userId}, document: ${documentName}`);
    }

  } catch (error) {
    console.error('Error clearing background job:', error);
    // Don't throw error here - this is cleanup, shouldn't fail the main operation
  }
}

module.exports = {
  generateFirestoreDocumentId,
  generateUserSubcollectionDocumentId,
  getUserSubcollectionDocumentRef,
  checkAndSetBackgroundJob,
  clearBackgroundJob,
};
