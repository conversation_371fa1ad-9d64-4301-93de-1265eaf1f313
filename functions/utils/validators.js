const Joi = require('joi');

// Meal plan request validation schema (for public endpoint)
const mealPlanRequestSchema = Joi.object({
  userId: Joi.string().optional().default('test-user'), // Made optional for testing
  preferences: Joi.object({
    dietaryRestrictions: Joi.array().items(Joi.string()).default([]),
    allergies: Joi.array().items(Joi.string()).default([]),
    cuisinePreferences: Joi.array().items(Joi.string()).default([]),
    mealsPerDay: Joi.number().min(1).max(6).default(3),
    calorieGoal: Joi.number().min(1000).max(5000).required(),
    proteinGoal: Joi.number().min(0).default(0),
    carbsGoal: Joi.number().min(0).default(0),
    fatGoal: Joi.number().min(0).default(0),
  }).required(),
  duration: Joi.number().min(1).max(30).default(7), // days
});

// Authenticated meal plan request validation schema (preferences from user profile)
const authenticatedMealPlanRequestSchema = Joi.object({
  // No additional fields required - userId comes from authentication
});

// Nutrition analysis request validation schema
const nutritionAnalysisSchema = Joi.object({
  foodItems: Joi.array().items(Joi.string()).min(1).required(),
  portion: Joi.string().default('1 serving'),
});

// User meal plans query validation schema
const mealPlansQuerySchema = Joi.object({
  limit: Joi.number().min(1).max(100).default(10),
  status: Joi.string().valid('active', 'inactive', 'completed').default('active'),
});

// Shopping list generation request validation schema
const shoppingListGenerationSchema = Joi.object({
  start_date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required()
    .messages({
      'string.pattern.base': 'start_date must be in YYYY-MM-DD format',
      'any.required': 'start_date is required'
    }),
  end_date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required()
    .messages({
      'string.pattern.base': 'end_date must be in YYYY-MM-DD format',
      'any.required': 'end_date is required'
    }),
  // Optional shopping list ID for replacement
  shopping_id: Joi.string().optional()
    .messages({
      'string.base': 'shopping_id must be a string'
    }),
  // Allow userId but strip it since we get it from auth middleware
  userId: Joi.string().optional().strip(),
})
.unknown(false) // Reject any other unknown fields
.custom((value, helpers) => {
  const startDate = new Date(value.start_date);
  const endDate = new Date(value.end_date);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return helpers.error('any.invalid', { message: 'Invalid date format. Use YYYY-MM-DD format.' });
  }

  if (startDate > endDate) {
    return helpers.error('any.invalid', { message: 'start_date must be before or equal to end_date' });
  }

  return value;
});

module.exports = {
  mealPlanRequestSchema,
  authenticatedMealPlanRequestSchema,
  nutritionAnalysisSchema,
  mealPlansQuerySchema,
  shoppingListGenerationSchema,
};
