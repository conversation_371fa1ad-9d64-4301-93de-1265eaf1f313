const {getOpenAI} = require('../config/openai');
const {createNutritionAnalysisPrompt} = require('../utils/prompts');
const {parseNutritionData, extractCompletionContent} = require('../utils/parsers');

/**
 * Analyzes nutrition content of food items using OpenAI
 * @param {string[]} foodItems - Array of food items to analyze
 * @param {string} portion - Portion size description
 * @returns {Object} Nutrition analysis data
 */
async function analyzeNutrition(foodItems, portion) {
  try {
    // Create prompt for nutrition analysis
    const prompt = createNutritionAnalysisPrompt(foodItems, portion);

    // Analyze nutrition using OpenAI
    const completion = await getOpenAI().chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a nutrition expert. Provide accurate nutritional analysis ' +
            'for food items in JSON format.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.3,
      max_tokens: 1000,
    });

    const nutritionText = extractCompletionContent(completion);
    if (!nutritionText) {
      throw new Error('Failed to analyze nutrition');
    }

    // Parse nutrition data
    const nutritionData = parseNutritionData(nutritionText);

    return nutritionData;
  } catch (error) {
    console.error('Error in analyzeNutrition service:', error);
    throw error;
  }
}

/**
 * Calculates daily nutrition goals based on user preferences
 * @param {Object} userPreferences - User dietary preferences
 * @returns {Object} Calculated nutrition goals
 */
function calculateNutritionGoals(userPreferences) {
  const {calorieGoal, proteinGoal, carbsGoal, fatGoal} = userPreferences;
  
  // If specific goals are not set, calculate based on calorie goal
  const defaultProtein = proteinGoal || Math.round(calorieGoal * 0.15 / 4); // 15% of calories
  const defaultCarbs = carbsGoal || Math.round(calorieGoal * 0.55 / 4); // 55% of calories
  const defaultFat = fatGoal || Math.round(calorieGoal * 0.30 / 9); // 30% of calories

  return {
    calories: calorieGoal,
    protein: defaultProtein,
    carbs: defaultCarbs,
    fat: defaultFat,
    fiber: Math.round(calorieGoal / 1000 * 14), // 14g per 1000 calories (recommended)
  };
}

/**
 * Validates nutrition data against user goals
 * @param {Object} nutritionData - Analyzed nutrition data
 * @param {Object} goals - User nutrition goals
 * @returns {Object} Validation results with recommendations
 */
function validateNutritionGoals(nutritionData, goals) {
  const {totalNutrition} = nutritionData;
  const recommendations = [];

  // Check calorie alignment
  const calorieRatio = totalNutrition.calories / goals.calories;
  if (calorieRatio < 0.8) {
    recommendations.push('Consider adding more calorie-dense foods to meet your daily goal');
  } else if (calorieRatio > 1.2) {
    recommendations.push('This meal plan exceeds your calorie goal - consider smaller portions');
  }

  // Check protein
  if (totalNutrition.protein < goals.protein * 0.8) {
    recommendations.push('Add more protein sources like lean meats, legumes, or dairy');
  }

  // Check fiber
  if (totalNutrition.fiber < goals.fiber * 0.8) {
    recommendations.push('Include more fiber-rich foods like vegetables, fruits, and whole grains');
  }

  return {
    isWithinGoals: recommendations.length === 0,
    recommendations,
    goalComparison: {
      calories: {target: goals.calories, actual: totalNutrition.calories},
      protein: {target: goals.protein, actual: totalNutrition.protein},
      carbs: {target: goals.carbs, actual: totalNutrition.carbs},
      fat: {target: goals.fat, actual: totalNutrition.fat},
      fiber: {target: goals.fiber, actual: totalNutrition.fiber},
    },
  };
}

module.exports = {
  analyzeNutrition,
  calculateNutritionGoals,
  validateNutritionGoals,
};
