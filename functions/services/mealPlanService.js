const OpenAIService = require('./openaiService');
const {initializeFirebase} = require('../config/firebase');
const {createMealPlanPrompt} = require('../utils/prompts');
const {parseMealPlan} = require('../utils/parsers');

/**
 * Generates a meal plan using OpenAI
 * @param {Object} preferences - User dietary preferences
 * @param {number} duration - Number of days for the meal plan
 * @param {string} userId - User ID for saving the plan
 * @return {Object} Generated meal plan
 */
async function generateMealPlan(preferences, duration, _userId) {
  try {
    console.log('Starting meal plan generation...', {duration, userId: _userId});
    const startTime = Date.now();

    // Create prompt for OpenAI
    const prompt = createMealPlanPrompt(preferences, duration);
    console.log('Prompt created, length:', prompt.length);

    // Get user's preferred language (default to Arabic)
    const preferredLanguage = preferences.preferred_language || 'ar';

    // Generate meal plan using abstract OpenAI service
    const mealPlanText = await OpenAIService.createMealPlan(prompt, preferredLanguage);

    const openAiTime = Date.now();
    console.log('OpenAI request completed in:', openAiTime - startTime, 'ms');

    console.log('OpenAI Response (first 500 chars):', mealPlanText.substring(0, 500));

    // Parse and structure the meal plan
    const mealPlan = parseMealPlan(mealPlanText, preferences, duration);

    const totalTime = Date.now();
    console.log('Meal plan generation completed in:', totalTime - startTime, 'ms');

    return mealPlan;
  } catch (error) {
    console.error('Error in generateMealPlan service:', error);
    throw error;
  }
}

/**
 * Saves a meal plan to Firestore
 * @param {string} userId - User ID
 * @param {Object} mealPlan - Generated meal plan
 * @param {Object} preferences - User preferences
 * @param {number} duration - Duration in days
 * @return {Object} Saved document reference
 */
async function saveMealPlan(userId, mealPlan, preferences, duration) {
  try {
    const admin = initializeFirebase();
    const docRef = await admin.firestore()
      .collection('meal_plans')
      .add({
        userId,
        mealPlan,
        preferences,
        duration,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: 'active',
      });

    console.log('Meal plan saved with ID:', docRef.id);
    return docRef;
  } catch (error) {
    console.error('Error saving meal plan:', error);
    throw error;
  }
}

/**
 * Retrieves user's meal plans from Firestore
 * @param {string} userId - User ID
 * @param {Object} options - Query options (limit, status)
 * @return {Array} Array of meal plans
 */
async function getUserMealPlans(userId, options = {}) {
  try {
    const admin = initializeFirebase();
    const {limit = 10, status = 'active'} = options;

    const mealPlansSnapshot = await admin.firestore()
      .collection('meal_plans')
      .where('userId', '==', userId)
      .where('status', '==', status)
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .get();

    const mealPlans = mealPlansSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return mealPlans;
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    throw error;
  }
}

module.exports = {
  generateMealPlan,
  saveMealPlan,
  getUserMealPlans,
};
