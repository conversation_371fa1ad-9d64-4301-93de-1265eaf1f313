{"@@locale": "en", "appName": "EasyDietAI", "@appName": {"description": "The name of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "register": "Register", "@register": {"description": "Register button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "home": "Home", "@home": {"description": "Home tab label"}, "mealPlanning": "Meal Planning", "@mealPlanning": {"description": "Meal planning tab label"}, "profile": "Profile", "@profile": {"description": "Profile tab label"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "breakfast": "Breakfast", "@breakfast": {"description": "Breakfast meal type"}, "lunch": "Lunch", "@lunch": {"description": "Lunch meal type"}, "dinner": "Dinner", "@dinner": {"description": "Dinner meal type"}, "snack": "Snack", "@snack": {"description": "Snack meal type"}, "calories": "Calories", "@calories": {"description": "Calories label"}, "protein": "<PERSON><PERSON>", "@protein": {"description": "Protein label"}, "carbs": "<PERSON><PERSON>", "@carbs": {"description": "Carbohydrates label"}, "fat": "Fat", "@fat": {"description": "Fat label"}, "fiber": "Fiber", "@fiber": {"description": "Fiber label"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "error": "Error", "@error": {"description": "Error message"}, "success": "Success", "@success": {"description": "Success message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "subscription": "Subscription", "@subscription": {"description": "Subscription page title"}, "premium": "Premium", "@premium": {"description": "Premium subscription label"}, "subscribe": "Subscribe Now", "@subscribe": {"description": "Subscribe button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "complete": "Complete", "@complete": {"description": "Complete button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "getStarted": "Get Started", "@getStarted": {"description": "Get started button text"}, "onboarding": "Onboarding", "@onboarding": {"description": "Onboarding page title"}, "personalInfo": "Personal Information", "@personalInfo": {"description": "Personal information step title"}, "physicalInfo": "Physical Information", "@physicalInfo": {"description": "Physical information step title"}, "healthInfo": "Health Information", "@healthInfo": {"description": "Health information step title"}, "goals": "Goals", "@goals": {"description": "Goals step title"}, "preferences": "Preferences", "@preferences": {"description": "Preferences step title"}, "notifications": "Notifications", "@notifications": {"description": "Notifications step title"}}