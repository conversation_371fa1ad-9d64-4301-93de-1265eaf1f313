import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

@freezed
class Failure with _$Failure {
  const factory Failure.server({
    required String message,
    int? statusCode,
  }) = ServerFailure;
  
  const factory Failure.network({
    required String message,
  }) = NetworkFailure;
  
  const factory Failure.cache({
    required String message,
  }) = CacheFailure;
  
  const factory Failure.validation({
    required String message,
    Map<String, String>? fieldErrors,
  }) = ValidationFailure;
  
  const factory Failure.authentication({
    required String message,
  }) = AuthenticationFailure;
  
  const factory Failure.authorization({
    required String message,
  }) = AuthorizationFailure;
  
  const factory Failure.notFound({
    required String message,
  }) = NotFoundFailure;
  
  const factory Failure.unknown({
    required String message,
    Object? error,
  }) = UnknownFailure;
}

extension FailureExtensions on Failure {
  String get userMessage {
    return when(
      server: (message, statusCode) {
        switch (statusCode) {
          case 500:
            return 'خطأ في الخادم، يرجى المحاولة لاحقاً';
          case 503:
            return 'الخدمة غير متاحة حالياً';
          default:
            return message.isNotEmpty ? message : 'حدث خطأ في الخادم';
        }
      },
      network: (message) => 'تحقق من اتصالك بالإنترنت',
      cache: (message) => 'خطأ في تحميل البيانات المحفوظة',
      validation: (message, fieldErrors) => message,
      authentication: (message) => 'يرجى تسجيل الدخول مرة أخرى',
      authorization: (message) => 'ليس لديك صلاحية للوصول لهذه الصفحة',
      notFound: (message) => 'العنصر المطلوب غير موجود',
      unknown: (message, error) => 'حدث خطأ غير متوقع',
    );
  }
  
  bool get isRetryable {
    return when(
      server: (message, statusCode) => statusCode != 400 && statusCode != 404,
      network: (message) => true,
      cache: (message) => true,
      validation: (message, fieldErrors) => false,
      authentication: (message) => false,
      authorization: (message) => false,
      notFound: (message) => false,
      unknown: (message, error) => true,
    );
  }
}
