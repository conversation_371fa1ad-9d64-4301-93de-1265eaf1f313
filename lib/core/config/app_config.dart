class AppConfig {
  static const String appName = 'EasyDietAI';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const String baseUrl = 'https://api.easydietai.com';
  static const String apiVersion = 'v1';

  // Firebase Configuration
  static const String firebaseProjectId = 'easydietai';

  // Firebase Functions URLs
  static String get functionsBaseUrl {
    if (isDebug) {
      // Local emulator URL
      return 'http://127.0.0.1:5004/$firebaseProjectId/us-central1/api';
    } else {
      // Production Firebase Functions URL
      return 'https://us-central1-$firebaseProjectId.cloudfunctions.net/api';
    }
  }

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userProfileKey = 'user_profile';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';

  // Hive Box Names
  static const String userBox = 'user_box';
  static const String mealPlanBox = 'meal_plan_box';
  static const String settingsBox = 'settings_box';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Cache Duration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(minutes: 30);

  // Subscription
  static const String premiumProductId = 'easydietai_premium_monthly';
  static const String yearlyProductId = 'easydietai_premium_yearly';

  // Notification Channels
  static const String mealReminderChannelId = 'meal_reminder';
  static const String generalChannelId = 'general';

  // Environment
  static bool get isDebug {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  static bool get isProduction => !isDebug;
}
