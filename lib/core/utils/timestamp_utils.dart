import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easydietai/core/utils/logger.dart';

/// Utility class for handling Firestore timestamp conversions
class TimestampUtils {
  /// Convert various timestamp formats to ISO 8601 string
  /// 
  /// Handles:
  /// - Firestore Timestamp objects
  /// - DateTime objects
  /// - ISO 8601 strings (validates format)
  /// - null values (returns current time)
  /// 
  /// Returns ISO 8601 formatted string that can be parsed by DateTime.parse()
  static String convertTimestampToString(dynamic timestamp) {
    if (timestamp == null) {
      return DateTime.now().toIso8601String();
    }

    if (timestamp is DateTime) {
      return timestamp.toIso8601String();
    }

    if (timestamp is Timestamp) {
      return timestamp.toDate().toIso8601String();
    }

    if (timestamp is String) {
      // Already a string, validate it's a proper ISO format
      try {
        DateTime.parse(timestamp);
        return timestamp;
      } catch (e) {
        AppLogger.warning('TimestampUtils: Invalid timestamp string format: $timestamp');
        return DateTime.now().toIso8601String();
      }
    }

    AppLogger.warning('TimestampUtils: Unknown timestamp type: ${timestamp.runtimeType}');
    return DateTime.now().toIso8601String();
  }

  /// Convert various timestamp formats to DateTime object
  /// 
  /// Handles:
  /// - Firestore Timestamp objects
  /// - DateTime objects (returns as-is)
  /// - ISO 8601 strings
  /// - null values (returns current time)
  /// 
  /// Returns DateTime object
  static DateTime convertTimestampToDateTime(dynamic timestamp) {
    if (timestamp == null) {
      return DateTime.now();
    }

    if (timestamp is DateTime) {
      return timestamp;
    }

    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }

    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        AppLogger.warning('TimestampUtils: Invalid timestamp string format: $timestamp');
        return DateTime.now();
      }
    }

    AppLogger.warning('TimestampUtils: Unknown timestamp type: ${timestamp.runtimeType}');
    return DateTime.now();
  }

  /// Convert various timestamp formats to nullable DateTime object
  /// 
  /// Similar to convertTimestampToDateTime but returns null for null input
  /// instead of current time
  /// 
  /// Returns DateTime object or null
  static DateTime? convertTimestampToDateTimeNullable(dynamic timestamp) {
    if (timestamp == null) {
      return null;
    }

    if (timestamp is DateTime) {
      return timestamp;
    }

    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }

    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        AppLogger.warning('TimestampUtils: Invalid timestamp string format: $timestamp');
        return null;
      }
    }

    // Handle Firestore timestamp as Map (when serialized/deserialized)
    if (timestamp is Map<String, dynamic>) {
      try {
        final seconds = timestamp['_seconds'];
        final nanoseconds = timestamp['_nanoseconds'];

        if (seconds != null) {
          // Convert seconds to milliseconds and add nanoseconds converted to milliseconds
          final secondsInt = (seconds is int) ? seconds : int.tryParse(seconds.toString()) ?? 0;
          final nanosecondsInt = (nanoseconds is int) ? nanoseconds : int.tryParse(nanoseconds?.toString() ?? '0') ?? 0;
          final milliseconds = (secondsInt * 1000) + (nanosecondsInt / 1000000).round();
          return DateTime.fromMillisecondsSinceEpoch(milliseconds);
        }
      } catch (e) {
        AppLogger.warning('TimestampUtils: Error parsing timestamp map: $e');
        return null;
      }
    }

    AppLogger.warning('TimestampUtils: Unknown timestamp type: ${timestamp.runtimeType}');
    return null;
  }

  /// Convert DateTime to Firestore Timestamp
  /// 
  /// Useful when saving data to Firestore
  static Timestamp dateTimeToTimestamp(DateTime dateTime) {
    return Timestamp.fromDate(dateTime);
  }

  /// Convert nullable DateTime to nullable Firestore Timestamp
  ///
  /// Returns null if input is null
  static Timestamp? dateTimeToTimestampNullable(DateTime? dateTime) {
    if (dateTime == null) return null;
    return Timestamp.fromDate(dateTime);
  }

  /// Convert Firestore Timestamp objects to ISO string format recursively
  ///
  /// Handles nested maps and lists containing timestamps
  /// Converts both Firestore Timestamps and DateTime objects to ISO strings
  ///
  /// Returns Map with all timestamps converted to ISO strings
  static Map<String, dynamic> convertFirestoreTimestampsToStrings(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Timestamp) {
        // Convert Timestamp to ISO string
        result[key] = value.toDate().toIso8601String();
      } else if (value is DateTime) {
        // Convert DateTime to ISO string
        result[key] = value.toIso8601String();
      } else if (value is Map<String, dynamic>) {
        // Recursively convert nested maps
        result[key] = convertFirestoreTimestampsToStrings(value);
      } else if (value is List) {
        // Handle lists that might contain maps with timestamps
        result[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return convertFirestoreTimestampsToStrings(item);
          } else if (item is Timestamp) {
            return item.toDate().toIso8601String();
          } else if (item is DateTime) {
            return item.toIso8601String();
          }
          return item;
        }).toList();
      } else {
        // Keep other values as-is
        result[key] = value;
      }
    }

    return result;
  }
}
