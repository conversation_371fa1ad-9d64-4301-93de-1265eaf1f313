import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/user_profile/data/models/user_profile.dart';
import '../utils/logger.dart';
import '../utils/timestamp_utils.dart';

part 'firestore_service.g.dart';

class FirestoreService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  FirestoreService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  /// Save user profile data to Firestore
  Future<void> saveUserProfile(UserProfile userProfile) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving user profile for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update the profile with current timestamp
      final updatedProfile = userProfile.copyWith(
        updatedAt: DateTime.now(),
      );

      await userDoc.set(updatedProfile.toJson(), SetOptions(merge: true));

      AppLogger.info('FirestoreService: User profile saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving user profile: $e');
      rethrow;
    }
  }

  /// Save user profile data to Firestore without preference fields to avoid duplication
  Future<void> saveUserProfileWithoutPreferences(UserProfile userProfile) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving user profile without preferences for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update the profile with current timestamp
      final updatedProfile = userProfile.copyWith(
        updatedAt: DateTime.now(),
      );

      // Get the full JSON and remove preference fields that would cause duplication
      final profileJson = updatedProfile.toJson();

      // Remove fields that should only exist in preferences or notification_settings
      // Preference fields (should be in users/{userId}/preferences)
      profileJson.remove('activity_level');
      profileJson.remove('allergies');
      profileJson.remove('dietary_restrictions');
      profileJson.remove('health_conditions');
      profileJson.remove('health_goal');
      profileJson.remove('medications');
      profileJson.remove('target_weight');
      profileJson.remove('daily_calorie_goal');
      profileJson.remove('selected_calorie_recommendation');
      profileJson.remove('selected_diet_type');
      profileJson.remove('custom_macro_distribution');
      profileJson.remove('use_custom_macros');
      profileJson.remove('favorite_ingredients');
      profileJson.remove('disliked_ingredients');
      profileJson.remove('favorite_cuisines');
      profileJson.remove('meals_per_day');
      profileJson.remove('snacks_per_day');
      profileJson.remove('daily_burned_calories');
      profileJson.remove('use_manual_calories');

      // Notification fields (should be in users/{userId}/notification_settings)
      profileJson.remove('meal_reminders');
      profileJson.remove('water_reminders');
      profileJson.remove('workout_reminders');
      profileJson.remove('progress_updates');

      await userDoc.set(profileJson, SetOptions(merge: true));

      AppLogger.info('FirestoreService: User profile saved successfully without preferences');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving user profile without preferences: $e');
      rethrow;
    }
  }

  /// Get complete user data from Firestore (profile, plan preferences, and notification settings separately)
  Future<Map<String, dynamic>?> getCompleteUserData([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting complete user data for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User profile not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      // Extract the separate components
      final userData = Map<String, dynamic>.from(data);
      final planPreferences = userData.remove('plan_preferences') as Map<String, dynamic>?;
      final notificationSettings = userData.remove('notification_settings') as Map<String, dynamic>?;

      // Convert Firestore Timestamp objects to DateTime-compatible format for all components
      final convertedUserData = _convertFirestoreTimestamps(userData);
      final convertedPlanPreferences = planPreferences != null ? _convertFirestoreTimestamps(planPreferences) : null;
      final convertedNotificationSettings = notificationSettings != null ? _convertFirestoreTimestamps(notificationSettings) : null;

      return {
        'user_profile': convertedUserData,
        'plan_preferences': convertedPlanPreferences,
        'notification_settings': convertedNotificationSettings,
      };
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting complete user data: $e');
      rethrow;
    }
  }

  /// Get user profile from Firestore with merged preferences and notification settings
  Future<UserProfile?> getUserProfile([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting user profile for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User profile not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      // Get nested plan preferences and notification settings
      final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
      final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

      // Log the onboarding completion status from Firestore
      AppLogger.info('FirestoreService: onboarding_completed from Firestore: ${data['onboarding_completed']}');

      // Merge plan preferences data into main profile data
      final mergedData = Map<String, dynamic>.from(data);
      if (planPreferences != null) {
        AppLogger.info('FirestoreService: Merging plan preferences: ${planPreferences.keys.toList()}');
        _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
      }
      if (notificationSettings != null) {
        AppLogger.info('FirestoreService: Merging notification settings: ${notificationSettings.keys.toList()}');
        _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
      }

      AppLogger.info('FirestoreService: Final merged data onboarding_completed: ${mergedData['onboarding_completed']}');
      AppLogger.info('FirestoreService: User profile retrieved and merged successfully');

      // Convert Firestore Timestamp objects to DateTime-compatible format
      final convertedData = _convertFirestoreTimestamps(mergedData);
      return UserProfile.fromJson(convertedData);
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting user profile: $e');
      rethrow;
    }
  }

  /// Merge plan preferences data into profile data for UserProfile creation
  void _mergePlanPreferencesIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> planPreferences) {
    // Health Information
    if (planPreferences.containsKey('allergies')) profileData['allergies'] = planPreferences['allergies'];
    if (planPreferences.containsKey('dietary_restrictions')) profileData['dietary_restrictions'] = planPreferences['dietary_restrictions'];
    if (planPreferences.containsKey('health_conditions')) profileData['health_conditions'] = planPreferences['health_conditions'];
    if (planPreferences.containsKey('medications')) profileData['medications'] = planPreferences['medications'];

    // Goals - with enum migration for backward compatibility
    if (planPreferences.containsKey('health_goal')) {
      profileData['health_goal'] = _migrateHealthGoalValue(planPreferences['health_goal']);
    }
    if (planPreferences.containsKey('target_weight')) profileData['target_weight'] = planPreferences['target_weight'];
    if (planPreferences.containsKey('daily_calorie_goal')) profileData['daily_calorie_goal'] = planPreferences['daily_calorie_goal'];
    if (planPreferences.containsKey('selected_calorie_recommendation')) profileData['selected_calorie_recommendation'] = planPreferences['selected_calorie_recommendation'];

    // Diet Type & Macros - with enum migration for backward compatibility
    if (planPreferences.containsKey('selected_diet_type')) {
      profileData['selected_diet_type'] = _migrateDietTypeValue(planPreferences['selected_diet_type']);
    }
    if (planPreferences.containsKey('custom_macro_distribution')) profileData['custom_macro_distribution'] = planPreferences['custom_macro_distribution'];
    if (planPreferences.containsKey('use_custom_macros')) profileData['use_custom_macros'] = planPreferences['use_custom_macros'];

    // Food Preferences
    if (planPreferences.containsKey('favorite_ingredients')) profileData['favorite_ingredients'] = planPreferences['favorite_ingredients'];
    if (planPreferences.containsKey('disliked_ingredients')) profileData['disliked_ingredients'] = planPreferences['disliked_ingredients'];
    if (planPreferences.containsKey('favorite_cuisines')) profileData['favorite_cuisines'] = planPreferences['favorite_cuisines'];
    if (planPreferences.containsKey('meals_per_day')) profileData['meals_per_day'] = planPreferences['meals_per_day'];
    if (planPreferences.containsKey('snacks_per_day')) profileData['snacks_per_day'] = planPreferences['snacks_per_day'];

    // Activity & Physical - with enum migration for backward compatibility
    if (planPreferences.containsKey('activity_level')) {
      profileData['activity_level'] = _migrateActivityLevelValue(planPreferences['activity_level']);
    }
    if (planPreferences.containsKey('daily_burned_calories')) profileData['daily_burned_calories'] = planPreferences['daily_burned_calories'];
    if (planPreferences.containsKey('use_manual_calories')) profileData['use_manual_calories'] = planPreferences['use_manual_calories'];
    if (planPreferences.containsKey('unit_system')) profileData['unit_system'] = planPreferences['unit_system'];
  }

  /// Merge notification settings data into profile data for UserProfile creation
  void _mergeNotificationSettingsIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> notificationSettings) {
    if (notificationSettings.containsKey('meal_reminders')) profileData['meal_reminders'] = notificationSettings['meal_reminders'];
    if (notificationSettings.containsKey('water_reminders')) profileData['water_reminders'] = notificationSettings['water_reminders'];
    if (notificationSettings.containsKey('workout_reminders')) profileData['workout_reminders'] = notificationSettings['workout_reminders'];
    if (notificationSettings.containsKey('progress_updates')) profileData['progress_updates'] = notificationSettings['progress_updates'];
  }

  /// Update specific fields in user profile
  /// WARNING: This method saves directly to user document root and may cause data duplication.
  /// Consider using saveUserProfileWithoutPreferences, saveUserPreferences, or saveNotificationSettings instead.
  @Deprecated('Use saveUserProfileWithoutPreferences, saveUserPreferences, or saveNotificationSettings to avoid data duplication')
  Future<void> updateUserProfileFields(Map<String, dynamic> fields) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Updating user profile fields for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Add updated timestamp
      fields['updated_at'] = FieldValue.serverTimestamp();

      await userDoc.update(fields);

      AppLogger.info('FirestoreService: User profile fields updated successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error updating user profile fields: $e');
      rethrow;
    }
  }

  /// Save plan preferences to nested structure in Firestore
  Future<void> savePlanPreferences(Map<String, dynamic> planPreferences) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving plan preferences for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update plan preferences with timestamp
      final planPreferencesData = {
        ...planPreferences,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await userDoc.set({
        'plan_preferences': planPreferencesData,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true),);

      AppLogger.info('FirestoreService: Plan preferences saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving plan preferences: $e');
      rethrow;
    }
  }

  /// Update specific field in plan preferences
  Future<void> updatePlanPreferencesField(String fieldName, dynamic value) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Updating plan preferences field $fieldName for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update specific field in plan preferences
      await userDoc.update({
        'plan_preferences.$fieldName': value,
        'plan_preferences.updated_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      AppLogger.info('FirestoreService: Plan preferences field $fieldName updated successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error updating plan preferences field $fieldName: $e');
      rethrow;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use savePlanPreferences instead')
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    return await savePlanPreferences(preferences);
  }

  /// Save notification settings to nested structure in Firestore
  Future<void> saveNotificationSettings(Map<String, dynamic> notificationSettings) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving notification settings for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update notification settings with timestamp
      final notificationData = {
        ...notificationSettings,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await userDoc.set({
        'notification_settings': notificationData,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true),);

      AppLogger.info('FirestoreService: Notification settings saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving notification settings: $e');
      rethrow;
    }
  }

  /// Get plan preferences from nested structure in Firestore
  Future<Map<String, dynamic>?> getPlanPreferences([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting plan preferences for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      return data['plan_preferences'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting plan preferences: $e');
      rethrow;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use getPlanPreferences instead')
  Future<Map<String, dynamic>?> getUserPreferences([String? userId]) async {
    return await getPlanPreferences(userId);
  }

  /// Get notification settings from nested structure in Firestore
  Future<Map<String, dynamic>?> getNotificationSettings([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting notification settings for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      return data['notification_settings'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting notification settings: $e');
      rethrow;
    }
  }

  /// Listen to notification settings changes in real-time
  Stream<Map<String, dynamic>?> listenToNotificationSettings([String? userId]) {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        AppLogger.warning('FirestoreService: No authenticated user for notification settings listener');
        return Stream.value(null);
      }

      AppLogger.info('FirestoreService: Setting up notification settings listener for $uid');

      return _firestore
          .collection('users')
          .doc(uid)
          .snapshots()
          .map((snapshot) {
        if (!snapshot.exists || snapshot.data() == null) {
          return null;
        }

        final data = snapshot.data()!;
        return data['notification_settings'] as Map<String, dynamic>?;
      });
    } catch (e) {
      AppLogger.error('FirestoreService: Error setting up notification settings listener: $e');
      return Stream.error(e);
    }
  }

  // Legacy Weight Tracking Methods (Deprecated - use generic methods instead)

  @Deprecated('Use generateDocumentId with subcollectionName: "weight_tracker"')
  String generateWeightEntryId() {
    return generateDocumentId('weight_tracker');
  }

  @Deprecated('Use saveToUserSubcollection with subcollectionName: "weight_tracker"')
  Future<String> saveWeightEntry(Map<String, dynamic> weightEntry) async {
    final entryId = weightEntry['id'] as String;

    // Prepare entry data (only the weight entry fields)
    final entryData = {
      'id': weightEntry['id'],
      'weight': weightEntry['weight'],
      'date': weightEntry['date'],
      'created_at': weightEntry['created_at'],
      'updated_at': weightEntry['updated_at'],
      'notes': weightEntry['notes'],
    };

    await saveToUserSubcollection(
      subcollectionName: 'weight_tracker',
      documentId: entryId,
      data: entryData,
      addMetadata: false, // Weight entries manage their own timestamps
    );

    return entryId;
  }

  @Deprecated('Use saveToUserDocument with field: "weight_tracker"')
  Future<void> saveWeightTrackingMetadata(Map<String, dynamic> metadata) async {
    // Prepare metadata with timestamps
    final metadataWithTimestamps = {
      ...metadata,
      'synced_at': FieldValue.serverTimestamp(),
      'last_modified': FieldValue.serverTimestamp(),
    };

    await saveToUserDocument(
      field: 'weight_tracker',
      data: metadataWithTimestamps,
    );
  }

  @Deprecated('Use getUserSubcollectionDocuments with subcollectionName: "weight_tracker"')
  Future<List<Map<String, dynamic>>> getWeightEntries() async {
    return getUserSubcollectionDocuments(
      subcollectionName: 'weight_tracker',
      orderByField: 'date',
      descending: true,
    );
  }

  @Deprecated('Use getFromUserDocument with field: "weight_tracker"')
  Future<Map<String, dynamic>?> getWeightTrackingMetadata() async {
    return getFromUserDocument('weight_tracker');
  }

  @Deprecated('Use deleteFromUserSubcollection with subcollectionName: "weight_tracker"')
  Future<bool> deleteWeightEntry(String entryId) async {
    try {
      await deleteFromUserSubcollection(
        subcollectionName: 'weight_tracker',
        documentId: entryId,
      );
      return true;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting weight entry: $e');
      return false;
    }
  }

  @Deprecated('Use batch operations with generic methods')
  Future<bool> deleteAllWeightTrackingData() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting all weight tracking data for $userId');

      final batch = _firestore.batch();

      // Delete all weight entries from subcollection
      final weightTrackingCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('weight_tracker');

      final querySnapshot = await weightTrackingCollectionRef.get();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete weight tracking metadata from user document
      final userDoc = _firestore.collection('users').doc(userId);
      batch.update(userDoc, {
        'weight_tracker': FieldValue.delete(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      AppLogger.info('FirestoreService: All weight tracking data deleted successfully');
      return true;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting all weight tracking data: $e');
      return false;
    }
  }

  // Legacy Water Intake Tracking Methods (Deprecated - move to feature-specific service)
  // Note: These methods contain complex business logic and should be moved to a dedicated
  // water intake service that uses the generic Firestore operations above.

  @Deprecated('Move to WaterIntakeService using generic Firestore operations')
  /// Save daily water intake data to Firestore
  Future<String> saveWaterIntakeData(String date, Map<String, dynamic> waterIntakeData) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving water intake data for $userId on $date');

      final waterIntakeCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('water_intake_tracking');

      // Check if document already exists for this date
      final existingDoc = await _getWaterIntakeDocumentForDate(date);

      String documentId;
      if (existingDoc != null) {
        // Update existing document
        documentId = existingDoc.id;
        AppLogger.info('FirestoreService: Updating existing water intake document: $documentId');
      } else {
        // Create new document with auto-generated ID
        final newDocRef = waterIntakeCollectionRef.doc();
        documentId = newDocRef.id;
        AppLogger.info('FirestoreService: Creating new water intake document: $documentId');
      }

      // Prepare data with metadata
      final firestoreData = {
        ...waterIntakeData,
        'date_hash_id': documentId,
        'synced_at': FieldValue.serverTimestamp(),
        'last_modified': FieldValue.serverTimestamp(),
      };

      // Save to Firestore
      await waterIntakeCollectionRef.doc(documentId).set(firestoreData, SetOptions(merge: true));

      AppLogger.info('FirestoreService: Water intake data saved successfully with document ID: $documentId');
      return documentId;
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving water intake data: $e');
      rethrow;
    }
  }

  @Deprecated('Move to WaterIntakeService using generic Firestore operations')
  /// Get water intake data for a specific date
  Future<Map<String, dynamic>?> getWaterIntakeDataForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting water intake data for $userId on $date');

      final doc = await _getWaterIntakeDocumentForDate(date);
      if (doc != null && doc.exists) {
        final data = doc.data() as Map<String, dynamic>?;
        if (data != null) {
          AppLogger.info('FirestoreService: Water intake data found for $date');
          return data;
        }
      }

      AppLogger.info('FirestoreService: No water intake data found for $date');
      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting water intake data: $e');
      rethrow;
    }
  }

  @Deprecated('Move to WaterIntakeService using generic Firestore operations')
  /// Get water intake document for a specific date
  Future<DocumentSnapshot?> _getWaterIntakeDocumentForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final waterIntakeCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('water_intake_tracking');

      // Query for documents with matching date
      final querySnapshot = await waterIntakeCollectionRef
          .where('date', isEqualTo: date)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first;
      }

      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting water intake document for date: $e');
      rethrow;
    }
  }

  @Deprecated('Move to WaterIntakeService using generic Firestore operations')
  /// Delete water intake data for a specific date
  Future<bool> deleteWaterIntakeDataForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting water intake data for $userId on $date');

      final doc = await _getWaterIntakeDocumentForDate(date);
      if (doc != null && doc.exists) {
        await doc.reference.delete();
        AppLogger.info('FirestoreService: Water intake data deleted successfully for $date');
        return true;
      }

      AppLogger.info('FirestoreService: No water intake data found to delete for $date');
      return false;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting water intake data: $e');
      return false;
    }
  }

  /// Mark onboarding as completed
  Future<void> markOnboardingCompleted() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Marking onboarding completed for $userId');

      final userDoc = _firestore.collection('users').doc(userId);
      await userDoc.update({
        'onboarding_completed': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      AppLogger.info('FirestoreService: Onboarding marked as completed');
    } catch (e) {
      AppLogger.error('FirestoreService: Error marking onboarding completed: $e');
      rethrow;
    }
  }

  /// Delete user profile
  Future<void> deleteUserProfile([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting user profile for $uid');

      await _firestore.collection('users').doc(uid).delete();

      AppLogger.info('FirestoreService: User profile deleted successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting user profile: $e');
      rethrow;
    }
  }

  /// Check if user profile exists
  Future<bool> userProfileExists([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        return false;
      }

      final userDoc = await _firestore.collection('users').doc(uid).get();
      return userDoc.exists;
    } catch (e) {
      AppLogger.error('FirestoreService: Error checking if user profile exists: $e');
      return false;
    }
  }

  /// Listen to user profile changes
  Stream<UserProfile?> watchUserProfile([String? userId]) {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        return Stream.value(null);
      }

      return _firestore
          .collection('users')
          .doc(uid)
          .snapshots()
          .map((snapshot) {
        if (!snapshot.exists || snapshot.data() == null) {
          return null;
        }

        final data = snapshot.data()!;

        // Get nested plan preferences and notification settings
        final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
        final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

        // Merge plan preferences data into main profile data
        final mergedData = Map<String, dynamic>.from(data);
        if (planPreferences != null) {
          _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
        }
        if (notificationSettings != null) {
          _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
        }

        // Convert Firestore Timestamp objects to DateTime-compatible format
        final convertedData = _convertFirestoreTimestamps(mergedData);
        return UserProfile.fromJson(convertedData);
      });
    } catch (e) {
      AppLogger.error('FirestoreService: Error watching user profile: $e');
      return Stream.error(e);
    }
  }

  /// Migrate old enum name format to new @JsonValue format for HealthGoal
  String _migrateHealthGoalValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'loseWeight':
        return 'lose_weight';
      case 'gainWeight':
        return 'gain_weight';
      case 'buildMuscle':
        return 'build_muscle';
      case 'improveHealth':
        return 'improve_health';
      case 'maintain':
        return 'maintain';
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for DietType
  String? _migrateDietTypeValue(dynamic value) {
    if (value == null) return null;
    if (value is! String) return value.toString();

    switch (value) {
      case 'highProtein':
        return 'high_protein';
      case 'lowCarb':
        return 'low_carb';
      case 'plantBased':
        return 'plant_based';
      case 'balanced':
      case 'ketogenic':
      case 'mediterranean':
      case 'custom':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for ActivityLevel
  String _migrateActivityLevelValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'veryActive':
        return 'very_active';
      case 'sedentary':
      case 'light':
      case 'moderate':
      case 'active':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for Gender
  String _migrateGenderValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'notSpecified':
        return 'not_specified';
      case 'male':
      case 'female':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Convert Firestore Timestamp objects to DateTime-compatible format for UserProfile
  Map<String, dynamic> _convertFirestoreTimestamps(Map<String, dynamic> data) {
    final convertedData = Map<String, dynamic>.from(data);

    // List of DateTime fields in UserProfile that need conversion
    final dateTimeFields = [
      'createdAt',
      'created_at',
      'updatedAt',
      'updated_at',
      'lastLoginAt',
      'last_login_at',
      'premiumExpiryDate',
      'premium_expiry_date',
    ];

    for (final field in dateTimeFields) {
      if (convertedData.containsKey(field) && convertedData[field] != null) {
        convertedData[field] = TimestampUtils.convertTimestampToString(convertedData[field]);
      }
    }

    return convertedData;
  }

  // Generic Collection Operations

  /// Save document to a user's subcollection
  Future<void> saveToUserSubcollection({
    required String subcollectionName,
    required String documentId,
    required Map<String, dynamic> data,
    bool addMetadata = true,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving document $documentId to $subcollectionName for $userId');

      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName)
          .doc(documentId);

      // Prepare data with optional metadata
      final firestoreData = addMetadata ? {
        ...data,
        'synced_at': FieldValue.serverTimestamp(),
        'last_modified': FieldValue.serverTimestamp(),
      } : data;

      await docRef.set(firestoreData, SetOptions(merge: true));

      AppLogger.info('FirestoreService: Successfully saved document $documentId to $subcollectionName');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving document to $subcollectionName: $e');
      rethrow;
    }
  }

  /// Get all documents from a user's subcollection
  Future<List<Map<String, dynamic>>> getUserSubcollectionDocuments({
    required String subcollectionName,
    String? orderByField,
    bool descending = true,
    int? limit,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Fetching documents from $subcollectionName for $userId');

      Query<Map<String, dynamic>> query = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName);

      // Add ordering if specified
      if (orderByField != null) {
        query = query.orderBy(orderByField, descending: descending);
      }

      // Add limit if specified
      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();

      final documents = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['firestore_id'] = doc.id;
        return data;
      }).toList();

      AppLogger.info('FirestoreService: Successfully fetched ${documents.length} documents from $subcollectionName');
      return documents;
    } catch (e) {
      AppLogger.error('FirestoreService: Error fetching documents from $subcollectionName: $e');
      return [];
    }
  }

  /// Delete document from a user's subcollection
  Future<void> deleteFromUserSubcollection({
    required String subcollectionName,
    required String documentId,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting document $documentId from $subcollectionName for $userId');

      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName)
          .doc(documentId);

      await docRef.delete();

      AppLogger.info('FirestoreService: Successfully deleted document $documentId from $subcollectionName');
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting document from $subcollectionName: $e');
      rethrow;
    }
  }


  /// Get real-time stream for latest added documents in a user's subcollection
  Stream<Map<String, dynamic>?> getLatestDocumentStream({
    required String subcollectionName,
    String orderByField = 'created_at',
    DateTime? afterTimestamp,
  }) {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final logSuffix = afterTimestamp != null
          ? ' after ${afterTimestamp.toIso8601String()}'
          : '';
      AppLogger.info('FirestoreService: Setting up latest document stream for $subcollectionName for $userId$logSuffix');

      Query<Map<String, dynamic>> query = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName)
          .orderBy(orderByField, descending: true)
          .limit(1);

      // Add timestamp filter if provided
      if (afterTimestamp != null) {
        query = query.where(orderByField, isGreaterThan: Timestamp.fromDate(afterTimestamp));
      }

      return query.snapshots().map((snapshot) {
        // Filter for only added documents (new entries)
        final addedDocs = snapshot.docChanges
            .where((change) => change.type == DocumentChangeType.added)
            .map((change) => change.doc)
            .toList();

        if (addedDocs.isNotEmpty) {
          // Return the most recent added document
          final latestDoc = addedDocs.first;
          final data = latestDoc.data();

          if (data != null) {
            final result = {
              'id': latestDoc.id,
              ...data,
            };

            AppLogger.info('FirestoreService: New document detected in $subcollectionName: ${latestDoc.id}');
            return result;
          } else {
            AppLogger.warning('FirestoreService: Document ${latestDoc.id} in $subcollectionName has no data');
          }
        }

        // No new additions detected
        return null;
      });
    } catch (e) {
      AppLogger.error('FirestoreService: Error setting up latest document stream for $subcollectionName: $e');
      return Stream.error(e);
    }
  }

  /// Get document by ID from a user's subcollection
  Future<Map<String, dynamic>?> getDocumentFromUserSubcollection({
    required String subcollectionName,
    required String documentId,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting document $documentId from $subcollectionName for $userId');

      final docRef = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName)
          .doc(documentId);

      final docSnapshot = await docRef.get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['firestore_id'] = docSnapshot.id;
        AppLogger.info('FirestoreService: Successfully retrieved document $documentId from $subcollectionName');
        return data;
      }

      AppLogger.info('FirestoreService: Document $documentId not found in $subcollectionName');
      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting document from $subcollectionName: $e');
      rethrow;
    }
  }

  /// Query documents from a user's subcollection with custom conditions
  Future<List<Map<String, dynamic>>> queryUserSubcollectionDocuments({
    required String subcollectionName,
    required String field,
    required dynamic value,
    String? orderByField,
    bool descending = true,
    int? limit,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Querying documents from $subcollectionName where $field == $value for $userId');

      Query<Map<String, dynamic>> query = _firestore
          .collection('users')
          .doc(userId)
          .collection(subcollectionName)
          .where(field, isEqualTo: value);

      // Add ordering if specified
      if (orderByField != null) {
        query = query.orderBy(orderByField, descending: descending);
      }

      // Add limit if specified
      if (limit != null) {
        query = query.limit(limit);
      }

      final querySnapshot = await query.get();

      final documents = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['firestore_id'] = doc.id;
        return data;
      }).toList();

      AppLogger.info('FirestoreService: Successfully queried ${documents.length} documents from $subcollectionName');
      return documents;
    } catch (e) {
      AppLogger.error('FirestoreService: Error querying documents from $subcollectionName: $e');
      return [];
    }
  }

  /// Generate a new Firestore document ID for a user's subcollection
  String generateDocumentId(String subcollectionName) {
    final userId = currentUserId;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    final collectionRef = _firestore
        .collection('users')
        .doc(userId)
        .collection(subcollectionName);

    // Generate auto-generated document ID
    return collectionRef.doc().id;
  }

  /// Save document to user's main document (not subcollection)
  /// If field is provided, saves data nested under that field
  /// If field is null, saves data directly to the root level of the user document
  Future<void> saveToUserDocument({
    String? field,
    required Map<String, dynamic> data,
    bool merge = true,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final userDoc = _firestore.collection('users').doc(userId);

      Map<String, dynamic> updateData;

      if (field != null) {
        // Save data nested under the specified field
        AppLogger.info('FirestoreService: Saving data to user document field: $field for $userId');
        updateData = {
          field: data,
          'updated_at': FieldValue.serverTimestamp(),
        };
      } else {
        // Save data directly to the root level of the user document
        AppLogger.info('FirestoreService: Saving data to user document root level for $userId');
        updateData = {
          ...data,
          'updated_at': FieldValue.serverTimestamp(),
        };
      }

      await userDoc.set(updateData, SetOptions(merge: merge));

      final location = field != null ? 'field: $field' : 'root level';
      AppLogger.info('FirestoreService: Successfully saved data to user document $location');
    } catch (e) {
      final location = field != null ? 'field $field' : 'root level';
      AppLogger.error('FirestoreService: Error saving data to user document $location: $e');
      rethrow;
    }
  }

  /// Generic method to get any document by path
  Future<Map<String, dynamic>?> getDocument(String path) async {
    try {
      AppLogger.info('FirestoreService: Getting document at path: $path');

      final doc = await _firestore.doc(path).get();

      if (!doc.exists) {
        AppLogger.info('FirestoreService: Document not found at path: $path');
        return null;
      }

      final data = doc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: Document exists but has no data at path: $path');
        return null;
      }

      return data;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting document at path $path: $e');
      rethrow;
    }
  }

  /// Generic method to update any document by path
  Future<void> updateDocument({
    required String path,
    required Map<String, dynamic> data,
    bool merge = true,
  }) async {
    try {
      AppLogger.info('FirestoreService: Updating document at path: $path');

      final doc = _firestore.doc(path);

      if (merge) {
        await doc.set(data, SetOptions(merge: true));
      } else {
        await doc.update(data);
      }

      AppLogger.info('FirestoreService: Successfully updated document at path: $path');
    } catch (e) {
      AppLogger.error('FirestoreService: Error updating document at path $path: $e');
      rethrow;
    }
  }

  /// Generic method to get document stream by path
  Stream<Map<String, dynamic>?> getDocumentStream(String path) {
    try {
      AppLogger.info('FirestoreService: Setting up document stream for path: $path');

      return _firestore.doc(path).snapshots().map((snapshot) {
        if (!snapshot.exists || snapshot.data() == null) {
          return null;
        }

        return snapshot.data()!;
      });
    } catch (e) {
      AppLogger.error('FirestoreService: Error setting up document stream for path $path: $e');
      return Stream.error(e);
    }
  }

  /// Get data from user's main document field
  Future<Map<String, dynamic>?> getFromUserDocument(String field) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting data from user document field: $field for $userId');

      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      final fieldData = data[field] as Map<String, dynamic>?;
      if (fieldData != null) {
        AppLogger.info('FirestoreService: Successfully retrieved data from user document field: $field');
        return fieldData;
      }

      AppLogger.info('FirestoreService: No data found in user document field: $field');
      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting data from user document field $field: $e');
      rethrow;
    }
  }

  // Legacy Shopping List Methods (Deprecated - use generic methods instead)

  @Deprecated('Use saveToUserSubcollection with subcollectionName: "shopping_list_entries"')
  Future<void> saveShoppingListEntry(String listId, Map<String, dynamic> shoppingListData) async {
    return saveToUserSubcollection(
      subcollectionName: 'shopping_list_entries',
      documentId: listId,
      data: shoppingListData,
    );
  }

  @Deprecated('Use getUserSubcollectionDocuments with subcollectionName: "shopping_list_entries"')
  Future<List<Map<String, dynamic>>> getShoppingListEntries() async {
    return getUserSubcollectionDocuments(
      subcollectionName: 'shopping_list_entries',
      orderByField: 'created_at',
      descending: true,
    );
  }

  @Deprecated('Use deleteFromUserSubcollection with subcollectionName: "shopping_list_entries"')
  Future<void> deleteShoppingListEntry(String listId) async {
    return deleteFromUserSubcollection(
      subcollectionName: 'shopping_list_entries',
      documentId: listId,
    );
  }

  @Deprecated('Use getLatestDocumentStream with subcollectionName: "shopping_list_entries"')
  Stream<Map<String, dynamic>?> getLatestShoppingListStream({DateTime? afterTimestamp}) {
    return getLatestDocumentStream(
      subcollectionName: 'shopping_list_entries',
      orderByField: 'created_at',
      afterTimestamp: afterTimestamp,
    );
  }
}

@riverpod
FirestoreService firestoreService(FirestoreServiceRef ref) {
  return FirestoreService();
}
