import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../config/app_config.dart';
import '../utils/logger.dart';
import 'local_storage_service.dart';

part 'dio_client.g.dart';

/// HTTP client service using Dio with interceptors
class DioClient {
  late final Dio _dio;
  final LocalStorageService _storageService;

  DioClient(this._storageService) {
    _dio = Dio();
    _setupInterceptors();
  }

  Dio get dio => _dio;

  void _setupInterceptors() {
    _dio.options = BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add interceptors
    _dio.interceptors.addAll([
      _AuthInterceptor(_storageService),
      _LoggingInterceptor(),
      _ErrorInterceptor(),
      _RetryInterceptor(),
    ]);
  }

  /// GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.get<T>(
      path,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.put<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.delete<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }

  /// PATCH request
  Future<Response<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return await _dio.patch<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
      cancelToken: cancelToken,
    );
  }
}

/// Authentication interceptor to add bearer token
class _AuthInterceptor extends Interceptor {
  final LocalStorageService _storageService;

  _AuthInterceptor(this._storageService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await _storageService.getUserToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
}

/// Logging interceptor for debugging
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.info('REQUEST[${options.method}] => PATH: ${options.path}');
      AppLogger.info('Headers: ${options.headers}');
      if (options.data != null) {
        AppLogger.info('Data: ${options.data}');
      }
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.info(
        'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
      );
      AppLogger.info('Data: ${response.data}');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.error(
        'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}',
      );
      AppLogger.error('Message: ${err.message}');
      if (err.response?.data != null) {
        AppLogger.error('Error Data: ${err.response?.data}');
      }
    }
    handler.next(err);
  }
}

/// Error handling interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw TimeoutException(err.requestOptions);
      case DioExceptionType.badResponse:
        switch (err.response?.statusCode) {
          case 400:
            throw BadRequestException(err.requestOptions);
          case 401:
            throw UnauthorizedException(err.requestOptions);
          case 403:
            throw ForbiddenException(err.requestOptions);
          case 404:
            throw NotFoundException(err.requestOptions);
          case 409:
            throw ConflictException(err.requestOptions);
          case 500:
            throw InternalServerErrorException(err.requestOptions);
          default:
            throw DioException(
              requestOptions: err.requestOptions,
              response: err.response,
              type: err.type,
              error: err.error,
            );
        }
      case DioExceptionType.cancel:
        break;
      case DioExceptionType.unknown:
        throw NoInternetConnectionException(err.requestOptions);
      default:
        throw DioException(
          requestOptions: err.requestOptions,
          response: err.response,
          type: err.type,
          error: err.error,
        );
    }
    handler.next(err);
  }
}

/// Retry interceptor for failed requests
class _RetryInterceptor extends Interceptor {
  static const int maxRetries = 3;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final extra = err.requestOptions.extra;
    final retryCount = extra['retryCount'] ?? 0;

    if ((retryCount as int) < maxRetries && _shouldRetry(err)) {
      extra['retryCount'] = retryCount + 1;

      // Wait before retrying
      await Future.delayed(Duration(seconds: (retryCount as int) + 1));

      try {
        final response = await Dio().fetch<dynamic>(err.requestOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        // Continue with original error if retry fails
      }
    }

    handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.sendTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.unknown ||
           (err.response?.statusCode != null && err.response!.statusCode! >= 500);
  }
}

// Custom exceptions
class TimeoutException extends DioException {
  TimeoutException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Connection timeout');
}

class BadRequestException extends DioException {
  BadRequestException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Bad request');
}

class UnauthorizedException extends DioException {
  UnauthorizedException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Unauthorized');
}

class ForbiddenException extends DioException {
  ForbiddenException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Forbidden');
}

class NotFoundException extends DioException {
  NotFoundException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Not found');
}

class ConflictException extends DioException {
  ConflictException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Conflict');
}

class InternalServerErrorException extends DioException {
  InternalServerErrorException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'Internal server error');
}

class NoInternetConnectionException extends DioException {
  NoInternetConnectionException(RequestOptions requestOptions)
      : super(requestOptions: requestOptions, error: 'No internet connection');
}

@riverpod
Future<DioClient> dioClient(DioClientRef ref) async {
  final storageService = await ref.watch(localStorageServiceProvider.future);
  return DioClient(storageService);
}
