import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF4CAF50);
  static const Color primaryLight = Color(0xFF81C784);
  static const Color primaryDark = Color(0xFF388E3C);
  
  // Secondary Colors
  static const Color secondary = Color(0xFFFF9800);
  static const Color secondaryLight = Color(0xFFFFB74D);
  static const Color secondaryDark = Color(0xFFF57C00);
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  
  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
  static const Color textSecondaryDark = Color(0xFFB3B3B3);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF424242);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Nutrition Colors
  static const Color protein = Color(0xFFE91E63);
  static const Color carbs = Color(0xFF2196F3);
  static const Color fat = Color(0xFFFF9800);
  static const Color fiber = Color(0xFF4CAF50);
  
  // Meal Type Colors
  static const Color breakfast = Color(0xFFFFEB3B);
  static const Color lunch = Color(0xFFFF9800);
  static const Color dinner = Color(0xFF673AB7);
  static const Color snack = Color(0xFF4CAF50);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Opacity Colors
  static Color get primaryWithOpacity => primary.withOpacity(0.1);
  static Color get secondaryWithOpacity => secondary.withOpacity(0.1);
  static Color get errorWithOpacity => error.withOpacity(0.1);
  static Color get successWithOpacity => success.withOpacity(0.1);
}
