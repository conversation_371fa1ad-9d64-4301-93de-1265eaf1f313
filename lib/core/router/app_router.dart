import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../utils/logger.dart';

import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/auth_page.dart';
import '../../features/auth/providers/auth_provider.dart';
import '../../features/intro_wizard/presentation/pages/intro_wizard_page.dart';
import '../../features/meal_planning/presentation/pages/home_page.dart';
import '../../features/meal_planning/presentation/pages/meal_details_page.dart';
import '../../features/meal_planning/data/models/meal_plan_request.dart';
import '../../features/nutrition_chart/presentation/pages/nutrition_chart_page.dart';
import '../../features/water_tracking/presentation/pages/water_tracking_page.dart';
import '../../features/shopping_list/presentation/pages/shopping_list_details_page.dart';
import '../../features/shopping_list/presentation/pages/shopping_lists_page.dart';
import '../../features/shopping_list/data/models/shopping_list_models.dart';
import '../../features/weight_tracking/presentation/pages/weight_tracking_page.dart';
import '../../features/notifications/presentation/pages/notifications_list_page.dart';
import '../../features/notifications/presentation/pages/notification_detail_page.dart';

import '../../features/user_profile/presentation/pages/profile_page.dart';
import '../../features/user_profile/presentation/pages/plan_settings_page.dart';
import '../../features/user_profile/presentation/pages/notifications_settings_page.dart';
import '../../features/user_profile/presentation/pages/notification_category_detail_page.dart';
import '../../features/user_profile/presentation/pages/app_settings_page.dart';
import '../../features/user_profile/presentation/pages/faq_page.dart';
import '../../features/onboarding/presentation/pages/onboarding_page.dart';
import '../../features/onboarding/presentation/pages/settings_onboarding_page.dart';
import '../../features/onboarding/providers/onboarding_provider.dart';

import '../../shared/providers/app_state_provider.dart';
import '../../features/user_profile/data/models/user_profile.dart';
import '../../features/onboarding/data/models/plan_preferences.dart';
import 'app_routes.dart';

part 'app_router.g.dart';

/// Helper function to check if user profile is complete
bool _isProfileComplete(UserProfile? userProfile, PlanPreferences? planPreferences, bool localOnboardingCompleted) {
  // First priority: Check local app state for onboarding completion
  if (localOnboardingCompleted) {
    return true;
  }

  if (userProfile == null) return false;

  // Second priority: Check if onboarding was explicitly marked as completed in profile
  if (userProfile.onboardingCompleted == true) {
    return true;
  }

  // Third priority: Check if essential profile data exists (fallback)
  if (planPreferences == null) return false;

  final hasPersonalInfo = planPreferences.dateOfBirth != null &&
                         planPreferences.gender != Gender.notSpecified;

  final hasPhysicalInfo = planPreferences.height != null &&
                         planPreferences.weight != null &&
                         planPreferences.activityLevel != ActivityLevel.moderate; // Default value means not set

  final hasGoals = planPreferences.healthGoal != HealthGoal.maintain && // Default value means not set
                  planPreferences.dailyCalorieGoal != null;

  // Profile is considered complete if it has all essential data
  return hasPersonalInfo && hasPhysicalInfo && hasGoals;
}

@riverpod
GoRouter appRouter(AppRouterRef ref) {
  final appState = ref.watch(appStateNotifierProvider);

  return GoRouter(
    initialLocation: AppRoutes.splash,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final currentPath = state.uri.path;
      final fullLocation = state.uri.toString();

      AppLogger.debug('Router: Evaluating path=$currentPath, fullLocation=$fullLocation, isInitialized=${appState.isInitialized}, authStatus=${appState.authStatus}');

      // Skip redirect logic if we're dealing with popup/overlay navigation artifacts
      if (currentPath == '/' && fullLocation != '/' && appState.authStatus == AuthStatus.loading) {
        AppLogger.debug('Router: Detected popup navigation artifact during auth loading, skipping redirect');
        return null;
      }

      // Get auth navigation provider to check where authentication was initiated from
      final authInitiatedFromPath = ref.read(authNavigationProviderProvider);

      // If auth is loading and we have a tracked path, prevent root path redirects
      if (appState.authStatus == AuthStatus.loading && authInitiatedFromPath != null && currentPath == '/') {
        AppLogger.debug('Router: Preventing root redirect during auth, initiated from: $authInitiatedFromPath');
        return null;
      }

      // Step 1: If not initialized, stay on splash (splash will mark as initialized after 3 seconds)
      if (!appState.isInitialized) {
        AppLogger.debug('Router: App not initialized, staying on splash');
        return currentPath == AppRoutes.splash ? null : AppRoutes.splash;
      }

      // Step 2: If auth is loading (e.g., during Google Sign-In), don't redirect to avoid app restart
      if (appState.authStatus == AuthStatus.loading) {
        AppLogger.debug('Router: Auth is loading, maintaining current route to prevent restart');

        // Additional protection: if currentPath is root but we're in auth flow, stay put
        if (currentPath == '/' && (fullLocation.contains('auth') || context.mounted)) {
          AppLogger.debug('Router: Preventing root redirect during auth loading');
        }

        return null; // Stay on current route
      }

      // Step 3: If we're on splash and app is initialized, redirect based on state
      if (currentPath == AppRoutes.splash && appState.isInitialized) {
        AppLogger.debug('Router: App initialized, redirecting from splash');
        // Let the logic below determine where to go
      }

      // Step 4: Check intro wizard requirement
      final needsIntroWizard = appState.isFirstLaunch && !appState.hasSeenIntroWizard;

      if (needsIntroWizard && currentPath != AppRoutes.introWizard) {
        AppLogger.debug('Router: Redirecting to intro wizard');
        return AppRoutes.introWizard;
      }

      // Step 5: Check authentication and profile completion
      final isAuthenticated = appState.authStatus == AuthStatus.authenticated;
      final userProfile = appState.userProfile;
      final planPreferences = appState.planPreferences;
      final isGuestUser = appState.isGuestUser;

      // Determine if profile is complete using both local and remote state
      final isProfileComplete = _isProfileComplete(userProfile, planPreferences, appState.isOnboardingCompleted);

      AppLogger.debug('Router: needsIntroWizard=$needsIntroWizard, isAuthenticated=$isAuthenticated, isGuestUser=$isGuestUser, isProfileComplete=$isProfileComplete, localOnboardingCompleted=${appState.isOnboardingCompleted}');

      // If intro wizard is done but user is not authenticated AND not a guest user, show welcome auth
      if (!needsIntroWizard && !isAuthenticated && !isGuestUser && currentPath != AppRoutes.auth) {
        AppLogger.debug('Router: Redirecting to welcome auth (not authenticated, not guest)');
        return AppRoutes.auth;
      }

      // If authenticated but profile incomplete, show onboarding
      if (isAuthenticated && !isProfileComplete && !currentPath.startsWith('/onboarding')) {
        AppLogger.debug('Router: Redirecting to onboarding (authenticated, profile incomplete)');
        return AppRoutes.onboarding;
      }

      // If guest user but onboarding not completed, allow access to onboarding
      // EXCEPT when they explicitly want to go to welcome auth (for login)
      if (isGuestUser && !isProfileComplete &&
          !currentPath.startsWith('/onboarding') &&
          currentPath != AppRoutes.auth) {
        AppLogger.debug('Router: Redirecting to onboarding (guest user, profile incomplete)');
        return AppRoutes.onboarding;
      }

      // Allow guest users to access welcome auth page for login
      if (isGuestUser && currentPath == AppRoutes.auth) {
        AppLogger.debug('Router: Allowing guest user access to welcome auth page');
        return null; // Allow navigation to welcome auth
      }

      // If everything is complete, redirect to home from initial screens
      // This applies to both authenticated users and guest users who completed onboarding
      if (!needsIntroWizard && isProfileComplete &&
          (isAuthenticated || isGuestUser) &&
          (currentPath == AppRoutes.splash ||
           currentPath == AppRoutes.introWizard ||
           currentPath == AppRoutes.auth ||
           currentPath.startsWith('/onboarding'))) {
        AppLogger.debug('Router: Redirecting to home (user with completed profile)');
        return AppRoutes.home;
      }

      AppLogger.debug('Router: No redirect needed');
      return null; // No redirect needed
    },
    routes: [
      // Splash Route
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Introduction Wizard Route
      GoRoute(
        path: AppRoutes.introWizard,
        name: 'intro-wizard',
        builder: (context, state) => const IntroWizardPage(),
      ),

      // Welcome Auth Route
      GoRoute(
        path: AppRoutes.auth,
        name: 'auth',
        builder: (context, state) => const AuthPage(),
      ),

      // Onboarding Route
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) {
          // Get the step parameter from query parameters
          final stepParam = state.uri.queryParameters['step'];
          int? initialStepIndex;

          if (stepParam != null) {
            initialStepIndex = int.tryParse(stepParam);
          }

          return OnboardingPage(initialStepIndex: initialStepIndex);
        },
      ),

      // Main App Routes
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HomePage(),
        routes: [
          GoRoute(
            path: 'meal-details',
            name: 'meal-details',
            pageBuilder: (context, state) {
              // Extract meal and dayMealPlan from extra
              final extra = state.extra as Map<String, dynamic>?;
              if (extra == null || !extra.containsKey('meal') || !extra.containsKey('dayMealPlan')) {
                throw Exception('Meal details route requires meal and dayMealPlan parameters');
              }

              final meal = extra['meal'] as GeneratedMeal;
              final dayMealPlan = extra['dayMealPlan'] as DayMealPlan;
              final date = extra['date'] as String? ?? dayMealPlan.date.toIso8601String();
              final mealType = extra['mealType'] as String? ?? meal.type;
              final mealSlot = extra['mealSlot'] as int? ?? 0;

              return CustomTransitionPage<void>(
                key: state.pageKey,
                child: MealDetailsPage(
                  meal: meal,
                  dayMealPlan: dayMealPlan,
                  date: date,
                  mealType: mealType,
                  mealSlot: mealSlot,
                ),
                transitionsBuilder: (context, animation, secondaryAnimation, child) {
                  const begin = Offset(0.0, 1.0);
                  const end = Offset.zero;
                  const curve = Curves.easeInOut;

                  var tween = Tween(begin: begin, end: end).chain(
                    CurveTween(curve: curve),
                  );

                  return SlideTransition(
                    position: animation.drive(tween),
                    child: child,
                  );
                },
              );
            },
          ),
          GoRoute(
            path: 'nutrition-chart',
            name: 'nutrition-chart',
            builder: (context, state) => const NutritionChartPage(),
          ),
          GoRoute(
            path: 'water-tracking',
            name: 'water-tracking',
            builder: (context, state) => const WaterTrackingPage(),
          ),
          GoRoute(
            path: 'shopping-list',
            name: 'shopping-list',
            builder: (context, state) => const ShoppingListsPage(),
            routes: [
              GoRoute(
                path: 'detail',
                name: 'shopping-list-detail',
                builder: (context, state) {
                  final shoppingList = state.extra as ShoppingList?;
                  return ShoppingListDetailsPage(shoppingList: shoppingList);
                },
              ),
            ],
          ),
          GoRoute(
            path: 'weight-tracking',
            name: 'weight-tracking',
            builder: (context, state) => const WeightTrackingPage(),
          ),
          GoRoute(
            path: 'notifications',
            name: 'notifications',
            builder: (context, state) => const NotificationsListPage(),
            routes: [
              GoRoute(
                path: 'detail',
                name: 'notification-detail',
                builder: (context, state) {
                  final notificationId = state.uri.queryParameters['id']!;
                  return NotificationDetailPage(notificationId: notificationId);
                },
              ),
            ],
          ),
        ],
      ),

      // Profile Route
      GoRoute(
        path: AppRoutes.profile,
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
        routes: [
          GoRoute(
            path: 'settings',
            name: 'settings',
            builder: (context, state) => const PlanSettingsPage(),
            routes: [
              GoRoute(
                path: 'personal',
                name: 'personal-settings',
                builder: (context, state) {
                  // Get the step parameter from query parameters
                  final stepParam = state.uri.queryParameters['step'];
                  final editModeParam = state.uri.queryParameters['edit'];
                  OnboardingStep? initialStep;
                  bool isEditMode = editModeParam == 'true';

                  if (stepParam != null) {
                    // Convert string to OnboardingStep enum
                    try {
                      initialStep = OnboardingStep.values.firstWhere(
                        (step) => step.name == stepParam,
                      );
                    } catch (e) {
                      // If invalid step, use null (will default to current step)
                      initialStep = null;
                    }
                  }

                  return SettingsOnboardingPage(
                    initialStep: initialStep,
                    isEditMode: isEditMode,
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: 'notifications',
            name: 'notifications-settings',
            builder: (context, state) => const NotificationsSettingsPage(),
            routes: [
              GoRoute(
                path: ':categoryName',
                name: 'notification-category-detail',
                builder: (context, state) {
                  final categoryName = state.pathParameters['categoryName']!;
                  return NotificationCategoryDetailPage(categoryName: categoryName);
                },
              ),
            ],
          ),
          GoRoute(
            path: 'app-settings',
            name: 'app-settings',
            builder: (context, state) => const AppSettingsPage(),
          ),
          GoRoute(
            path: 'faq',
            name: 'faq',
            builder: (context, state) => const FaqPage(),
          ),
        ],
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في التنقل',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
}
