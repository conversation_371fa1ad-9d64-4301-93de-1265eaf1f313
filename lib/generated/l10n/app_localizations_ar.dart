/// Generated file. Do not edit.
///
/// Original: {arb-file}
/// To regenerate, run: `flutter gen-l10n`
///


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'EasyDietAI';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get home => 'الرئيسية';

  @override
  String get mealPlanning => 'خطة الوجبات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get breakfast => 'الإفطار';

  @override
  String get lunch => 'الغداء';

  @override
  String get dinner => 'العشاء';

  @override
  String get snack => 'وجبة خفيفة';

  @override
  String get calories => 'السعرات الحرارية';

  @override
  String get protein => 'البروتين';

  @override
  String get carbs => 'الكربوهيدرات';

  @override
  String get fat => 'الدهون';

  @override
  String get fiber => 'الألياف';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get subscription => 'الاشتراك';

  @override
  String get premium => 'المميز';

  @override
  String get subscribe => 'اشترك الآن';

  @override
  String get back => 'السابق';

  @override
  String get next => 'التالي';

  @override
  String get complete => 'إكمال';

  @override
  String get skip => 'تخطي';

  @override
  String get getStarted => 'ابدأ الآن';

  @override
  String get onboarding => 'الإعداد الأولي';

  @override
  String get personalInfo => 'المعلومات الشخصية';

  @override
  String get physicalInfo => 'المعلومات الجسدية';

  @override
  String get healthInfo => 'المعلومات الصحية';

  @override
  String get goals => 'الأهداف';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get notifications => 'الإشعارات';
}
