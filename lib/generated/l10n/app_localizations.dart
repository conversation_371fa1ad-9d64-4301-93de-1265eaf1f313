/// Generated file. Do not edit.
///
/// Original: {arb-file}
/// To regenerate, run: `flutter gen-l10n`
///

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// The name of the application
  ///
  /// In ar, this message translates to:
  /// **'EasyDietAI'**
  String get appName;

  /// Welcome message
  ///
  /// In ar, this message translates to:
  /// **'مرحباً'**
  String get welcome;

  /// Login button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get login;

  /// Register button text
  ///
  /// In ar, this message translates to:
  /// **'إنشاء حساب'**
  String get register;

  /// Email field label
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// Password field label
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور'**
  String get password;

  /// Confirm password field label
  ///
  /// In ar, this message translates to:
  /// **'تأكيد كلمة المرور'**
  String get confirmPassword;

  /// Forgot password link text
  ///
  /// In ar, this message translates to:
  /// **'نسيت كلمة المرور؟'**
  String get forgotPassword;

  /// Home tab label
  ///
  /// In ar, this message translates to:
  /// **'الرئيسية'**
  String get home;

  /// Meal planning tab label
  ///
  /// In ar, this message translates to:
  /// **'خطة الوجبات'**
  String get mealPlanning;

  /// Profile tab label
  ///
  /// In ar, this message translates to:
  /// **'الملف الشخصي'**
  String get profile;

  /// Settings page title
  ///
  /// In ar, this message translates to:
  /// **'الإعدادات'**
  String get settings;

  /// Breakfast meal type
  ///
  /// In ar, this message translates to:
  /// **'الإفطار'**
  String get breakfast;

  /// Lunch meal type
  ///
  /// In ar, this message translates to:
  /// **'الغداء'**
  String get lunch;

  /// Dinner meal type
  ///
  /// In ar, this message translates to:
  /// **'العشاء'**
  String get dinner;

  /// Snack meal type
  ///
  /// In ar, this message translates to:
  /// **'وجبة خفيفة'**
  String get snack;

  /// Calories label
  ///
  /// In ar, this message translates to:
  /// **'السعرات الحرارية'**
  String get calories;

  /// Protein label
  ///
  /// In ar, this message translates to:
  /// **'البروتين'**
  String get protein;

  /// Carbohydrates label
  ///
  /// In ar, this message translates to:
  /// **'الكربوهيدرات'**
  String get carbs;

  /// Fat label
  ///
  /// In ar, this message translates to:
  /// **'الدهون'**
  String get fat;

  /// Fiber label
  ///
  /// In ar, this message translates to:
  /// **'الألياف'**
  String get fiber;

  /// Save button text
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// Cancel button text
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// Delete button text
  ///
  /// In ar, this message translates to:
  /// **'حذف'**
  String get delete;

  /// Edit button text
  ///
  /// In ar, this message translates to:
  /// **'تعديل'**
  String get edit;

  /// Loading message
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// Error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// Success message
  ///
  /// In ar, this message translates to:
  /// **'نجح'**
  String get success;

  /// Retry button text
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// Logout button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الخروج'**
  String get logout;

  /// Subscription page title
  ///
  /// In ar, this message translates to:
  /// **'الاشتراك'**
  String get subscription;

  /// Premium subscription label
  ///
  /// In ar, this message translates to:
  /// **'المميز'**
  String get premium;

  /// Subscribe button text
  ///
  /// In ar, this message translates to:
  /// **'اشترك الآن'**
  String get subscribe;

  /// Back button text
  ///
  /// In ar, this message translates to:
  /// **'السابق'**
  String get back;

  /// Next button text
  ///
  /// In ar, this message translates to:
  /// **'التالي'**
  String get next;

  /// Complete button text
  ///
  /// In ar, this message translates to:
  /// **'إكمال'**
  String get complete;

  /// Skip button text
  ///
  /// In ar, this message translates to:
  /// **'تخطي'**
  String get skip;

  /// Get started button text
  ///
  /// In ar, this message translates to:
  /// **'ابدأ الآن'**
  String get getStarted;

  /// Onboarding page title
  ///
  /// In ar, this message translates to:
  /// **'الإعداد الأولي'**
  String get onboarding;

  /// Personal information step title
  ///
  /// In ar, this message translates to:
  /// **'المعلومات الشخصية'**
  String get personalInfo;

  /// Physical information step title
  ///
  /// In ar, this message translates to:
  /// **'المعلومات الجسدية'**
  String get physicalInfo;

  /// Health information step title
  ///
  /// In ar, this message translates to:
  /// **'المعلومات الصحية'**
  String get healthInfo;

  /// Goals step title
  ///
  /// In ar, this message translates to:
  /// **'الأهداف'**
  String get goals;

  /// Preferences step title
  ///
  /// In ar, this message translates to:
  /// **'التفضيلات'**
  String get preferences;

  /// Notifications step title
  ///
  /// In ar, this message translates to:
  /// **'الإشعارات'**
  String get notifications;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
