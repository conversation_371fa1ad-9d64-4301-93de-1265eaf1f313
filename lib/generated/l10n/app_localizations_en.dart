/// Generated file. Do not edit.
///
/// Original: {arb-file}
/// To regenerate, run: `flutter gen-l10n`
///


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'EasyDietAI';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get home => 'Home';

  @override
  String get mealPlanning => 'Meal Planning';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get breakfast => 'Breakfast';

  @override
  String get lunch => 'Lunch';

  @override
  String get dinner => 'Dinner';

  @override
  String get snack => 'Snack';

  @override
  String get calories => 'Calories';

  @override
  String get protein => 'Protein';

  @override
  String get carbs => 'Carbs';

  @override
  String get fat => 'Fat';

  @override
  String get fiber => 'Fiber';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get retry => 'Retry';

  @override
  String get logout => 'Logout';

  @override
  String get subscription => 'Subscription';

  @override
  String get premium => 'Premium';

  @override
  String get subscribe => 'Subscribe Now';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get complete => 'Complete';

  @override
  String get skip => 'Skip';

  @override
  String get getStarted => 'Get Started';

  @override
  String get onboarding => 'Onboarding';

  @override
  String get personalInfo => 'Personal Information';

  @override
  String get physicalInfo => 'Physical Information';

  @override
  String get healthInfo => 'Health Information';

  @override
  String get goals => 'Goals';

  @override
  String get preferences => 'Preferences';

  @override
  String get notifications => 'Notifications';
}
