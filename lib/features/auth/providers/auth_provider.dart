import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../core/utils/logger.dart';
import '../../../core/services/local_storage_service.dart';
import '../data/services/auth_service.dart';
import '../utils/auth_helpers.dart';
import '../utils/auth_validators.dart';

part '../providers/auth_provider.g.dart';

/// Auth form state for managing form inputs and validation
class AuthFormState {
  final String email;
  final String password;
  final String confirmPassword;
  final String displayName;
  final bool isLoading;
  final String? emailError;
  final String? passwordError;
  final String? confirmPasswordError;
  final String? displayNameError;
  final String? generalError;

  const AuthFormState({
    this.email = '',
    this.password = '',
    this.confirmPassword = '',
    this.displayName = '',
    this.isLoading = false,
    this.emailError,
    this.passwordError,
    this.confirmPasswordError,
    this.displayNameError,
    this.generalError,
  });

  AuthFormState copyWith({
    String? email,
    String? password,
    String? confirmPassword,
    String? displayName,
    bool? isLoading,
    String? emailError,
    String? passwordError,
    String? confirmPasswordError,
    String? displayNameError,
    String? generalError,
  }) {
    return AuthFormState(
      email: email ?? this.email,
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      displayName: displayName ?? this.displayName,
      isLoading: isLoading ?? this.isLoading,
      emailError: emailError ?? this.emailError,
      passwordError: passwordError ?? this.passwordError,
      confirmPasswordError: confirmPasswordError ?? this.confirmPasswordError,
      displayNameError: displayNameError ?? this.displayNameError,
      generalError: generalError ?? this.generalError,
    );
  }

  bool get isValid => 
      emailError == null && 
      passwordError == null && 
      confirmPasswordError == null && 
      displayNameError == null &&
      email.isNotEmpty && 
      password.isNotEmpty;

  bool get hasErrors => 
      emailError != null || 
      passwordError != null || 
      confirmPasswordError != null || 
      displayNameError != null || 
      generalError != null;
}

/// Auth provider for managing authentication form state and operations
/// This is separate from the global app state and focuses on auth-specific UI logic
@riverpod
class AuthProvider extends _$AuthProvider {
  AuthService? _authService;

  @override
  AuthFormState build() {
    // Initialize auth service asynchronously
    _initializeAuthService();
    return const AuthFormState();
  }

  /// Initialize auth service asynchronously
  Future<void> _initializeAuthService() async {
    try {
      _authService = await ref.read(authServiceProvider.future);
    } catch (e) {
      AppLogger.error('AuthProvider: Failed to initialize auth service: $e');
    }
  }

  /// Get auth service, initializing if needed
  Future<AuthService> _getAuthService() async {
    if (_authService == null) {
      await _initializeAuthService();
    }
    if (_authService == null) {
      throw Exception('Auth service not initialized');
    }
    return _authService!;
  }

  /// Update email field
  void updateEmail(String email) {
    final sanitizedEmail = AuthValidators.sanitizeEmail(email);
    final emailError = AuthValidators.validateEmail(sanitizedEmail);
    
    state = state.copyWith(
      email: sanitizedEmail,
      emailError: emailError,
      generalError: null, // Clear general error when user types
    );
  }

  /// Update password field
  void updatePassword(String password) {
    final passwordError = AuthValidators.validatePassword(password);
    
    state = state.copyWith(
      password: password,
      passwordError: passwordError,
      generalError: null,
    );
  }

  /// Update confirm password field
  void updateConfirmPassword(String confirmPassword) {
    final confirmPasswordError = AuthValidators.validatePasswordConfirmation(
      state.password,
      confirmPassword,
    );
    
    state = state.copyWith(
      confirmPassword: confirmPassword,
      confirmPasswordError: confirmPasswordError,
      generalError: null,
    );
  }

  /// Update display name field
  void updateDisplayName(String displayName) {
    final sanitizedName = AuthValidators.sanitizeDisplayName(displayName);
    final displayNameError = AuthValidators.validateDisplayName(sanitizedName);
    
    state = state.copyWith(
      displayName: sanitizedName,
      displayNameError: displayNameError,
      generalError: null,
    );
  }

  /// Validate all fields
  void validateAllFields() {
    final emailError = AuthValidators.validateEmail(state.email);
    final passwordError = AuthValidators.validatePassword(state.password);
    final confirmPasswordError = state.confirmPassword.isNotEmpty
        ? AuthValidators.validatePasswordConfirmation(state.password, state.confirmPassword)
        : null;
    final displayNameError = state.displayName.isNotEmpty
        ? AuthValidators.validateDisplayName(state.displayName)
        : null;

    state = state.copyWith(
      emailError: emailError,
      passwordError: passwordError,
      confirmPasswordError: confirmPasswordError,
      displayNameError: displayNameError,
    );
  }

  /// Clear all form data
  void clearForm() {
    state = const AuthFormState();
  }

  /// Clear only errors
  void clearErrors() {
    state = state.copyWith(
      emailError: null,
      passwordError: null,
      confirmPasswordError: null,
      displayNameError: null,
      generalError: null,
    );
  }

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword() async {
    try {
      state = state.copyWith(isLoading: true, generalError: null);

      // Validate fields
      validateAllFields();
      if (!state.isValid) {
        state = state.copyWith(isLoading: false);
        return false;
      }

      AppLogger.info('AuthProvider: Starting email/password sign-in');
      final authService = await _getAuthService();
      await authService.signInWithEmailAndPassword(state.email, state.password);

      AppLogger.info('AuthProvider: Email/password sign-in successful');
      state = state.copyWith(isLoading: false);
      return true;

    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthProvider: Email/password sign-in failed: ${e.code}');
      state = state.copyWith(
        isLoading: false,
        generalError: AuthHelpers.formatAuthError(e),
      );
      return false;
    } catch (e) {
      AppLogger.error('AuthProvider: Unexpected error during sign-in: $e');
      state = state.copyWith(
        isLoading: false,
        generalError: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      );
      return false;
    }
  }

  /// Sign up with email and password
  Future<bool> signUpWithEmailAndPassword() async {
    try {
      state = state.copyWith(isLoading: true, generalError: null);

      // Validate fields including confirm password
      validateAllFields();
      if (!state.isValid || state.confirmPasswordError != null) {
        state = state.copyWith(isLoading: false);
        return false;
      }

      AppLogger.info('AuthProvider: Starting email/password sign-up');
      final authService = await _getAuthService();
      await authService.signUpWithEmailAndPassword(state.email, state.password);

      AppLogger.info('AuthProvider: Email/password sign-up successful');
      state = state.copyWith(isLoading: false);
      return true;

    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthProvider: Email/password sign-up failed: ${e.code}');
      state = state.copyWith(
        isLoading: false,
        generalError: AuthHelpers.formatAuthError(e),
      );
      return false;
    } catch (e) {
      AppLogger.error('AuthProvider: Unexpected error during sign-up: $e');
      state = state.copyWith(
        isLoading: false,
        generalError: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      );
      return false;
    }
  }

  /// Reset password
  Future<bool> resetPassword() async {
    try {
      state = state.copyWith(isLoading: true, generalError: null);

      // Validate email only
      final emailError = AuthValidators.validateEmail(state.email);
      if (emailError != null) {
        state = state.copyWith(
          isLoading: false,
          emailError: emailError,
        );
        return false;
      }

      AppLogger.info('AuthProvider: Sending password reset email');
      final authService = await _getAuthService();
      await authService.resetPassword(state.email);

      AppLogger.info('AuthProvider: Password reset email sent successfully');
      state = state.copyWith(isLoading: false);
      return true;

    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthProvider: Password reset failed: ${e.code}');
      state = state.copyWith(
        isLoading: false,
        generalError: AuthHelpers.formatAuthError(e),
      );
      return false;
    } catch (e) {
      AppLogger.error('AuthProvider: Unexpected error during password reset: $e');
      state = state.copyWith(
        isLoading: false,
        generalError: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      );
      return false;
    }
  }

  /// Sign in with Google
  Future<bool> signInWithGoogle([String? currentPath]) async {
    try {
      state = state.copyWith(isLoading: true, generalError: null);

      AppLogger.info('AuthProvider: Starting Google Sign-In from path: ${currentPath ?? 'unknown'}');

      // Save the path where authentication was initiated
      if (currentPath != null) {
        final authNavProvider = ref.read(authNavigationProviderProvider.notifier);
        await authNavProvider.saveAuthInitiatedPath(currentPath);
      }

      final authService = await _getAuthService();
      final userCredential = await authService.signInWithGoogle();

      if (userCredential?.user != null) {
        AppLogger.info('AuthProvider: Google Sign-In successful: ${userCredential!.user!.uid}');
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        AppLogger.warning('AuthProvider: Google Sign-In returned null user');
        state = state.copyWith(
          isLoading: false,
          generalError: 'فشل تسجيل الدخول، يرجى المحاولة مرة أخرى',
        );
        return false;
      }

    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthProvider: Google Sign-In failed: ${e.code}');
      state = state.copyWith(
        isLoading: false,
        generalError: AuthHelpers.formatAuthError(e),
      );
      return false;
    } catch (e) {
      AppLogger.error('AuthProvider: Unexpected error during Google Sign-In: $e');

      // Handle user cancellation gracefully
      if (e.toString().contains('sign_in_canceled') ||
          e.toString().contains('popup_closed_by_user') ||
          e.toString().contains('cancelled')) {
        AppLogger.info('AuthProvider: Google Sign-In cancelled by user');
        state = state.copyWith(isLoading: false, generalError: null);
        return false;
      }

      state = state.copyWith(
        isLoading: false,
        generalError: 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      );
      return false;
    }
  }

  /// Get password strength
  int getPasswordStrength() {
    return AuthValidators.getPasswordStrength(state.password);
  }

  /// Get password strength description
  String getPasswordStrengthDescription() {
    final strength = getPasswordStrength();
    return AuthValidators.getPasswordStrengthDescription(strength);
  }

  /// Get password feedback
  List<String> getPasswordFeedback() {
    return AuthValidators.getPasswordFeedback(state.password);
  }

  /// Check if email is from common provider
  bool isCommonEmailProvider() {
    return AuthValidators.isCommonEmailProvider(state.email);
  }
}

/// Provider for navigation after authentication
@riverpod
class AuthNavigationProvider extends _$AuthNavigationProvider {
  @override
  String? build() {
    return null; // No saved navigation path initially
  }

  /// Save the path where authentication was initiated
  Future<void> saveAuthInitiatedPath(String path) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      // Save the auth initiated path to local storage
      await localStorageService.setString('auth_initiated_path', path);
      state = path;
      AppLogger.info('AuthNavigationProvider: Saved auth initiated path: $path');
    } catch (e) {
      AppLogger.warning('AuthNavigationProvider: Failed to save auth path: $e');
    }
  }

  /// Get and clear the saved path
  Future<String?> getAndClearSavedPath() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final savedPath = localStorageService.getString('auth_initiated_path');

      if (savedPath != null) {
        // Clear from storage
        await localStorageService.remove('auth_initiated_path');
      }

      // Also clear from state
      state = null;
      AppLogger.info('AuthNavigationProvider: Retrieved and cleared saved path: $savedPath');
      return savedPath;
    } catch (e) {
      AppLogger.warning('AuthNavigationProvider: Failed to get saved path: $e');
      return null;
    }
  }

  /// Clear saved path
  Future<void> clearSavedPath() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.remove('auth_initiated_path');
      state = null;
      AppLogger.info('AuthNavigationProvider: Cleared saved path');
    } catch (e) {
      AppLogger.warning('AuthNavigationProvider: Failed to clear saved path: $e');
    }
  }
}
