import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';

/// App logo and branding widget for authentication pages
/// Pure UI component that displays the app logo, name, and welcome message
class AppLogoWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final String? description;
  final double logoSize;
  final double spacing;

  const AppLogoWidget({
    super.key,
    this.title,
    this.subtitle,
    this.description,
    this.logoSize = 120,
    this.spacing = 32,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Column(
      children: [
        // App Logo
        Container(
          width: logoSize,
          height: logoSize,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Icon(
            Icons.restaurant_menu,
            size: logoSize * 0.5,
            color: Colors.white,
          ),
        ),
        
        SizedBox(height: spacing),
        
        // App Name
        Text(
          title ?? l10n.appName,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 16),
        
        // Welcome Message
        Text(
          subtitle ?? 'مرحباً بك في تطبيق التغذية الذكي',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        
        if (description != null) ...[
          const SizedBox(height: 8),
          Text(
            description!,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// Animated version of the app logo widget
class AnimatedAppLogoWidget extends StatelessWidget {
  final Animation<double> fadeAnimation;
  final String? title;
  final String? subtitle;
  final String? description;
  final double logoSize;
  final double spacing;

  const AnimatedAppLogoWidget({
    super.key,
    required this.fadeAnimation,
    this.title,
    this.subtitle,
    this.description,
    this.logoSize = 120,
    this.spacing = 32,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: fadeAnimation,
      child: AppLogoWidget(
        title: title,
        subtitle: subtitle,
        description: description,
        logoSize: logoSize,
        spacing: spacing,
      ),
    );
  }
}

/// Compact version of the app logo for smaller spaces
class CompactAppLogoWidget extends StatelessWidget {
  final String? title;
  final double logoSize;
  final bool showTitle;

  const CompactAppLogoWidget({
    super.key,
    this.title,
    this.logoSize = 60,
    this.showTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Compact Logo
        Container(
          width: logoSize,
          height: logoSize,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            Icons.restaurant_menu,
            size: logoSize * 0.5,
            color: Colors.white,
          ),
        ),
        
        if (showTitle) ...[
          const SizedBox(width: 12),
          Text(
            title ?? l10n.appName,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ],
    );
  }
}

/// Logo-only widget for minimal spaces
class LogoOnlyWidget extends StatelessWidget {
  final double size;
  final Color? backgroundColor;
  final Color? iconColor;
  final double borderRadius;

  const LogoOnlyWidget({
    super.key,
    this.size = 48,
    this.backgroundColor,
    this.iconColor,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primary,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? AppColors.primary).withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Icon(
        Icons.restaurant_menu,
        size: size * 0.5,
        color: iconColor ?? Colors.white,
      ),
    );
  }
}
