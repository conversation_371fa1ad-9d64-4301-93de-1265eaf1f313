import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

import '../../../../core/theme/app_colors.dart';

/// Terms and privacy widget for authentication pages
/// Pure UI component that displays terms and privacy policy text with optional links
class TermsWidget extends StatelessWidget {
  final String? text;
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;
  final TextAlign textAlign;
  final TextStyle? textStyle;
  final bool showLinks;

  const TermsWidget({
    super.key,
    this.text,
    this.onTermsPressed,
    this.onPrivacyPressed,
    this.textAlign = TextAlign.center,
    this.textStyle,
    this.showLinks = false,
  });

  @override
  Widget build(BuildContext context) {
    final defaultText = text ?? 'بالمتابعة، أنت توافق على شروط الاستخدام وسياسة الخصوصية';
    final defaultStyle = textStyle ?? 
        Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.textSecondary,
        );

    if (!showLinks) {
      return Text(
        defaultText,
        style: defaultStyle,
        textAlign: textAlign,
      );
    }

    return RichText(
      textAlign: textAlign,
      text: TextSpan(
        style: defaultStyle,
        children: [
          const TextSpan(text: 'بالمتابعة، أنت توافق على '),
          TextSpan(
            text: 'شروط الاستخدام',
            style: defaultStyle?.copyWith(
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
            recognizer: TapGestureRecognizer()..onTap = onTermsPressed,
          ),
          const TextSpan(text: ' و'),
          TextSpan(
            text: 'سياسة الخصوصية',
            style: defaultStyle?.copyWith(
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
            recognizer: TapGestureRecognizer()..onTap = onPrivacyPressed,
          ),
        ],
      ),
    );
  }
}

/// Animated terms widget
class AnimatedTermsWidget extends StatelessWidget {
  final Animation<double> fadeAnimation;
  final String? text;
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;
  final TextAlign textAlign;
  final TextStyle? textStyle;
  final bool showLinks;

  const AnimatedTermsWidget({
    super.key,
    required this.fadeAnimation,
    this.text,
    this.onTermsPressed,
    this.onPrivacyPressed,
    this.textAlign = TextAlign.center,
    this.textStyle,
    this.showLinks = false,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: fadeAnimation,
      child: TermsWidget(
        text: text,
        onTermsPressed: onTermsPressed,
        onPrivacyPressed: onPrivacyPressed,
        textAlign: textAlign,
        textStyle: textStyle,
        showLinks: showLinks,
      ),
    );
  }
}

/// Compact terms widget for smaller spaces
class CompactTermsWidget extends StatelessWidget {
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;
  final bool showLinks;

  const CompactTermsWidget({
    super.key,
    this.onTermsPressed,
    this.onPrivacyPressed,
    this.showLinks = false,
  });

  @override
  Widget build(BuildContext context) {
    return TermsWidget(
      text: 'بالمتابعة، توافق على الشروط والخصوصية',
      onTermsPressed: onTermsPressed,
      onPrivacyPressed: onPrivacyPressed,
      textStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: AppColors.textSecondary,
        fontSize: 12,
      ),
      showLinks: showLinks,
    );
  }
}

/// Terms checkbox widget for explicit consent
class TermsCheckboxWidget extends StatelessWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;
  final VoidCallback? onTermsPressed;
  final VoidCallback? onPrivacyPressed;
  final String? text;
  final bool isRequired;

  const TermsCheckboxWidget({
    super.key,
    required this.value,
    required this.onChanged,
    this.onTermsPressed,
    this.onPrivacyPressed,
    this.text,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: value,
          onChanged: onChanged,
          activeColor: AppColors.primary,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: GestureDetector(
            onTap: () => onChanged(!value),
            child: RichText(
              text: TextSpan(
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
                children: [
                  TextSpan(
                    text: isRequired ? 'أوافق على ' : 'أوافق على ',
                  ),
                  TextSpan(
                    text: 'شروط الاستخدام',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.primary,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = onTermsPressed,
                  ),
                  const TextSpan(text: ' و'),
                  TextSpan(
                    text: 'سياسة الخصوصية',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.primary,
                      decoration: TextDecoration.underline,
                    ),
                    recognizer: TapGestureRecognizer()..onTap = onPrivacyPressed,
                  ),
                  if (isRequired) const TextSpan(text: ' *'),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Privacy notice widget for data collection
class PrivacyNoticeWidget extends StatelessWidget {
  final String? title;
  final String? description;
  final VoidCallback? onPrivacyPressed;
  final IconData icon;
  final Color? backgroundColor;

  const PrivacyNoticeWidget({
    super.key,
    this.title,
    this.description,
    this.onPrivacyPressed,
    this.icon = Icons.privacy_tip_outlined,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null) ...[
                  Text(
                    title!,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                RichText(
                  text: TextSpan(
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    children: [
                      TextSpan(
                        text: description ?? 
                            'نحن نحترم خصوصيتك ونحمي بياناتك الشخصية. اقرأ ',
                      ),
                      TextSpan(
                        text: 'سياسة الخصوصية',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.primary,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()..onTap = onPrivacyPressed,
                      ),
                      const TextSpan(text: ' لمعرفة المزيد.'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
