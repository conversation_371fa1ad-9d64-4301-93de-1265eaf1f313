import 'package:flutter/material.dart';

import 'auth_button_widget.dart';

/// Authentication options widget
/// Pure UI component that displays all available authentication methods
class AuthOptionsWidget extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onEmailPressed;
  final VoidCallback? onAnonymousPressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final bool isLoading;
  final bool showGoogle;
  final bool showEmail;
  final bool showAnonymous;
  final bool showFacebook;
  final bool showApple;
  final bool showDivider;
  final String? dividerText;
  final double spacing;
  final EdgeInsets padding;

  const AuthOptionsWidget({
    super.key,
    this.onGooglePressed,
    this.onEmailPressed,
    this.onAnonymousPressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.isLoading = false,
    this.showGoogle = true,
    this.showEmail = false,
    this.showAnonymous = true,
    this.showFacebook = false,
    this.showApple = false,
    this.showDivider = true,
    this.dividerText,
    this.spacing = 24,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    // Add social auth buttons
    if (showGoogle || showFacebook || showApple) {
      final socialButtons = <Widget>[];

      if (showGoogle) {
        socialButtons.add(
          AuthButtonWidget(
            type: AuthButtonType.google,
            onPressed: onGooglePressed,
            isLoading: isLoading,
            isEnabled: !isLoading,
          ),
        );
      }

      if (showFacebook) {
        if (socialButtons.isNotEmpty) {
          socialButtons.add(SizedBox(height: spacing / 2));
        }
        socialButtons.add(
          AuthButtonWidget(
            type: AuthButtonType.facebook,
            onPressed: onFacebookPressed,
            isLoading: isLoading,
            isEnabled: !isLoading,
          ),
        );
      }

      if (showApple) {
        if (socialButtons.isNotEmpty) {
          socialButtons.add(SizedBox(height: spacing / 2));
        }
        socialButtons.add(
          AuthButtonWidget(
            type: AuthButtonType.apple,
            onPressed: onApplePressed,
            isLoading: isLoading,
            isEnabled: !isLoading,
          ),
        );
      }

      buttons.addAll(socialButtons);
    }

    // Add email button
    if (showEmail) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(height: spacing));
      }
      buttons.add(
        AuthButtonWidget(
          type: AuthButtonType.email,
          onPressed: onEmailPressed,
          isLoading: isLoading,
          isEnabled: !isLoading,
        ),
      );
    }

    // Add divider if needed
    if (showDivider && showAnonymous && buttons.isNotEmpty) {
      buttons.add(SizedBox(height: spacing));
      buttons.add(
        AuthDividerWidget(
          text: dividerText ?? 'أو',
        ),
      );
      buttons.add(SizedBox(height: spacing));
    }

    // Add anonymous/skip button
    if (showAnonymous) {
      if (buttons.isNotEmpty && !showDivider) {
        buttons.add(SizedBox(height: spacing));
      }
      buttons.add(
        TextButton(
          onPressed: isLoading ? null : onAnonymousPressed,
          child: Text(
            'تخطي وإنشاء ملف شخصي',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: buttons,
      ),
    );
  }
}

/// Animated auth options widget
class AnimatedAuthOptionsWidget extends StatelessWidget {
  final Animation<Offset> slideAnimation;
  final Animation<double> fadeAnimation;
  final VoidCallback? onGooglePressed;
  final VoidCallback? onEmailPressed;
  final VoidCallback? onAnonymousPressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final bool isLoading;
  final bool showGoogle;
  final bool showEmail;
  final bool showAnonymous;
  final bool showFacebook;
  final bool showApple;
  final bool showDivider;
  final String? dividerText;
  final double spacing;
  final EdgeInsets padding;

  const AnimatedAuthOptionsWidget({
    super.key,
    required this.slideAnimation,
    required this.fadeAnimation,
    this.onGooglePressed,
    this.onEmailPressed,
    this.onAnonymousPressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.isLoading = false,
    this.showGoogle = true,
    this.showEmail = false,
    this.showAnonymous = true,
    this.showFacebook = false,
    this.showApple = false,
    this.showDivider = true,
    this.dividerText,
    this.spacing = 24,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: AuthOptionsWidget(
          onGooglePressed: onGooglePressed,
          onEmailPressed: onEmailPressed,
          onAnonymousPressed: onAnonymousPressed,
          onFacebookPressed: onFacebookPressed,
          onApplePressed: onApplePressed,
          isLoading: isLoading,
          showGoogle: showGoogle,
          showEmail: showEmail,
          showAnonymous: showAnonymous,
          showFacebook: showFacebook,
          showApple: showApple,
          showDivider: showDivider,
          dividerText: dividerText,
          spacing: spacing,
          padding: padding,
        ),
      ),
    );
  }
}

/// Compact auth options for smaller spaces
class CompactAuthOptionsWidget extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onEmailPressed;
  final bool isLoading;
  final double spacing;

  const CompactAuthOptionsWidget({
    super.key,
    this.onGooglePressed,
    this.onEmailPressed,
    this.isLoading = false,
    this.spacing = 12,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (onGooglePressed != null)
          AuthButtonWidget(
            type: AuthButtonType.google,
            onPressed: onGooglePressed,
            isLoading: isLoading,
            isEnabled: !isLoading,
            height: 48,
          ),
        
        if (onGooglePressed != null && onEmailPressed != null)
          SizedBox(height: spacing),
        
        if (onEmailPressed != null)
          AuthButtonWidget(
            type: AuthButtonType.email,
            onPressed: onEmailPressed,
            isLoading: isLoading,
            isEnabled: !isLoading,
            height: 48,
          ),
      ],
    );
  }
}

/// Auth options grid for multiple providers
class AuthOptionsGridWidget extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final VoidCallback? onEmailPressed;
  final bool isLoading;
  final int crossAxisCount;
  final double spacing;
  final double childAspectRatio;

  const AuthOptionsGridWidget({
    super.key,
    this.onGooglePressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.onEmailPressed,
    this.isLoading = false,
    this.crossAxisCount = 2,
    this.spacing = 12,
    this.childAspectRatio = 3,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    if (onGooglePressed != null) {
      buttons.add(
        AuthButtonWidget(
          type: AuthButtonType.google,
          onPressed: onGooglePressed,
          isLoading: isLoading,
          isEnabled: !isLoading,
          height: 48,
        ),
      );
    }

    if (onFacebookPressed != null) {
      buttons.add(
        AuthButtonWidget(
          type: AuthButtonType.facebook,
          onPressed: onFacebookPressed,
          isLoading: isLoading,
          isEnabled: !isLoading,
          height: 48,
        ),
      );
    }

    if (onApplePressed != null) {
      buttons.add(
        AuthButtonWidget(
          type: AuthButtonType.apple,
          onPressed: onApplePressed,
          isLoading: isLoading,
          isEnabled: !isLoading,
          height: 48,
        ),
      );
    }

    if (onEmailPressed != null) {
      buttons.add(
        AuthButtonWidget(
          type: AuthButtonType.email,
          onPressed: onEmailPressed,
          isLoading: isLoading,
          isEnabled: !isLoading,
          height: 48,
        ),
      );
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: spacing,
      mainAxisSpacing: spacing,
      childAspectRatio: childAspectRatio,
      children: buttons,
    );
  }
}
