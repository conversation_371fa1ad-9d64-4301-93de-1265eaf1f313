import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';

/// Authentication button types
enum AuthButtonType {
  google,
  email,
  anonymous,
  facebook,
  apple,
  custom,
}

/// Specialized authentication button widget
/// Pure UI component for authentication actions with predefined styles
class AuthButtonWidget extends StatelessWidget {
  final AuthButtonType type;
  final String? text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final IconData? customIcon;
  final Color? customBackgroundColor;
  final Color? customTextColor;
  final double height;
  final double borderRadius;

  const AuthButtonWidget({
    super.key,
    required this.type,
    this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.customIcon,
    this.customBackgroundColor,
    this.customTextColor,
    this.height = 56,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getButtonConfig(context);

    return CustomButton(
      text: text ?? config.text,
      onPressed: isEnabled ? onPressed : null,
      icon: Icon(
        customIcon ?? config.icon,
        color: customTextColor ?? config.textColor,
      ),
      backgroundColor: customBackgroundColor ?? config.backgroundColor,
      textColor: customTextColor ?? config.textColor,
      isOutlined: config.isOutlined,
      isLoading: isLoading,
      height: height,
      borderRadius: borderRadius,
    );
  }

  _AuthButtonConfig _getButtonConfig(BuildContext context) {
    switch (type) {
      case AuthButtonType.google:
        return _AuthButtonConfig(
          text: 'تسجيل الدخول بجوجل',
          icon: Icons.g_mobiledata,
          backgroundColor: Colors.white,
          textColor: AppColors.textPrimary,
          isOutlined: true,
        );
      
      case AuthButtonType.email:
        return _AuthButtonConfig(
          text: 'تسجيل الدخول بالبريد الإلكتروني',
          icon: Icons.email_outlined,
          backgroundColor: AppColors.primary,
          textColor: Colors.white,
          isOutlined: false,
        );
      
      case AuthButtonType.anonymous:
        return _AuthButtonConfig(
          text: 'تخطي وإنشاء ملف شخصي',
          icon: Icons.person_outline,
          backgroundColor: Colors.transparent,
          textColor: AppColors.primary,
          isOutlined: true,
        );
      
      case AuthButtonType.facebook:
        return _AuthButtonConfig(
          text: 'تسجيل الدخول بفيسبوك',
          icon: Icons.facebook,
          backgroundColor: const Color(0xFF1877F2),
          textColor: Colors.white,
          isOutlined: false,
        );
      
      case AuthButtonType.apple:
        return _AuthButtonConfig(
          text: 'تسجيل الدخول بآبل',
          icon: Icons.apple,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          isOutlined: false,
        );
      
      case AuthButtonType.custom:
        return _AuthButtonConfig(
          text: 'تسجيل الدخول',
          icon: Icons.login,
          backgroundColor: AppColors.primary,
          textColor: Colors.white,
          isOutlined: false,
        );
    }
  }
}

/// Configuration class for auth button styling
class _AuthButtonConfig {
  final String text;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;
  final bool isOutlined;

  const _AuthButtonConfig({
    required this.text,
    required this.icon,
    required this.backgroundColor,
    required this.textColor,
    required this.isOutlined,
  });
}

/// Auth button group widget for multiple authentication options
class AuthButtonGroupWidget extends StatelessWidget {
  final List<AuthButtonWidget> buttons;
  final double spacing;
  final EdgeInsets padding;

  const AuthButtonGroupWidget({
    super.key,
    required this.buttons,
    this.spacing = 16,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: buttons
            .expand((button) => [button, SizedBox(height: spacing)])
            .take(buttons.length * 2 - 1) // Remove last spacer
            .toList(),
      ),
    );
  }
}

/// Animated auth button widget
class AnimatedAuthButtonWidget extends StatelessWidget {
  final Animation<Offset> slideAnimation;
  final Animation<double> fadeAnimation;
  final AuthButtonWidget button;

  const AnimatedAuthButtonWidget({
    super.key,
    required this.slideAnimation,
    required this.fadeAnimation,
    required this.button,
  });

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: button,
      ),
    );
  }
}

/// Social auth buttons row widget
class SocialAuthButtonsWidget extends StatelessWidget {
  final VoidCallback? onGooglePressed;
  final VoidCallback? onFacebookPressed;
  final VoidCallback? onApplePressed;
  final bool isLoading;
  final bool showGoogle;
  final bool showFacebook;
  final bool showApple;

  const SocialAuthButtonsWidget({
    super.key,
    this.onGooglePressed,
    this.onFacebookPressed,
    this.onApplePressed,
    this.isLoading = false,
    this.showGoogle = true,
    this.showFacebook = false,
    this.showApple = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttons = <Widget>[];

    if (showGoogle) {
      buttons.add(
        Expanded(
          child: AuthButtonWidget(
            type: AuthButtonType.google,
            onPressed: onGooglePressed,
            isLoading: isLoading,
            height: 48,
          ),
        ),
      );
    }

    if (showFacebook) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: AuthButtonWidget(
            type: AuthButtonType.facebook,
            onPressed: onFacebookPressed,
            isLoading: isLoading,
            height: 48,
          ),
        ),
      );
    }

    if (showApple) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));
      buttons.add(
        Expanded(
          child: AuthButtonWidget(
            type: AuthButtonType.apple,
            onPressed: onApplePressed,
            isLoading: isLoading,
            height: 48,
          ),
        ),
      );
    }

    return Row(children: buttons);
  }
}

/// Auth divider widget with "or" text
class AuthDividerWidget extends StatelessWidget {
  final String text;
  final Color? color;
  final double thickness;

  const AuthDividerWidget({
    super.key,
    this.text = 'أو',
    this.color,
    this.thickness = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: color ?? AppColors.textSecondary.withOpacity(0.3),
            thickness: thickness,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color ?? AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: color ?? AppColors.textSecondary.withOpacity(0.3),
            thickness: thickness,
          ),
        ),
      ],
    );
  }
}
