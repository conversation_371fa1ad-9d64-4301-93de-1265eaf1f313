import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../core/utils/logger.dart';

part 'auth_service.g.dart';

/// Auth service for handling authentication operations
/// This is a feature-specific service that wraps Firebase Auth operations
/// and provides auth-specific business logic
class AuthService {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final FirestoreService _firestoreService;
  final LocalStorageService _storageService;

  /// Constructor for AuthService
  AuthService({
    required FirebaseAuth firebaseAuth,
    required GoogleSignIn googleSignIn,
    required FirestoreService firestoreService,
    required LocalStorageService storageService,
  })  : _firebaseAuth = firebaseAuth,
        _googleSignIn = googleSignIn,
        _firestoreService = firestoreService,
        _storageService = storageService;

  /// Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      AppLogger.info('AuthService: Starting Google Sign-In');

      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        AppLogger.info('AuthService: Google Sign-In cancelled by user');
        return null;
      }

      AppLogger.info('AuthService: Google user obtained: ${googleUser.email}');
      final googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      AppLogger.info('AuthService: Signing in with Firebase credential');
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        AppLogger.info('AuthService: Firebase sign-in successful: ${userCredential.user!.uid}');
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Firebase auth error: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      AppLogger.warning('AuthService: Unexpected error during Google Sign-In: $e');
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword(String email, String password) async {
    try {
      AppLogger.info('AuthService: Starting email/password sign-in for: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      AppLogger.info('AuthService: Email/password sign-in successful: ${credential.user?.uid}');
      return credential;
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Email/password sign-in error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Sign up with email and password
  Future<UserCredential> signUpWithEmailAndPassword(String email, String password) async {
    try {
      AppLogger.info('AuthService: Starting email/password sign-up for: $email');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      AppLogger.info('AuthService: Email/password sign-up successful: ${credential.user?.uid}');
      return credential;
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Email/password sign-up error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Sign in anonymously (guest mode)
  Future<UserCredential> signInAnonymously() async {
    try {
      AppLogger.info('AuthService: Starting anonymous sign-in');
      
      final credential = await _firebaseAuth.signInAnonymously();

      AppLogger.info('AuthService: Anonymous sign-in successful: ${credential.user?.uid}');
      return credential;
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Anonymous sign-in error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      AppLogger.info('AuthService: Starting sign out');
      
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);

      AppLogger.info('AuthService: Sign out successful');
    } catch (e) {
      AppLogger.warning('AuthService: Sign out error: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      AppLogger.info('AuthService: Sending password reset email to: $email');
      
      await _firebaseAuth.sendPasswordResetEmail(email: email);

      AppLogger.info('AuthService: Password reset email sent successfully');
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Password reset error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Get current user
  User? getCurrentUser() {
    return _firebaseAuth.currentUser;
  }

  /// Get auth state changes stream
  Stream<User?> getAuthStateChanges() {
    return _firebaseAuth.authStateChanges();
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _firebaseAuth.currentUser != null;

  /// Check if current user is anonymous
  bool get isAnonymous => _firebaseAuth.currentUser?.isAnonymous ?? false;

  /// Get user-friendly error message from Firebase Auth error code in Arabic
  String getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'يوجد حساب مسجل بهذا البريد الإلكتروني بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'محاولات كثيرة جداً، يرجى المحاولة لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'network-request-failed':
        return 'خطأ في الشبكة، يرجى التحقق من الاتصال';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بهذا البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'invalid-verification-code':
        return 'رمز التحقق غير صحيح';
      case 'invalid-verification-id':
        return 'معرف التحقق غير صحيح';
      case 'session-expired':
        return 'انتهت صلاحية الجلسة، يرجى المحاولة مرة أخرى';
      default:
        return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  }

  /// Load user preferences from Firestore after authentication
  Future<void> loadUserPreferencesFromFirestore(String userId) async {
    try {
      AppLogger.info('AuthService: Loading user preferences from Firestore for: $userId');

      // Load plan preferences using the correct method
      final planPreferences = await _firestoreService.getPlanPreferences(userId);

      // Load notification settings using the correct method
      final notificationSettings = await _firestoreService.getNotificationSettings(userId);

      // Save preferences to local storage using the correct method
      if (planPreferences != null) {
        await _storageService.storeSecureObject('plan_preferences', planPreferences);
        AppLogger.info('AuthService: Plan preferences loaded and cached');
      }

      if (notificationSettings != null) {
        await _storageService.storeSecureObject('notification_settings', notificationSettings);
        AppLogger.info('AuthService: Notification settings loaded and cached');
      }

      AppLogger.info('AuthService: User preferences loading completed');
    } catch (e) {
      AppLogger.warning('AuthService: Failed to load user preferences from Firestore: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }

  /// Check onboarding completion status from Firestore
  Future<bool> checkOnboardingCompletion(String userId) async {
    try {
      AppLogger.info('AuthService: Checking onboarding completion for: $userId');

      // Use a direct Firestore query to check onboarding completion
      // since getUserProfile returns a UserProfile object, not raw data
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists && userDoc.data() != null) {
        final userData = userDoc.data()!;
        final onboardingCompleted = userData['onboarding_completed'] as bool? ?? false;

        AppLogger.info('AuthService: Onboarding completion status: $onboardingCompleted');
        return onboardingCompleted;
      }

      AppLogger.info('AuthService: User document not found, onboarding not completed');
      return false;
    } catch (e) {
      AppLogger.warning('AuthService: Failed to check onboarding completion: $e');
      return false;
    }
  }

  /// Execute complete data loading sequence after authentication
  Future<void> executeCompleteDataLoading(String userId) async {
    try {
      AppLogger.info('AuthService: Starting complete data loading for: $userId');

      // Load user preferences
      await loadUserPreferencesFromFirestore(userId);

      // Load other user data as needed
      // This can be extended to load meal plans, water intake, etc.

      AppLogger.info('AuthService: Complete data loading finished');
    } catch (e) {
      AppLogger.warning('AuthService: Complete data loading failed: $e');
      rethrow;
    }
  }

  /// Send email verification to current user
  Future<void> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      if (user.emailVerified) {
        AppLogger.info('AuthService: Email already verified');
        return;
      }

      AppLogger.info('AuthService: Sending email verification');
      await user.sendEmailVerification();
      AppLogger.info('AuthService: Email verification sent successfully');
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Email verification error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Reload current user to get updated information
  Future<void> reloadUser() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      AppLogger.info('AuthService: Reloading user information');
      await user.reload();
      AppLogger.info('AuthService: User information reloaded successfully');
    } catch (e) {
      AppLogger.warning('AuthService: Failed to reload user: $e');
      rethrow;
    }
  }

  /// Update user display name
  Future<void> updateDisplayName(String displayName) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      AppLogger.info('AuthService: Updating display name to: $displayName');
      await user.updateDisplayName(displayName);
      await user.reload();
      AppLogger.info('AuthService: Display name updated successfully');
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Display name update error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Update user photo URL
  Future<void> updatePhotoURL(String photoURL) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      AppLogger.info('AuthService: Updating photo URL');
      await user.updatePhotoURL(photoURL);
      await user.reload();
      AppLogger.info('AuthService: Photo URL updated successfully');
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Photo URL update error: ${e.code} - ${e.message}');
      rethrow;
    }
  }

  /// Delete current user account
  Future<void> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      AppLogger.info('AuthService: Deleting user account: ${user.uid}');

      // Delete user data from Firestore first
      try {
        await _firestoreService.deleteUserProfile(user.uid);
        AppLogger.info('AuthService: User data deleted from Firestore');
      } catch (e) {
        AppLogger.warning('AuthService: Failed to delete user data from Firestore: $e');
        // Continue with account deletion even if Firestore deletion fails
      }

      // Delete the Firebase Auth account
      await user.delete();
      AppLogger.info('AuthService: User account deleted successfully');
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthService: Account deletion error: ${e.code} - ${e.message}');
      rethrow;
    }
  }
}

/// Provider for AuthService
@riverpod
Future<AuthService> authService(Ref ref) async {
  final storageService = await ref.read(localStorageServiceProvider.future);
  return AuthService(
    firebaseAuth: FirebaseAuth.instance,
    googleSignIn: GoogleSignIn(),
    firestoreService: ref.read(firestoreServiceProvider),
    storageService: storageService,
  );
}
