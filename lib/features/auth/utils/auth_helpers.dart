import 'package:firebase_auth/firebase_auth.dart';

/// Authentication helper utilities
/// Contains helper functions for auth-related operations
class AuthHelpers {
  /// Get user display name with fallback
  static String getUserDisplayName(User? user) {
    if (user == null) return 'مستخدم';
    
    // Try display name first
    if (user.displayName != null && user.displayName!.isNotEmpty) {
      return user.displayName!;
    }
    
    // Fallback to email username
    if (user.email != null && user.email!.isNotEmpty) {
      return user.email!.split('@').first;
    }
    
    // Final fallback
    return 'مستخدم';
  }

  /// Get user avatar URL with fallback
  static String? getUserAvatarUrl(User? user) {
    if (user == null) return null;
    
    // Return photo URL if available
    if (user.photoURL != null && user.photoURL!.isNotEmpty) {
      return user.photoURL;
    }
    
    return null;
  }

  /// Check if user has completed email verification
  static bool isEmailVerified(User? user) {
    return user?.emailVerified ?? false;
  }

  /// Check if user is anonymous
  static bool isAnonymousUser(User? user) {
    return user?.isAnonymous ?? false;
  }

  /// Get user provider information
  static List<String> getUserProviders(User? user) {
    if (user == null) return [];
    
    return user.providerData.map((info) => info.providerId).toList();
  }

  /// Check if user signed in with Google
  static bool isGoogleUser(User? user) {
    return getUserProviders(user).contains('google.com');
  }

  /// Check if user signed in with email/password
  static bool isEmailPasswordUser(User? user) {
    return getUserProviders(user).contains('password');
  }

  /// Format authentication error for display
  static String formatAuthError(FirebaseAuthException error) {
    switch (error.code) {
      case 'user-not-found':
        return 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'يوجد حساب مسجل بهذا البريد الإلكتروني بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'محاولات كثيرة جداً، يرجى المحاولة لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'network-request-failed':
        return 'خطأ في الشبكة، يرجى التحقق من الاتصال';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بهذا البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'invalid-verification-code':
        return 'رمز التحقق غير صحيح';
      case 'invalid-verification-id':
        return 'معرف التحقق غير صحيح';
      case 'session-expired':
        return 'انتهت صلاحية الجلسة، يرجى المحاولة مرة أخرى';
      default:
        return 'حدث خطأ غير متوقع: ${error.message ?? error.code}';
    }
  }

  /// Generate initials from display name
  static String getInitials(String? displayName) {
    if (displayName == null || displayName.isEmpty) {
      return 'م'; // Default Arabic initial for "مستخدم"
    }

    final words = displayName.trim().split(' ');
    if (words.isEmpty) return 'م';

    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    }

    // Take first letter of first and last word
    return '${words.first.substring(0, 1)}${words.last.substring(0, 1)}'.toUpperCase();
  }

  /// Check if user needs to complete profile
  static bool needsProfileCompletion(User? user) {
    if (user == null) return true;
    
    // Anonymous users always need profile completion
    if (user.isAnonymous) return true;
    
    // Check if basic profile info is missing
    return user.displayName == null || user.displayName!.isEmpty;
  }

  /// Get authentication method display name
  static String getAuthMethodDisplayName(String providerId) {
    switch (providerId) {
      case 'google.com':
        return 'جوجل';
      case 'password':
        return 'البريد الإلكتروني';
      case 'anonymous':
        return 'زائر';
      case 'facebook.com':
        return 'فيسبوك';
      case 'twitter.com':
        return 'تويتر';
      case 'apple.com':
        return 'آبل';
      default:
        return providerId;
    }
  }

  /// Check if password reset is available for user
  static bool canResetPassword(User? user) {
    if (user == null) return false;
    
    // Password reset is only available for email/password users
    return isEmailPasswordUser(user) && user.email != null;
  }

  /// Check if email verification is needed
  static bool needsEmailVerification(User? user) {
    if (user == null) return false;
    
    // Only email/password users need email verification
    return isEmailPasswordUser(user) && !isEmailVerified(user);
  }

  /// Get user creation date formatted
  static String? getFormattedCreationDate(User? user) {
    if (user?.metadata.creationTime == null) return null;
    
    final creationDate = user!.metadata.creationTime!;
    return '${creationDate.day}/${creationDate.month}/${creationDate.year}';
  }

  /// Get last sign in date formatted
  static String? getFormattedLastSignIn(User? user) {
    if (user?.metadata.lastSignInTime == null) return null;
    
    final lastSignIn = user!.metadata.lastSignInTime!;
    return '${lastSignIn.day}/${lastSignIn.month}/${lastSignIn.year}';
  }

  /// Check if user account is recently created (within 24 hours)
  static bool isRecentlyCreated(User? user) {
    if (user?.metadata.creationTime == null) return false;
    
    final creationTime = user!.metadata.creationTime!;
    final now = DateTime.now();
    final difference = now.difference(creationTime);
    
    return difference.inHours < 24;
  }

  /// Generate a secure random password
  static String generateSecurePassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = DateTime.now().millisecondsSinceEpoch;
    
    String password = '';
    for (int i = 0; i < length; i++) {
      password += chars[(random + i) % chars.length];
    }
    
    return password;
  }

  /// Mask email for display (e.g., "u***@gmail.com")
  static String maskEmail(String email) {
    if (email.isEmpty) return email;
    
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) {
      return '${username[0]}***@$domain';
    }
    
    return '${username[0]}${'*' * (username.length - 2)}${username[username.length - 1]}@$domain';
  }

  /// Check if user can link additional providers
  static bool canLinkProviders(User? user) {
    if (user == null || user.isAnonymous) return false;
    
    // Users can link additional providers if they have less than 3
    return user.providerData.length < 3;
  }

  /// Get available providers for linking
  static List<String> getAvailableProvidersForLinking(User? user) {
    if (user == null) return [];
    
    final currentProviders = getUserProviders(user);
    const allProviders = ['google.com', 'password'];
    
    return allProviders.where((provider) => !currentProviders.contains(provider)).toList();
  }

  /// Format user info for debugging
  static Map<String, dynamic> getUserDebugInfo(User? user) {
    if (user == null) return {'status': 'not_authenticated'};
    
    return {
      'uid': user.uid,
      'email': user.email,
      'displayName': user.displayName,
      'photoURL': user.photoURL,
      'emailVerified': user.emailVerified,
      'isAnonymous': user.isAnonymous,
      'providers': getUserProviders(user),
      'creationTime': user.metadata.creationTime?.toIso8601String(),
      'lastSignInTime': user.metadata.lastSignInTime?.toIso8601String(),
    };
  }
}
