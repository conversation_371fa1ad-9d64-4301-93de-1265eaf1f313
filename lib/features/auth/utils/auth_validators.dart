/// Authentication validation utilities
/// Contains validators for email, password, and other auth-related inputs
class AuthValidators {
  /// Email validation regex pattern
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  /// Password validation regex patterns
  static final RegExp _hasUppercase = RegExp(r'[A-Z]');
  static final RegExp _hasLowercase = RegExp(r'[a-z]');
  static final RegExp _hasDigits = RegExp(r'[0-9]');
  static final RegExp _hasSpecialCharacters = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  /// Validate email address
  /// Returns null if valid, error message if invalid
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }

    if (!_emailRegex.hasMatch(email)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }

    return null;
  }

  /// Validate password
  /// Returns null if valid, error message if invalid
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }

    if (password.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    if (password.length > 128) {
      return 'كلمة المرور طويلة جداً';
    }

    return null;
  }

  /// Validate strong password with specific requirements
  /// Returns null if valid, error message if invalid
  static String? validateStrongPassword(String? password) {
    final basicValidation = validatePassword(password);
    if (basicValidation != null) {
      return basicValidation;
    }

    if (!_hasUppercase.hasMatch(password!)) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }

    if (!_hasLowercase.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }

    if (!_hasDigits.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }

    if (!_hasSpecialCharacters.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
    }

    return null;
  }

  /// Validate password confirmation
  /// Returns null if valid, error message if invalid
  static String? validatePasswordConfirmation(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }

    if (password != confirmPassword) {
      return 'كلمات المرور غير متطابقة';
    }

    return null;
  }

  /// Check password strength
  /// Returns a score from 0 (very weak) to 5 (very strong)
  static int getPasswordStrength(String password) {
    if (password.isEmpty) return 0;

    int score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // Character variety checks
    if (_hasUppercase.hasMatch(password)) score++;
    if (_hasLowercase.hasMatch(password)) score++;
    if (_hasDigits.hasMatch(password)) score++;
    if (_hasSpecialCharacters.hasMatch(password)) score++;

    // Cap at 5
    return score > 5 ? 5 : score;
  }

  /// Get password strength description in Arabic
  static String getPasswordStrengthDescription(int strength) {
    switch (strength) {
      case 0:
      case 1:
        return 'ضعيفة جداً';
      case 2:
        return 'ضعيفة';
      case 3:
        return 'متوسطة';
      case 4:
        return 'قوية';
      case 5:
        return 'قوية جداً';
      default:
        return 'غير محددة';
    }
  }

  /// Validate display name
  /// Returns null if valid, error message if invalid
  static String? validateDisplayName(String? name) {
    if (name == null || name.isEmpty) {
      return 'الاسم مطلوب';
    }

    if (name.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }

    if (name.trim().length > 50) {
      return 'الاسم طويل جداً';
    }

    // Basic validation - allow most characters for names
    // More complex validation can be added later if needed

    return null;
  }

  /// Validate phone number (optional, basic validation)
  /// Returns null if valid or empty, error message if invalid
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) {
      return null; // Phone is optional
    }

    // Remove spaces and common separators
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check if it's all digits and has reasonable length
    if (!RegExp(r'^\+?[0-9]{8,15}$').hasMatch(cleanPhone)) {
      return 'يرجى إدخال رقم هاتف صحيح';
    }

    return null;
  }

  /// Check if email is from a common provider (for UI hints)
  static bool isCommonEmailProvider(String email) {
    final domain = email.split('@').last.toLowerCase();
    const commonProviders = [
      'gmail.com',
      'yahoo.com',
      'hotmail.com',
      'outlook.com',
      'icloud.com',
      'live.com',
      'msn.com',
    ];
    return commonProviders.contains(domain);
  }

  /// Sanitize email input (trim and lowercase)
  static String sanitizeEmail(String email) {
    return email.trim().toLowerCase();
  }

  /// Sanitize display name input (trim and title case)
  static String sanitizeDisplayName(String name) {
    return name.trim();
  }

  /// Check if password contains common patterns to avoid
  static bool hasCommonPatterns(String password) {
    final lowerPassword = password.toLowerCase();
    
    // Common patterns to avoid
    const commonPatterns = [
      '123456',
      'password',
      'qwerty',
      'abc123',
      '111111',
      '000000',
      'admin',
      'user',
      'guest',
    ];

    for (final pattern in commonPatterns) {
      if (lowerPassword.contains(pattern)) {
        return true;
      }
    }

    return false;
  }

  /// Generate password strength feedback
  static List<String> getPasswordFeedback(String password) {
    final feedback = <String>[];

    if (password.length < 8) {
      feedback.add('استخدم 8 أحرف على الأقل');
    }

    if (!_hasUppercase.hasMatch(password)) {
      feedback.add('أضف حرف كبير واحد على الأقل');
    }

    if (!_hasLowercase.hasMatch(password)) {
      feedback.add('أضف حرف صغير واحد على الأقل');
    }

    if (!_hasDigits.hasMatch(password)) {
      feedback.add('أضف رقم واحد على الأقل');
    }

    if (!_hasSpecialCharacters.hasMatch(password)) {
      feedback.add('أضف رمز خاص واحد على الأقل');
    }

    if (hasCommonPatterns(password)) {
      feedback.add('تجنب الكلمات والأرقام الشائعة');
    }

    return feedback;
  }
}
