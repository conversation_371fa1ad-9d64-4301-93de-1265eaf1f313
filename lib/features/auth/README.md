# Auth Feature

The authentication feature handles user authentication, registration, and related UI components following the feature-first architecture pattern.

## Structure

```
lib/features/auth/
├── data/
│   └── services/
│       └── auth_service.dart           # Authentication operations service
├── presentation/
│   ├── pages/
│   │   ├── splash_page.dart           # App initialization screen
│   │   └── welcome_auth_page.dart     # Main authentication page
│   ├── providers/
│   │   └── auth_provider.dart         # Auth form state management
│   └── widgets/
│       ├── app_logo_widget.dart       # App branding components
│       ├── auth_button_widget.dart    # Authentication buttons
│       ├── auth_options_widget.dart   # Authentication options layout
│       └── terms_widget.dart          # Terms and privacy components
├── utils/
│   ├── auth_helpers.dart              # Authentication helper functions
│   └── auth_validators.dart           # Input validation utilities
└── README.md                          # This file
```

## Components

### Data Layer

#### AuthService (`data/services/auth_service.dart`)
Feature-specific service that wraps Firebase Auth operations:
- Google Sign-In
- Email/password authentication
- Anonymous sign-in
- Password reset
- User preferences loading
- Onboarding completion checking

**Usage:**
```dart
final authService = ref.read(authServiceProvider.notifier);
final userCredential = await authService.signInWithGoogle();
```

### Presentation Layer

#### Pages

**SplashPage** - App initialization screen with loading animation
**AuthPage** - Main authentication page with multiple sign-in options

#### Providers

**AuthProvider** - Manages authentication form state and validation:
- Form field management (email, password, etc.)
- Real-time validation
- Authentication operations
- Error handling

**AuthNavigationProvider** - Handles navigation after authentication

#### Widgets

**AppLogoWidget** - Reusable app branding component:
- Standard logo with title and subtitle
- Animated version with fade transitions
- Compact version for smaller spaces
- Logo-only version for minimal spaces

**AuthButtonWidget** - Specialized authentication buttons:
- Predefined styles for different providers (Google, Email, etc.)
- Loading states and disabled states
- Customizable appearance

**AuthOptionsWidget** - Layout for multiple authentication options:
- Flexible configuration of visible options
- Animated version with slide/fade transitions
- Compact and grid layouts

**TermsWidget** - Terms and privacy policy components:
- Simple text version
- Interactive version with clickable links
- Checkbox version for explicit consent
- Privacy notice widget

### Utils Layer

#### AuthValidators (`utils/auth_validators.dart`)
Input validation utilities:
- Email validation with regex
- Password strength checking (5-level system)
- Password confirmation validation
- Display name validation
- Phone number validation (optional)
- Password feedback generation

**Usage:**
```dart
final emailError = AuthValidators.validateEmail(email);
final passwordStrength = AuthValidators.getPasswordStrength(password);
```

#### AuthHelpers (`utils/auth_helpers.dart`)
Authentication helper functions:
- User display name extraction with fallbacks
- Avatar URL handling
- Provider information checking
- Error message formatting
- User metadata formatting

**Usage:**
```dart
final displayName = AuthHelpers.getUserDisplayName(user);
final isGoogleUser = AuthHelpers.isGoogleUser(user);
```

## Usage Examples

### Basic Authentication Form

```dart
class LoginForm extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProviderProvider);
    final authNotifier = ref.read(authProviderProvider.notifier);

    return Column(
      children: [
        TextFormField(
          onChanged: authNotifier.updateEmail,
          decoration: InputDecoration(
            errorText: authState.emailError,
          ),
        ),
        TextFormField(
          onChanged: authNotifier.updatePassword,
          decoration: InputDecoration(
            errorText: authState.passwordError,
          ),
        ),
        AuthButtonWidget(
          type: AuthButtonType.email,
          onPressed: authState.isValid 
            ? () => authNotifier.signInWithEmailAndPassword()
            : null,
          isLoading: authState.isLoading,
        ),
      ],
    );
  }
}
```

### Authentication Options

```dart
AuthOptionsWidget(
  onGooglePressed: () => _handleGoogleLogin(),
  onEmailPressed: () => _navigateToEmailLogin(),
  onAnonymousPressed: () => _handleSkip(),
  isLoading: _isLoading,
  showGoogle: true,
  showEmail: true,
  showAnonymous: true,
)
```

### App Logo with Animation

```dart
AnimatedAppLogoWidget(
  fadeAnimation: _fadeAnimation,
  title: 'EasyDietAI',
  subtitle: 'مرحباً بك في تطبيق التغذية الذكي',
  description: 'اختر طريقة الدخول للمتابعة',
)
```

## Integration with Global State

The auth feature integrates with the global app state through:

1. **AppStateProvider** - Global authentication state management
2. **AuthService** - Feature-specific operations that update global state
3. **Navigation** - Router integration for auth-based navigation

## Best Practices

1. **Separation of Concerns**: UI components are pure and don't contain business logic
2. **Validation**: All inputs are validated using the AuthValidators utility
3. **Error Handling**: Consistent error formatting using AuthHelpers
4. **Loading States**: All async operations show appropriate loading indicators
5. **Accessibility**: All components support screen readers and keyboard navigation

## Testing

The feature is designed for easy testing:
- Pure UI components can be tested in isolation
- Business logic is separated into providers and services
- Validation utilities have comprehensive test coverage
- Mock services can be easily injected for testing

## Future Enhancements

- Biometric authentication support
- Multi-factor authentication
- Social login providers (Facebook, Apple)
- Email verification flow
- Password strength meter UI component
