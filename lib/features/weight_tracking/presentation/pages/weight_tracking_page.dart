import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../providers/weight_tracking_provider.dart';
import '../../data/models/weight_tracking_models.dart';
import '../widgets/weight_entry_form.dart';
import '../widgets/weight_progress_chart.dart';

class WeightTrackingPage extends ConsumerStatefulWidget {
  const WeightTrackingPage({super.key});

  @override
  ConsumerState<WeightTrackingPage> createState() => _WeightTrackingPageState();
}

class _WeightTrackingPageState extends ConsumerState<WeightTrackingPage> {
  @override
  void initState() {
    super.initState();
    // Automatically reload weight tracking data when page is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _reloadWeightTrackingData();
    });
  }

  /// Reload weight tracking data from local storage
  Future<void> _reloadWeightTrackingData() async {
    try {
      // Trigger data reload through the provider
      // This will seamlessly refresh the data without interfering with existing auto-loading
      await ref.read(weightTrackingNotifierProvider.notifier).loadWeightTrackingData();
    } catch (e) {
      // Handle any errors silently - the page will show appropriate empty states
      // if no data exists or if there are any issues loading the data
      debugPrint('Weight tracking data reload error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final weightTrackingState = ref.watch(weightTrackingNotifierProvider);
    final weightData = ref.watch(weightTrackingDataProvider);
    final canAddNew = ref.watch(canAddNewWeightEntryProvider);
    final daysUntilNext = ref.watch(daysUntilNextWeightEntryProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('تتبع الوزن'),
        backgroundColor: Colors.purple.shade50,
        foregroundColor: Colors.purple.shade800,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(),
          ),
        ],
      ),
      backgroundColor: Colors.purple.shade50,
      body: weightTrackingState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current Status Card
                  _buildCurrentStatusCard(weightData, canAddNew, daysUntilNext),
                  
                  const SizedBox(height: 20),
                  
                  // Add Weight Button/Form
                  _buildAddWeightSection(canAddNew, daysUntilNext),
                  
                  const SizedBox(height: 20),
                  
                  // Progress Chart
                  if (weightData.entries.length >= 2) ...[
                    _buildProgressChartCard(weightData),
                    const SizedBox(height: 20),
                  ],
                  
                  // Historical Entries
                  _buildHistoricalEntriesCard(weightData),
                ],
              ),
            ),
      floatingActionButton: canAddNew
          ? FloatingActionButton(
              onPressed: () => _showAddWeightDialog(),
              backgroundColor: Colors.purple.shade600,
              child: Icon(Icons.add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildCurrentStatusCard(WeightTrackingData weightData, bool canAddNew, int daysUntilNext) {
    final today = DateTime.now();
    final arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');
    final latestEntry = weightData.latestEntry;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade400, Colors.purple.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.monitor_weight,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تتبع الوزن الأسبوعي',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      arabicDateFormat.format(today),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Current Weight Info
          if (latestEntry != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.scale,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'آخر وزن مسجل',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        latestEntry.formattedWeight,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: Colors.white70,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('d MMMM yyyy', 'ar').format(latestEntry.date),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      const Spacer(),
                      if (weightData.totalWeightChange != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getWeightChangeColor(weightData.totalWeightChangeType),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            weightData.totalWeightChangeText,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'لم يتم تسجيل أي وزن بعد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddWeightSection(bool canAddNew, int daysUntilNext) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إضافة وزن جديد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade800,
            ),
          ),
          
          const SizedBox(height: 16),
          
          if (canAddNew) ...[
            Text(
              'يمكنك الآن إضافة وزن جديد',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.green.shade600,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showAddWeightDialog(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                icon: Icon(Icons.add),
                label: Text('إضافة وزن'),
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color: Colors.orange.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'يمكنك إضافة وزن جديد بعد $daysUntilNext ${daysUntilNext == 1 ? 'يوم' : 'أيام'}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يُسمح بتسجيل الوزن مرة واحدة كل 7 أيام للحصول على قياسات دقيقة',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.orange.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressChartCard(WeightTrackingData weightData) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط التقدم',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade800,
            ),
          ),

          const SizedBox(height: 16),

          // Chart
          SizedBox(
            height: 200,
            child: WeightProgressChart(weightData: weightData),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoricalEntriesCard(WeightTrackingData weightData) {
    final sortedEntries = weightData.sortedEntries;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'السجل التاريخي',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade800,
                ),
              ),
              const Spacer(),
              Text(
                '${sortedEntries.length} قياس',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.purple.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (sortedEntries.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.monitor_weight_outlined,
                    size: 48,
                    color: Colors.purple.shade300,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'لم يتم تسجيل أي وزن بعد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.purple.shade400,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ابدأ بإضافة أول قياس للوزن!',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.purple.shade300,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sortedEntries.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final entry = sortedEntries[index];
                final previousEntry = index < sortedEntries.length - 1
                    ? sortedEntries[index + 1]
                    : null;
                return _buildWeightEntryItem(entry, previousEntry);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildWeightEntryItem(WeightEntry entry, WeightEntry? previousEntry) {
    final dateFormat = DateFormat('d MMM yyyy', 'ar');
    final changeType = entry.getWeightChangeType(previousEntry);
    final changeText = entry.getWeightChangeText(previousEntry);

    // Check if this entry is the oldest one (should not be deletable)
    final weightData = ref.watch(weightTrackingDataProvider);
    final isOldestEntry = weightData.isOldestEntry(entry.id);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.shade100),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.purple.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.monitor_weight,
              color: Colors.purple.shade600,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      entry.formattedWeight,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade800,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (changeText.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getWeightChangeColor(changeType),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          changeText,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                Text(
                  dateFormat.format(entry.date),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.purple.shade600,
                  ),
                ),
                if (entry.notes != null && entry.notes!.isNotEmpty)
                  Text(
                    entry.notes!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.purple.shade500,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),

          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                _showEditWeightDialog(entry);
              } else if (value == 'delete') {
                _showDeleteConfirmationDialog(entry);
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    const SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              // Only show delete option if this is NOT the oldest entry
              if (!isOldestEntry)
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      const SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
            ],
            child: Icon(
              Icons.more_vert,
              color: Colors.purple.shade400,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Color _getWeightChangeColor(WeightChangeType changeType) {
    switch (changeType) {
      case WeightChangeType.loss:
        return Colors.green.shade600;
      case WeightChangeType.gain:
        return Colors.red.shade600;
      case WeightChangeType.maintenance:
        return Colors.blue.shade600;
      case WeightChangeType.none:
        return Colors.grey.shade600;
    }
  }

  void _showAddWeightDialog() {
    showDialog(
      context: context,
      builder: (context) => WeightEntryForm(
        onSave: (weight, date, notes) async {
          await ref.read(weightTrackingNotifierProvider.notifier).addWeightEntry(
            weight: weight,
            date: date,
            notes: notes,
          );
          if (mounted) Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showEditWeightDialog(WeightEntry entry) {
    showDialog(
      context: context,
      builder: (context) => WeightEntryForm(
        initialWeight: entry.weight,
        initialDate: entry.date,
        initialNotes: entry.notes,
        isEditing: true,
        onSave: (weight, date, notes) async {
          await ref.read(weightTrackingNotifierProvider.notifier).editWeightEntry(
            entryId: entry.id,
            weight: weight,
            date: date,
            notes: notes,
          );
          if (mounted) Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showDeleteConfirmationDialog(WeightEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف القياس'),
        content: Text('هل تريد حذف قياس الوزن ${entry.formattedWeight} المسجل في ${DateFormat('d MMMM yyyy', 'ar').format(entry.date)}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(weightTrackingNotifierProvider.notifier).deleteWeightEntry(entry.id);
              if (mounted) Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
            ),
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    // TODO: Implement settings dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إعدادات تتبع الوزن - قريباً'),
        backgroundColor: Colors.purple.shade600,
      ),
    );
  }
}
