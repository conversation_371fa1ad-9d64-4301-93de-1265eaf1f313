import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../data/models/weight_tracking_models.dart';

class WeightProgressChart extends StatelessWidget {
  final WeightTrackingData weightData;

  const WeightProgressChart({
    super.key,
    required this.weightData,
  });

  @override
  Widget build(BuildContext context) {
    final entries = weightData.chronologicalEntries;
    
    if (entries.length < 2) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              size: 48,
              color: Colors.purple.shade300,
            ),
            const SizedBox(height: 8),
            Text(
              'يحتاج المخطط إلى قياسين على الأقل',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.purple.shade400,
              ),
            ),
          ],
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: _calculateHorizontalInterval(entries),
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.purple.shade100,
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.purple.shade100,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                return _buildBottomTitle(value, meta, entries);
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: _calculateHorizontalInterval(entries),
              reservedSize: 42,
              getTitlesWidget: (value, meta) {
                return _buildLeftTitle(value, meta);
              },
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(
            color: Colors.purple.shade200,
            width: 1,
          ),
        ),
        minX: 0,
        maxX: (entries.length - 1).toDouble(),
        minY: _getMinY(entries),
        maxY: _getMaxY(entries),
        lineBarsData: [
          LineChartBarData(
            spots: _createSpots(entries),
            isCurved: true,
            gradient: LinearGradient(
              colors: _getLineColors(entries),
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: Colors.white,
                  strokeWidth: 2,
                  strokeColor: _getSpotColor(entries, index),
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: _getLineColors(entries).map((color) => color.withOpacity(0.1)).toList(),
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colors.purple.shade800,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final entry = entries[barSpot.x.toInt()];
                final dateFormat = DateFormat('d MMM', 'ar');

                return LineTooltipItem(
                  '${entry.formattedWeight}\n${dateFormat.format(entry.date)}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  List<FlSpot> _createSpots(List<WeightEntry> entries) {
    return entries.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.weight);
    }).toList();
  }

  double _getMinY(List<WeightEntry> entries) {
    final weights = entries.map((e) => e.weight).toList();
    final minWeight = weights.reduce((a, b) => a < b ? a : b);
    return (minWeight - 2).floorToDouble(); // Add some padding
  }

  double _getMaxY(List<WeightEntry> entries) {
    final weights = entries.map((e) => e.weight).toList();
    final maxWeight = weights.reduce((a, b) => a > b ? a : b);
    return (maxWeight + 2).ceilToDouble(); // Add some padding
  }

  double _calculateHorizontalInterval(List<WeightEntry> entries) {
    final weights = entries.map((e) => e.weight).toList();
    final minWeight = weights.reduce((a, b) => a < b ? a : b);
    final maxWeight = weights.reduce((a, b) => a > b ? a : b);
    final range = maxWeight - minWeight;
    
    if (range <= 5) return 1;
    if (range <= 10) return 2;
    if (range <= 20) return 5;
    return 10;
  }

  List<Color> _getLineColors(List<WeightEntry> entries) {
    if (entries.length < 2) return [Colors.purple.shade600];
    
    final firstWeight = entries.first.weight;
    final lastWeight = entries.last.weight;
    
    if (lastWeight < firstWeight) {
      // Weight loss - green gradient
      return [Colors.green.shade400, Colors.green.shade600];
    } else if (lastWeight > firstWeight) {
      // Weight gain - red gradient
      return [Colors.red.shade400, Colors.red.shade600];
    } else {
      // Maintenance - blue gradient
      return [Colors.blue.shade400, Colors.blue.shade600];
    }
  }

  Color _getSpotColor(List<WeightEntry> entries, int index) {
    if (index == 0) return Colors.purple.shade600;
    
    final currentWeight = entries[index].weight;
    final previousWeight = entries[index - 1].weight;
    
    if (currentWeight < previousWeight) {
      return Colors.green.shade600; // Weight loss
    } else if (currentWeight > previousWeight) {
      return Colors.red.shade600; // Weight gain
    } else {
      return Colors.blue.shade600; // Maintenance
    }
  }

  Widget _buildBottomTitle(double value, TitleMeta meta, List<WeightEntry> entries) {
    final index = value.toInt();
    if (index < 0 || index >= entries.length) {
      return const SizedBox.shrink();
    }

    final entry = entries[index];
    final dateFormat = DateFormat('d/M', 'ar');

    return Text(
      dateFormat.format(entry.date),
      style: TextStyle(
        color: Colors.purple.shade600,
        fontWeight: FontWeight.w500,
        fontSize: 10,
      ),
    );
  }

  Widget _buildLeftTitle(double value, TitleMeta meta) {
    return Text(
      '${value.toInt()}',
      style: TextStyle(
        color: Colors.purple.shade600,
        fontWeight: FontWeight.w500,
        fontSize: 10,
      ),
    );
  }
}
