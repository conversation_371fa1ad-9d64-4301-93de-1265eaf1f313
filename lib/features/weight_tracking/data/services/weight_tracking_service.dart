import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/user_profile/data/services/user_data_service.dart';
import 'package:easydietai/features/weight_tracking/data/models/weight_tracking_models.dart';
import 'package:easydietai/features/weight_tracking/data/services/weight_tracking_sync_service.dart';

/// Provider for weight tracking service
final weightTrackingServiceProvider = FutureProvider<WeightTrackingService>((ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  final userDataService = ref.watch(userDataServiceProvider);
  final syncService = await ref.watch(weightTrackingSyncServiceProvider.future);

  return FirebaseWeightTrackingService(
    localStorageService,
    firestoreService,
    userDataService,
    syncService,
  );
});

/// Result class for weight tracking operations
class WeightTrackingOperationResult {
  const WeightTrackingOperationResult({
    required this.success,
    this.data,
    this.error,
    this.warnings = const [],
  });

  final bool success;
  final dynamic data;
  final String? error;
  final List<String> warnings;
}

/// Abstract weight tracking service interface
abstract class WeightTrackingService {
  // Data loading operations
  Future<WeightTrackingOperationResult> loadWeightTrackingData();
  Future<WeightTrackingOperationResult> refreshWeightTrackingData();
  
  // Weight entry operations
  Future<WeightTrackingOperationResult> addWeightEntry({
    required double weight,
    required DateTime date,
    String? notes,
  });
  
  Future<WeightTrackingOperationResult> updateWeightEntry({
    required String entryId,
    required double weight,
    required DateTime date,
    String? notes,
  });
  
  Future<WeightTrackingOperationResult> deleteWeightEntry(String entryId);
  
  // Validation operations
  WeightTrackingOperationResult validateWeightEntry({
    required double weight,
    required DateTime date,
    required List<WeightEntry> existingEntries,
    bool isEditing = false,
    String? editingEntryId,
  });
  
  // Utility operations
  String generateWeightEntryId();
  Future<void> updatePlanPreferencesWeightIfLatest(WeightEntry entry, List<WeightEntry> allEntries);
  
  // Sync operations
  Future<void> syncWeightEntryToFirestore(WeightEntry entry);
  Future<void> saveWeightTrackingDataToLocalStorage(WeightTrackingData data);
}

/// Firebase implementation of weight tracking service
class FirebaseWeightTrackingService implements WeightTrackingService {
  FirebaseWeightTrackingService(
    this._localStorageService,
    this._firestoreService,
    this._userDataService,
    this._syncService,
  );

  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;
  final UserDataService _userDataService;
  final WeightTrackingSyncService _syncService;

  @override
  Future<WeightTrackingOperationResult> loadWeightTrackingData() async {
    try {
      AppLogger.info('WeightTrackingService: Loading weight tracking data from local storage');

      // Load weight data from local storage
      final weightData = _localStorageService.loadWeightTrackingData();
      
      WeightTrackingData data;
      if (weightData != null) {
        data = WeightTrackingData.fromJson(weightData);
      } else {
        data = const WeightTrackingData();
      }
      
      // Update computed properties
      final latestEntry = data.latestEntry;
      data = data.copyWith(
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: data.chronologicalEntries.isNotEmpty 
            ? data.chronologicalEntries.first.weight 
            : null,
      );

      return WeightTrackingOperationResult(success: true, data: data);
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error loading weight tracking data: $e');
      return WeightTrackingOperationResult(
        success: false,
        error: 'فشل في تحميل بيانات الوزن: $e',
      );
    }
  }

  @override
  Future<WeightTrackingOperationResult> refreshWeightTrackingData() async {
    try {
      AppLogger.info('WeightTrackingService: Refreshing weight tracking data from Firestore');

      // Sync from Firestore first
      await _syncService.syncWeightTrackingDataFromFirestore();
      
      // Then load from local storage
      return await loadWeightTrackingData();
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error refreshing weight tracking data: $e');
      return WeightTrackingOperationResult(
        success: false,
        error: 'فشل في تحديث بيانات الوزن: $e',
      );
    }
  }

  @override
  Future<WeightTrackingOperationResult> addWeightEntry({
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    try {
      AppLogger.info('WeightTrackingService: Adding weight entry: ${weight}kg on ${date.toIso8601String()}');

      // Load current data first
      final loadResult = await loadWeightTrackingData();
      if (!loadResult.success) {
        return loadResult;
      }

      final currentData = loadResult.data as WeightTrackingData;

      // Validate the entry
      final validation = validateWeightEntry(
        weight: weight,
        date: date,
        existingEntries: currentData.entries,
        isEditing: false,
      );

      if (!validation.success) {
        return validation;
      }

      // Generate Firestore auto-generated ID
      final entryId = generateWeightEntryId();

      // Create new entry
      final newEntry = WeightEntry(
        id: entryId,
        weight: weight,
        date: date,
        createdAt: DateTime.now(),
        notes: notes,
      );

      // Add to existing entries
      final updatedEntries = [...currentData.entries, newEntry];
      
      // Update data with computed properties
      final updatedData = currentData.copyWith(
        entries: updatedEntries,
        lastEntryDate: date,
        currentWeight: weight,
        startingWeight: currentData.startingWeight ?? weight, // Set starting weight if first entry
      );

      // Save to local storage
      await saveWeightTrackingDataToLocalStorage(updatedData);

      // Update plan preferences if this is the latest entry
      await updatePlanPreferencesWeightIfLatest(newEntry, updatedEntries);

      // Sync to Firestore in background
      syncWeightEntryToFirestore(newEntry);

      return WeightTrackingOperationResult(
        success: true,
        data: updatedData,
        warnings: validation.warnings,
      );
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error adding weight entry: $e');
      return WeightTrackingOperationResult(
        success: false,
        error: 'فشل في إضافة قياس الوزن: $e',
      );
    }
  }

  @override
  Future<WeightTrackingOperationResult> updateWeightEntry({
    required String entryId,
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    try {
      AppLogger.info('WeightTrackingService: Updating weight entry: $entryId');

      // Load current data first
      final loadResult = await loadWeightTrackingData();
      if (!loadResult.success) {
        return loadResult;
      }

      final currentData = loadResult.data as WeightTrackingData;

      // Find the entry to update
      final entryIndex = currentData.entries.indexWhere((entry) => entry.id == entryId);
      if (entryIndex == -1) {
        return const WeightTrackingOperationResult(
          success: false,
          error: 'لم يتم العثور على قياس الوزن المحدد',
        );
      }

      // Validate the updated entry
      final validation = validateWeightEntry(
        weight: weight,
        date: date,
        existingEntries: currentData.entries,
        isEditing: true,
        editingEntryId: entryId,
      );

      if (!validation.success) {
        return validation;
      }

      // Create updated entry
      final updatedEntry = currentData.entries[entryIndex].copyWith(
        weight: weight,
        date: date,
        notes: notes,
        updatedAt: DateTime.now(),
      );

      // Update entries list
      final updatedEntries = [...currentData.entries];
      updatedEntries[entryIndex] = updatedEntry;
      
      // Update data with computed properties
      final latestEntry = WeightTrackingData(entries: updatedEntries).latestEntry;
      final updatedData = currentData.copyWith(
        entries: updatedEntries,
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: WeightTrackingData(entries: updatedEntries).chronologicalEntries.isNotEmpty 
            ? WeightTrackingData(entries: updatedEntries).chronologicalEntries.first.weight 
            : null,
      );

      // Save to local storage
      await saveWeightTrackingDataToLocalStorage(updatedData);

      // Update plan preferences if this is the latest entry
      await updatePlanPreferencesWeightIfLatest(updatedEntry, updatedEntries);

      // Sync to Firestore in background
      syncWeightEntryToFirestore(updatedEntry);

      return WeightTrackingOperationResult(
        success: true,
        data: updatedData,
        warnings: validation.warnings,
      );
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error updating weight entry: $e');
      return WeightTrackingOperationResult(
        success: false,
        error: 'فشل في تحديث قياس الوزن: $e',
      );
    }
  }

  @override
  Future<WeightTrackingOperationResult> deleteWeightEntry(String entryId) async {
    try {
      AppLogger.info('WeightTrackingService: Deleting weight entry: $entryId');

      // Load current data first
      final loadResult = await loadWeightTrackingData();
      if (!loadResult.success) {
        return loadResult;
      }

      final currentData = loadResult.data as WeightTrackingData;

      // Check if entry exists
      if (!currentData.entries.any((entry) => entry.id == entryId)) {
        throw Exception('Entry not found');
      }

      // Remove the entry
      final updatedEntries = currentData.entries.where((entry) => entry.id != entryId).toList();

      // Update data with computed properties
      final latestEntry = WeightTrackingData(entries: updatedEntries).latestEntry;
      final updatedData = currentData.copyWith(
        entries: updatedEntries,
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: WeightTrackingData(entries: updatedEntries).chronologicalEntries.isNotEmpty
            ? WeightTrackingData(entries: updatedEntries).chronologicalEntries.first.weight
            : null,
      );

      // Save to local storage
      await saveWeightTrackingDataToLocalStorage(updatedData);

      // Delete from Firestore in background
      Future.microtask(() async {
        try {
          await _firestoreService.deleteFromUserSubcollection(
            subcollectionName: 'weight_entries',
            documentId: entryId,
          );
          AppLogger.info('WeightTrackingService: Weight entry deleted from Firestore: $entryId');
        } catch (e) {
          AppLogger.warning('WeightTrackingService: Failed to delete weight entry from Firestore: $e');
        }
      });

      return WeightTrackingOperationResult(success: true, data: updatedData);
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error deleting weight entry: $e');
      return WeightTrackingOperationResult(
        success: false,
        error: 'فشل في حذف قياس الوزن: $e',
      );
    }
  }

  @override
  WeightTrackingOperationResult validateWeightEntry({
    required double weight,
    required DateTime date,
    required List<WeightEntry> existingEntries,
    bool isEditing = false,
    String? editingEntryId,
  }) {
    final validation = WeightValidator.validateWeightEntry(
      weight: weight,
      date: date,
      existingEntries: existingEntries,
      isEditing: isEditing,
      editingEntryId: editingEntryId,
    );

    return WeightTrackingOperationResult(
      success: validation.isValid,
      error: validation.isValid ? null : validation.errorMessage,
      warnings: validation.warnings,
    );
  }

  @override
  String generateWeightEntryId() {
    return _firestoreService.generateDocumentId('weight_entries');
  }

  @override
  Future<void> updatePlanPreferencesWeightIfLatest(WeightEntry entry, List<WeightEntry> allEntries) async {
    try {
      // Check if this is the latest entry by date
      final sortedEntries = [...allEntries]..sort((a, b) => b.date.compareTo(a.date));

      if (sortedEntries.isNotEmpty && sortedEntries.first.id == entry.id) {
        AppLogger.info('WeightTrackingService: This is the latest entry, updating plan preferences weight');

        // Update plan preferences with the new weight
        await _userDataService.updatePlanPreferencesField('weight', entry.weight);

        AppLogger.info('WeightTrackingService: Plan preferences weight updated to ${entry.weight}kg');
      } else {
        AppLogger.info('WeightTrackingService: This is not the latest entry, skipping plan preferences update');
      }
    } catch (e) {
      AppLogger.warning('WeightTrackingService: Failed to update plan preferences weight: $e');
      // Don't throw error as this shouldn't break the main operation
    }
  }

  @override
  Future<void> syncWeightEntryToFirestore(WeightEntry entry) async {
    // Run in background without blocking the UI
    Future.microtask(() async {
      try {
        AppLogger.info('WeightTrackingService: Syncing weight entry to Firestore: ${entry.id}');

        await _firestoreService.saveToUserSubcollection(
          subcollectionName: 'weight_entries',
          documentId: entry.id,
          data: entry.toJson(),
        );

        AppLogger.info('WeightTrackingService: Weight entry synced to Firestore successfully: ${entry.id}');
      } catch (e) {
        AppLogger.warning('WeightTrackingService: Failed to sync weight entry to Firestore: $e');
        // Don't throw error as this is a background operation
      }
    });
  }

  @override
  Future<void> saveWeightTrackingDataToLocalStorage(WeightTrackingData data) async {
    try {
      AppLogger.info('WeightTrackingService: Saving weight tracking data to local storage');

      await _localStorageService.saveWeightTrackingData(data.toJson());

      AppLogger.info('WeightTrackingService: Weight tracking data saved to local storage successfully');
    } catch (e) {
      AppLogger.error('WeightTrackingService: Error saving weight tracking data to local storage: $e');
      rethrow;
    }
  }
}
