import 'package:freezed_annotation/freezed_annotation.dart';

part 'weight_tracking_models.freezed.dart';
part 'weight_tracking_models.g.dart';

/// Model for a single weight entry
@freezed
class WeightEntry with _$WeightEntry {
  const factory WeightEntry({
    required String id,
    required double weight, // Weight in kilograms
    required DateTime date,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @Json<PERSON>ey(name: 'updated_at') DateTime? updatedAt,
    String? notes,
  }) = _WeightEntry;

  factory WeightEntry.fromJson(Map<String, dynamic> json) =>
      _$WeightEntryFromJson(json);
}

/// Extension to add computed properties to WeightEntry
extension WeightEntryExtension on WeightEntry {
  /// Format weight for display (e.g., "75.5 كجم")
  String get formattedWeight {
    return '${weight.toStringAsFixed(1)} كجم';
  }

  /// Get weight change from previous entry (if provided)
  String getWeightChangeText(WeightEntry? previousEntry) {
    if (previousEntry == null) return '';
    
    final change = weight - previousEntry.weight;
    if (change > 0) {
      return '+${change.toStringAsFixed(1)} كجم';
    } else if (change < 0) {
      return '${change.toStringAsFixed(1)} كجم';
    } else {
      return 'لا تغيير';
    }
  }

  /// Get weight change color for UI
  WeightChangeType getWeightChangeType(WeightEntry? previousEntry) {
    if (previousEntry == null) return WeightChangeType.none;
    
    final change = weight - previousEntry.weight;
    if (change > 0) {
      return WeightChangeType.gain;
    } else if (change < 0) {
      return WeightChangeType.loss;
    } else {
      return WeightChangeType.maintenance;
    }
  }

  /// Check if this entry can be edited (not restricted by 7-day rule)
  bool canBeEdited() {
    // All existing entries can be edited regardless of date
    return true;
  }
}

/// Enum for weight change types
enum WeightChangeType {
  gain,
  loss,
  maintenance,
  none,
}

/// Model for weight tracking state
@freezed
class WeightTrackingData with _$WeightTrackingData {
  const factory WeightTrackingData({
    @Default([]) List<WeightEntry> entries,
    @JsonKey(name: 'last_entry_date') DateTime? lastEntryDate,
    @JsonKey(name: 'current_weight') double? currentWeight,
    @JsonKey(name: 'starting_weight') double? startingWeight,
  }) = _WeightTrackingData;

  factory WeightTrackingData.fromJson(Map<String, dynamic> json) =>
      _$WeightTrackingDataFromJson(json);
}

/// Extension to add computed properties to WeightTrackingData
extension WeightTrackingDataExtension on WeightTrackingData {
  /// Get the most recent weight entry
  WeightEntry? get latestEntry {
    if (entries.isEmpty) return null;
    return entries.reduce((a, b) => a.date.isAfter(b.date) ? a : b);
  }

  /// Get the oldest weight entry (earliest datetime)
  WeightEntry? get oldestEntry {
    if (entries.isEmpty) return null;
    return entries.reduce((a, b) => a.date.isBefore(b.date) ? a : b);
  }

  /// Get entries sorted by date (newest first)
  List<WeightEntry> get sortedEntries {
    final sorted = List<WeightEntry>.from(entries);
    sorted.sort((a, b) => b.date.compareTo(a.date));
    return sorted;
  }

  /// Get entries sorted by date (oldest first) for chart display
  List<WeightEntry> get chronologicalEntries {
    final sorted = List<WeightEntry>.from(entries);
    sorted.sort((a, b) => a.date.compareTo(b.date));
    return sorted;
  }

  /// Check if user can add a new weight entry (7-day rule)
  bool get canAddNewEntry {
    final latest = latestEntry;
    if (latest == null) return true;
    
    final daysSinceLastEntry = DateTime.now().difference(latest.date).inDays;
    return daysSinceLastEntry >= 7;
  }

  /// Get days remaining until next entry is allowed
  int get daysUntilNextEntry {
    final latest = latestEntry;
    if (latest == null) return 0;
    
    final daysSinceLastEntry = DateTime.now().difference(latest.date).inDays;
    final daysRemaining = 7 - daysSinceLastEntry;
    return daysRemaining > 0 ? daysRemaining : 0;
  }

  /// Get next available entry date
  DateTime? get nextAvailableEntryDate {
    final latest = latestEntry;
    if (latest == null) return DateTime.now();

    return latest.date.add(const Duration(days: 7));
  }

  /// Check if a specific entry is the latest one (by datetime)
  bool isLatestEntry(String entryId) {
    final latest = latestEntry;
    return latest?.id == entryId;
  }

  /// Check if a specific entry is the oldest one (by datetime)
  bool isOldestEntry(String entryId) {
    final oldest = oldestEntry;
    return oldest?.id == entryId;
  }

  /// Get total weight change from first to latest entry
  double? get totalWeightChange {
    if (entries.length < 2) return null;
    
    final chronological = chronologicalEntries;
    final first = chronological.first;
    final latest = chronological.last;
    
    return latest.weight - first.weight;
  }

  /// Get formatted total weight change text
  String get totalWeightChangeText {
    final change = totalWeightChange;
    if (change == null) return 'لا توجد بيانات كافية';
    
    if (change > 0) {
      return '+${change.toStringAsFixed(1)} كجم';
    } else if (change < 0) {
      return '${change.toStringAsFixed(1)} كجم';
    } else {
      return 'لا تغيير';
    }
  }

  /// Get weight change type for total change
  WeightChangeType get totalWeightChangeType {
    final change = totalWeightChange;
    if (change == null) return WeightChangeType.none;
    
    if (change > 0) {
      return WeightChangeType.gain;
    } else if (change < 0) {
      return WeightChangeType.loss;
    } else {
      return WeightChangeType.maintenance;
    }
  }


}

/// Model for weight entry validation result
@freezed
class WeightEntryValidation with _$WeightEntryValidation {
  const factory WeightEntryValidation({
    @Default(true) bool isValid,
    String? errorMessage,
    @Default([]) List<String> warnings,
  }) = _WeightEntryValidation;
}

/// Weight validation helper class
class WeightValidator {
  static const double minWeight = 20.0; // Minimum reasonable weight in kg
  static const double maxWeight = 300.0; // Maximum reasonable weight in kg
  
  /// Validate a weight entry
  static WeightEntryValidation validateWeightEntry({
    required double weight,
    required DateTime date,
    required List<WeightEntry> existingEntries,
    bool isEditing = false,
    String? editingEntryId,
  }) {
    final warnings = <String>[];
    
    // Validate weight range
    if (weight < minWeight || weight > maxWeight) {
      return const WeightEntryValidation(
        isValid: false,
        errorMessage: 'الوزن يجب أن يكون بين 20 و 300 كيلوجرام',
      );
    }
    
    // Validate date is not in future
    if (date.isAfter(DateTime.now())) {
      return const WeightEntryValidation(
        isValid: false,
        errorMessage: 'لا يمكن إدخال وزن لتاريخ مستقبلي',
      );
    }
    
    // Check 7-day rule for new entries
    if (!isEditing) {
      final recentEntries = existingEntries.where((entry) {
        final daysDifference = date.difference(entry.date).inDays.abs();
        return daysDifference < 7;
      }).toList();
      
      if (recentEntries.isNotEmpty) {
        return const WeightEntryValidation(
          isValid: false,
          errorMessage: 'يجب أن يكون هناك 7 أيام على الأقل بين القياسات',
        );
      }
    } else {
      // For editing, check conflicts with other entries (excluding the one being edited)
      final otherEntries = existingEntries.where((entry) => entry.id != editingEntryId).toList();
      final conflictingEntries = otherEntries.where((entry) {
        final daysDifference = date.difference(entry.date).inDays.abs();
        return daysDifference < 7;
      }).toList();
      
      if (conflictingEntries.isNotEmpty) {
        return const WeightEntryValidation(
          isValid: false,
          errorMessage: 'هذا التاريخ قريب جداً من قياس آخر (أقل من 7 أيام)',
        );
      }
    }
    
    // Add warnings for significant weight changes
    if (existingEntries.isNotEmpty) {
      final sortedEntries = List<WeightEntry>.from(existingEntries);
      sortedEntries.sort((a, b) => a.date.compareTo(b.date));
      
      // Find the closest previous entry
      WeightEntry? previousEntry;
      for (final entry in sortedEntries.reversed) {
        if (entry.date.isBefore(date)) {
          previousEntry = entry;
          break;
        }
      }
      
      if (previousEntry != null) {
        final weightChange = (weight - previousEntry.weight).abs();
        if (weightChange > 5.0) {
          warnings.add('تغيير كبير في الوزن (${weightChange.toStringAsFixed(1)} كجم)');
        }
      }
    }
    
    return WeightEntryValidation(
      isValid: true,
      warnings: warnings,
    );
  }
}
