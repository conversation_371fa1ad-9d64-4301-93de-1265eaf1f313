import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/weight_tracking/data/models/weight_tracking_models.dart';
import 'package:easydietai/features/weight_tracking/data/services/weight_tracking_service.dart';

part 'weight_tracking_provider.freezed.dart';
part 'weight_tracking_provider.g.dart';

@freezed
class WeightTrackingState with _$WeightTrackingState {
  const factory WeightTrackingState({
    @Default(WeightTrackingData()) WeightTrackingData data,
    @Default(false) bool isLoading,
    @Default(false) bool isSaving,
    @Default(false) bool isRefreshing,
    String? error,
    @Default([]) List<String> warnings,
  }) = _WeightTrackingState;
}

@riverpod
class WeightTrackingNotifier extends _$WeightTrackingNotifier {
  @override
  WeightTrackingState build() {
    // Load data on initialization
    loadWeightTrackingData();
    return const WeightTrackingState();
  }

  /// Load weight tracking data from local storage (data should already be synced by auth provider)
  Future<void> loadWeightTrackingData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final weightTrackingService = await ref.read(weightTrackingServiceProvider.future);
      final result = await weightTrackingService.loadWeightTrackingData();

      if (result.success) {
        final data = result.data as WeightTrackingData;
        state = state.copyWith(
          data: data,
          isLoading: false,
          warnings: result.warnings,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحميل بيانات الوزن: $e',
      );
      AppLogger.error('WeightTrackingNotifier: Error loading weight tracking data: $e');
    }
  }

  /// Refresh weight tracking data from Firestore
  Future<void> refreshWeightTrackingData() async {
    state = state.copyWith(isRefreshing: true, error: null);

    try {
      final weightTrackingService = await ref.read(weightTrackingServiceProvider.future);
      final result = await weightTrackingService.refreshWeightTrackingData();

      if (result.success) {
        final data = result.data as WeightTrackingData;
        state = state.copyWith(
          data: data,
          isRefreshing: false,
          warnings: result.warnings,
        );
      } else {
        state = state.copyWith(
          isRefreshing: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isRefreshing: false,
        error: 'فشل في تحديث بيانات الوزن: $e',
      );
      AppLogger.error('WeightTrackingNotifier: Error refreshing weight tracking data: $e');
    }
  }

  /// Add a new weight entry
  Future<void> addWeightEntry({
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      final weightTrackingService = await ref.read(weightTrackingServiceProvider.future);
      final result = await weightTrackingService.addWeightEntry(
        weight: weight,
        date: date,
        notes: notes,
      );

      if (result.success) {
        final data = result.data as WeightTrackingData;
        state = state.copyWith(
          data: data,
          isSaving: false,
          warnings: result.warnings,
        );
      } else {
        state = state.copyWith(
          isSaving: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في إضافة الوزن: $e',
      );
      AppLogger.error('WeightTrackingNotifier: Error adding weight entry: $e');
    }
  }

  /// Edit an existing weight entry
  Future<void> editWeightEntry({
    required String entryId,
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      final weightTrackingService = await ref.read(weightTrackingServiceProvider.future);
      final result = await weightTrackingService.updateWeightEntry(
        entryId: entryId,
        weight: weight,
        date: date,
        notes: notes,
      );

      if (result.success) {
        final data = result.data as WeightTrackingData;
        state = state.copyWith(
          data: data,
          isSaving: false,
          warnings: result.warnings,
        );
      } else {
        state = state.copyWith(
          isSaving: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في تعديل الوزن: $e',
      );
      AppLogger.error('WeightTrackingNotifier: Error editing weight entry: $e');
    }
  }

  /// Delete a weight entry
  Future<void> deleteWeightEntry(String entryId) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      final weightTrackingService = await ref.read(weightTrackingServiceProvider.future);
      final result = await weightTrackingService.deleteWeightEntry(entryId);

      if (result.success) {
        final data = result.data as WeightTrackingData;
        state = state.copyWith(
          data: data,
          isSaving: false,
        );
      } else {
        state = state.copyWith(
          isSaving: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في حذف الوزن: $e',
      );
      AppLogger.error('WeightTrackingNotifier: Error deleting weight entry: $e');
    }
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear warnings
  void clearWarnings() {
    state = state.copyWith(warnings: <String>[]);
  }

  /// Get weight entry by ID
  WeightEntry? getWeightEntry(String entryId) {
    try {
      return state.data.entries.firstWhere((entry) => entry.id == entryId);
    } catch (e) {
      return null;
    }
  }
}

/// Provider for current weight tracking data
@riverpod
WeightTrackingData weightTrackingData(Ref ref) {
  final weightTrackingState = ref.watch(weightTrackingNotifierProvider);
  return weightTrackingState.data;
}

/// Provider for latest weight entry
@riverpod
WeightEntry? latestWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.latestEntry;
}

/// Provider for checking if new entry can be added
@riverpod
bool canAddNewWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.canAddNewEntry;
}

/// Provider for days until next entry
@riverpod
int daysUntilNextWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.daysUntilNextEntry;
}
