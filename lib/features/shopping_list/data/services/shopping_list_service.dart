import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import 'package:easydietai/core/services/firebase_functions_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/core/utils/timestamp_utils.dart';
import '../models/shopping_list_models.dart';
import './shopping_list_sync_service.dart';

/// Provider for shopping list service
final shoppingListServiceProvider = FutureProvider<ShoppingListService>((ref) async {
  final functionsService = await ref.watch(firebaseFunctionsServiceProvider.future);
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  final syncService = ref.watch(shoppingListSyncServiceProvider);

  return FirebaseShoppingListService(
    functionsService,
    localStorageService,
    firestoreService,
    syncService,
  );
});

/// Result class for shopping list operations
class ShoppingListOperationResult {
  const ShoppingListOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// Result class for generation validation
class GenerationValidationResult {
  const GenerationValidationResult({
    required this.isValid,
    this.error,
    this.startDate,
    this.endDate,
    this.duplicateListTitle,
    this.shoppingListId,
  });

  final bool isValid;
  final String? error;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? duplicateListTitle;
  final String? shoppingListId; // ID of duplicate shopping list for replacement
}

/// Abstract shopping list service interface
abstract class ShoppingListService {
  // Core operations
  Future<Map<String, dynamic>> generateShoppingListFromMeals({
    required DateTime startDate,
    required DateTime endDate,
    String? shoppingIdToReplace,
  });

  Future<ShoppingListOperationResult> loadShoppingLists();
  Future<ShoppingListOperationResult> refreshShoppingLists();
  Future<ShoppingListOperationResult> deleteShoppingList(String shoppingListId);

  // Business logic operations
  Future<GenerationValidationResult> validateShoppingListGeneration();
  Future<ShoppingListOperationResult> executeGeneration(DateTime startDate, DateTime endDate, {String? shoppingIdToReplace});

  // Utility operations
  Map<String, DateTime>? computeDateRangeFromMealPlan(Map<String, dynamic> mealPlanData);
  Map<String, dynamic>? findDuplicateShoppingList(
    List<Map<String, dynamic>> existingLists,
    DateTime startDate,
    DateTime endDate,
  );
  DateTime? parseFirestoreTimestamp(dynamic timestamp);

  // State checking operations
  bool isGenerating();
  bool isLoadingFlagExpired();
  Future<void> setLoadingFlag();
  Future<void> removeLoadingFlag();

  // Detail operations
  Future<ShoppingListOperationResult> loadMostRecentShoppingList();
  Future<ShoppingListOperationResult> toggleItemPurchase(ShoppingList shoppingList, String itemId);
  Future<ShoppingListOperationResult> clearShoppingList(String shoppingListId);
  Future<ShoppingListOperationResult> generateShoppingListFromDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  // Sync operations
  Future<void> syncShoppingListUpdate(ShoppingList shoppingList);
  Future<void> saveShoppingListToLocalStorage(ShoppingList shoppingList);
  Future<bool> setGeneratingFlag();
  Future<bool> removeGeneratingFlag();

  // Listener operations
  Future<bool> handleFirestoreUpdate(Map<String, dynamic> newEntry);
  Future<ShoppingList?> updateShoppingListFromListener(ShoppingList shoppingList);

  // Firestore operations (feature-specific implementations)
  Future<void> saveShoppingListToFirestore(String listId, Map<String, dynamic> shoppingListData);
  Future<List<Map<String, dynamic>>> getShoppingListsFromFirestore();
  Future<void> deleteShoppingListFromFirestore(String listId);
  Stream<Map<String, dynamic>?> getShoppingListStream({DateTime? afterTimestamp});

  // Firebase Functions operations (feature-specific implementations)
  Future<Map<String, dynamic>> callGenerateShoppingListFunction({
    required DateTime startDate,
    required DateTime endDate,
    String? shoppingIdToReplace,
  });
  Future<Map<String, dynamic>> callAnalyzeShoppingListFunction({
    required List<String> items,
  });
  Future<Map<String, dynamic>> callOptimizeShoppingListFunction({
    required String shoppingListId,
    Map<String, dynamic>? preferences,
  });
}

/// Firebase implementation of shopping list service
class FirebaseShoppingListService implements ShoppingListService {
  FirebaseShoppingListService(
    this._functionsService,
    this._localStorageService,
    this._firestoreService,
    this._syncService,
  );

  final FirebaseFunctionsService _functionsService;
  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;
  final ShoppingListSyncService _syncService;

  @override
  Future<Map<String, dynamic>> generateShoppingListFromMeals({
    required DateTime startDate,
    required DateTime endDate,
    String? shoppingIdToReplace,
  }) async {
    return callGenerateShoppingListFunction(
      startDate: startDate,
      endDate: endDate,
      shoppingIdToReplace: shoppingIdToReplace,
    );
  }

  @override
  Future<ShoppingListOperationResult> loadShoppingLists() async {
    try {
      AppLogger.info('ShoppingListService: Loading shopping lists from local storage');

      // Load from local storage (data should already be synced by auth provider)
      final localShoppingListsData = _localStorageService.loadAllShoppingLists();

      // Convert local data to ShoppingList objects
      final shoppingLists = localShoppingListsData
          .map((data) {
            try {
              return ShoppingList.fromJson(data);
            } catch (e) {
              AppLogger.warning('ShoppingListService: Failed to parse shopping list: $e');
              return null;
            }
          })
          .where((list) => list != null)
          .cast<ShoppingList>()
          .toList();

      AppLogger.info('ShoppingListService: Loaded ${shoppingLists.length} shopping lists from local storage');

      return ShoppingListOperationResult(
        success: true,
        data: shoppingLists,
      );
    } catch (e) {
      AppLogger.error('ShoppingListService: Error loading shopping lists: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في تحميل قوائم التسوق: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> refreshShoppingLists() async {
    try {
      AppLogger.info('ShoppingListService: Refreshing shopping lists from Firestore');

      // Use the sync service to refresh data from Firestore
      final syncSuccess = await _syncService.syncShoppingListDataFromFirestore();

      if (syncSuccess) {
        // Reload from local storage after successful sync
        return await loadShoppingLists();
      } else {
        // If Firestore sync fails, just reload from local storage
        return await loadShoppingLists();
      }
    } catch (e) {
      AppLogger.error('ShoppingListService: Error refreshing shopping lists: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في تحديث قوائم التسوق: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> deleteShoppingList(String shoppingListId) async {
    try {
      AppLogger.info('ShoppingListService: Deleting shopping list: $shoppingListId');

      // Remove from Firestore using generic method
      await deleteShoppingListFromFirestore(shoppingListId);

      // Remove from local storage
      await _localStorageService.removeShoppingList(shoppingListId);

      AppLogger.info('ShoppingListService: Successfully deleted shopping list: $shoppingListId');

      return const ShoppingListOperationResult(success: true);
    } catch (e) {
      AppLogger.error('ShoppingListService: Error deleting shopping list: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في حذف قائمة التسوق: $e',
      );
    }
  }

  @override
  Future<GenerationValidationResult> validateShoppingListGeneration() async {
    try {
      AppLogger.info('ShoppingListService: Starting shopping list generation validation');

      // Pre-check 1: Verify meal plan data exists
      final mealPlanData = _localStorageService.loadCurrentMealPlan();

      if (mealPlanData == null || mealPlanData.isEmpty) {
        return const GenerationValidationResult(
          isValid: false,
          error: 'لا توجد خطة وجبات متاحة. يرجى إنشاء خطة وجبات أولاً.',
        );
      }

      // Parse meal plan to compute date range
      final dateRange = computeDateRangeFromMealPlan(mealPlanData);
      if (dateRange == null) {
        return const GenerationValidationResult(
          isValid: false,
          error: 'لا توجد وجبات قادمة في خطة الوجبات.',
        );
      }

      final startDate = dateRange['startDate']!;
      final endDate = dateRange['endDate']!;

      // Pre-check 2: Check for existing shopping lists and duplicates
      final existingLists = _localStorageService.loadAllShoppingLists();
      if (existingLists.isNotEmpty) {
        // Check if a shopping list for the same date range already exists
        final duplicateList = findDuplicateShoppingList(existingLists, startDate, endDate);
        if (duplicateList != null) {
          return GenerationValidationResult(
            isValid: false,
            startDate: startDate,
            endDate: endDate,
            duplicateListTitle: duplicateList['title'] as String,
            shoppingListId: duplicateList['id'] as String, // Return the ID for replacement
          );
        }
      }

      return GenerationValidationResult(
        isValid: true,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      AppLogger.error('ShoppingListService: Error in validateShoppingListGeneration: $e');
      return GenerationValidationResult(
        isValid: false,
        error: 'فشل في التحقق من صحة إنشاء قائمة التسوق: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> executeGeneration(DateTime startDate, DateTime endDate, {String? shoppingIdToReplace}) async {
    try {
      // Set loading flag
      await setLoadingFlag();

      final action = shoppingIdToReplace != null ? 'استبدال' : 'إنشاء';
      AppLogger.info('ShoppingListService: Executing $action for date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      if (shoppingIdToReplace != null) {
        AppLogger.info('ShoppingListService: Replacing shopping list ID: $shoppingIdToReplace');
      }

      // Generate shopping list using Firebase Functions
      final response = await generateShoppingListFromMeals(
        startDate: startDate,
        endDate: endDate,
        shoppingIdToReplace: shoppingIdToReplace,
      );

      final success = response['success'] as bool? ?? false;
      if (success) {
        AppLogger.info('ShoppingListService: Successfully started shopping list $action');
        return const ShoppingListOperationResult(success: true);
      } else {
        final errorMessage = response['error'] as String? ?? 'فشل في $action قائمة التسوق';
        throw Exception(errorMessage);
      }
    } catch (e) {
      // Clear the loading flag on error
      await removeLoadingFlag();

      final action = shoppingIdToReplace != null ? 'استبدال' : 'إنشاء';
      AppLogger.error('ShoppingListService: Error executing $action: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في $action قائمة التسوق: $e',
      );
    }
  }

  @override
  Map<String, DateTime>? computeDateRangeFromMealPlan(Map<String, dynamic> mealPlanData) {
    try {
      final days = mealPlanData['days'] as List<dynamic>?;
      if (days == null || days.isEmpty) return null;

      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      // Find upcoming meals (from today onwards)
      final upcomingDays = days.where((day) {
        try {
          final dayDate = DateTime.parse(day['date'] as String);
          final dayDateOnly = DateTime(dayDate.year, dayDate.month, dayDate.day);
          return dayDateOnly.isAtSameMomentAs(todayDate) || dayDateOnly.isAfter(todayDate);
        } catch (e) {
          return false;
        }
      }).toList();

      if (upcomingDays.isEmpty) return null;

      // Get start and end dates
      final startDate = DateTime.parse(upcomingDays.first['date'] as String);
      final endDate = DateTime.parse(upcomingDays.last['date'] as String);

      return {
        'startDate': DateTime(startDate.year, startDate.month, startDate.day),
        'endDate': DateTime(endDate.year, endDate.month, endDate.day),
      };
    } catch (e) {
      AppLogger.error('ShoppingListService: Error computing date range: $e');
      return null;
    }
  }

  @override
  Map<String, dynamic>? findDuplicateShoppingList(
    List<Map<String, dynamic>> existingLists,
    DateTime startDate,
    DateTime endDate,
  ) {
    try {
      for (final listData in existingLists) {
        final listStartDate = listData['start_date'] as String?;
        final listEndDate = listData['end_date'] as String?;

        if (listStartDate != null && listEndDate != null) {
          final listStart = DateTime.parse(listStartDate);
          final listEnd = DateTime.parse(listEndDate);

          final listStartOnly = DateTime(listStart.year, listStart.month, listStart.day);
          final listEndOnly = DateTime(listEnd.year, listEnd.month, listEnd.day);
          final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
          final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);

          if (listStartOnly.isAtSameMomentAs(startDateOnly) &&
              listEndOnly.isAtSameMomentAs(endDateOnly)) {
            return listData;
          }
        }
      }
      return null;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error finding duplicate: $e');
      return null;
    }
  }

  @override
  DateTime? parseFirestoreTimestamp(dynamic timestamp) {
    return TimestampUtils.convertTimestampToDateTimeNullable(timestamp);
  }

  @override
  bool isGenerating() {
    return _syncService.isGenerating();
  }

  @override
  bool isLoadingFlagExpired() {
    return _localStorageService.isShoppingListLoadingFlagExpired();
  }

  @override
  Future<void> setLoadingFlag() async {
    await _localStorageService.setShoppingListLoadingFlag();
  }

  @override
  Future<void> removeLoadingFlag() async {
    await _localStorageService.removeShoppingListLoadingFlag();
  }

  /// Sync shopping lists from Firestore and load into local storage
  Future<void> syncAndLoadShoppingLists() async {
    try {
      await _syncService.syncShoppingListDataFromFirestore();
    } catch (e) {
      AppLogger.error('ShoppingListService: Error syncing shopping lists: $e');
    }
  }

  /// Handle Firestore updates and check for new shopping lists
  Future<bool> handleFirestoreUpdate(Map<String, dynamic> newEntry) async {
    try {
      // Check if we have a loading flag
      final loadingFlag = _localStorageService.getShoppingListLoadingFlag();
      if (loadingFlag != null) {
        final startedAtString = loadingFlag['started_at'] as String?;
        if (startedAtString != null) {
          final startedAt = DateTime.parse(startedAtString);

          // Check if the new entry was created after the loading flag
          final entryCreatedAt = parseFirestoreTimestamp(newEntry['created_at']);
          if (entryCreatedAt != null && entryCreatedAt.isAfter(startedAt)) {
            AppLogger.info('ShoppingListService: New shopping list detected (${newEntry['id']}), completing generation');

            // Load all shopping list data from Firestore
            await syncAndLoadShoppingLists();

            // Clear the loading flag
            await removeLoadingFlag();

            AppLogger.info('ShoppingListService: Completed shopping list generation');
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error handling Firestore update: $e');
      return false;
    }
  }

  @override
  Future<ShoppingListOperationResult> loadMostRecentShoppingList() async {
    try {
      AppLogger.info('ShoppingListService: Loading most recent shopping list from local storage');

      final allShoppingLists = _localStorageService.loadAllShoppingLists();

      if (allShoppingLists.isNotEmpty) {
        // Load the most recent shopping list
        final mostRecentListData = allShoppingLists.first;
        final shoppingList = ShoppingList.fromJson(mostRecentListData);

        AppLogger.info('ShoppingListService: Loaded most recent shopping list: ${shoppingList.id}');
        return ShoppingListOperationResult(
          success: true,
          data: shoppingList,
        );
      } else {
        AppLogger.info('ShoppingListService: No shopping lists found in local storage');
        return const ShoppingListOperationResult(success: true, data: null);
      }
    } catch (e) {
      AppLogger.error('ShoppingListService: Error loading most recent shopping list: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في تحميل قائمة التسوق: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> toggleItemPurchase(ShoppingList shoppingList, String itemId) async {
    try {
      AppLogger.info('ShoppingListService: Toggling item purchase status for item: $itemId');

      // Find and update the item
      final updatedCategories = shoppingList.categories.map((category) {
        final updatedItems = category.items.map((item) {
          if (item.id == itemId) {
            return item.copyWith(isPurchased: !item.isPurchased);
          }
          return item;
        }).toList();
        return category.copyWith(items: updatedItems);
      }).toList();

      final updatedList = shoppingList.copyWith(
        categories: updatedCategories,
        lastUpdated: DateTime.now(),
      );

      // Sync to Firestore and local storage
      await syncShoppingListUpdate(updatedList);

      AppLogger.info('ShoppingListService: Successfully toggled item purchase status');
      return ShoppingListOperationResult(
        success: true,
        data: updatedList,
      );
    } catch (e) {
      AppLogger.error('ShoppingListService: Error toggling item purchase: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في تحديث حالة العنصر: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> clearShoppingList(String shoppingListId) async {
    try {
      AppLogger.info('ShoppingListService: Clearing shopping list: $shoppingListId');

      await _localStorageService.removeShoppingList(shoppingListId);

      AppLogger.info('ShoppingListService: Successfully cleared shopping list');
      return const ShoppingListOperationResult(success: true);
    } catch (e) {
      AppLogger.error('ShoppingListService: Error clearing shopping list: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في مسح قائمة التسوق: $e',
      );
    }
  }

  @override
  Future<ShoppingListOperationResult> generateShoppingListFromDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Generating shopping list from date range: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

      // Set generation flag in local storage
      final flagSet = await setGeneratingFlag();
      if (!flagSet) {
        AppLogger.warning('ShoppingListService: Failed to set generation flag, continuing anyway');
      }

      final response = await generateShoppingListFromMeals(
        startDate: startDate,
        endDate: endDate,
      );

      final success = response['success'] as bool? ?? false;
      if (success) {
        AppLogger.info('ShoppingListService: Shopping list generation started successfully');
        return const ShoppingListOperationResult(success: true);
      } else {
        // Clear generation flag on failure
        await removeGeneratingFlag();
        final errorMessage = response['error'] as String? ?? 'Unknown error';
        return ShoppingListOperationResult(
          success: false,
          error: errorMessage,
        );
      }
    } catch (e) {
      // Clear generation flag on error
      await removeGeneratingFlag();

      AppLogger.error('ShoppingListService: Error generating shopping list from date range: $e');
      return ShoppingListOperationResult(
        success: false,
        error: 'فشل في طلب إنشاء قائمة التسوق: $e',
      );
    }
  }

  @override
  Future<void> syncShoppingListUpdate(ShoppingList shoppingList) async {
    try {
      AppLogger.info('ShoppingListService: Syncing shopping list update: ${shoppingList.id}');
      await _syncService.syncShoppingList(shoppingList, isNewList: false);
    } catch (e) {
      AppLogger.error('ShoppingListService: Error syncing shopping list update: $e');
      // Fallback to local storage only
      await saveShoppingListToLocalStorage(shoppingList);
    }
  }

  @override
  Future<void> saveShoppingListToLocalStorage(ShoppingList shoppingList) async {
    try {
      AppLogger.info('ShoppingListService: Saving shopping list to local storage: ${shoppingList.id}');
      await _localStorageService.updateShoppingList(shoppingList.toJson());
    } catch (e) {
      AppLogger.error('ShoppingListService: Error saving to local storage: $e');
    }
  }

  @override
  Future<bool> setGeneratingFlag() async {
    try {
      return await _syncService.setGeneratingFlag();
    } catch (e) {
      AppLogger.error('ShoppingListService: Error setting generating flag: $e');
      return false;
    }
  }

  @override
  Future<bool> removeGeneratingFlag() async {
    try {
      return await _syncService.removeGeneratingFlag();
    } catch (e) {
      AppLogger.error('ShoppingListService: Error removing generating flag: $e');
      return false;
    }
  }

  /// Update shopping list from Firestore listener and clear generation flag
  Future<ShoppingList?> updateShoppingListFromListener(ShoppingList shoppingList) async {
    try {
      AppLogger.info('ShoppingListService: Updating shopping list from listener: ${shoppingList.id}');

      // Clear generation flag since new data has arrived
      await removeGeneratingFlag();

      // Save to local storage for offline access
      await saveShoppingListToLocalStorage(shoppingList);

      return shoppingList;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error updating shopping list from listener: $e');
      return null;
    }
  }

  // Firestore operations (feature-specific implementations)

  @override
  Future<void> saveShoppingListToFirestore(String listId, Map<String, dynamic> shoppingListData) async {
    try {
      AppLogger.info('ShoppingListService: Saving shopping list to Firestore: $listId');
      await _firestoreService.saveToUserSubcollection(
        subcollectionName: 'shopping_list_entries',
        documentId: listId,
        data: shoppingListData,
      );
      AppLogger.info('ShoppingListService: Successfully saved shopping list to Firestore: $listId');
    } catch (e) {
      AppLogger.error('ShoppingListService: Error saving shopping list to Firestore: $e');
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getShoppingListsFromFirestore() async {
    try {
      AppLogger.info('ShoppingListService: Fetching shopping lists from Firestore');
      final documents = await _firestoreService.getUserSubcollectionDocuments(
        subcollectionName: 'shopping_list_entries',
        orderByField: 'created_at',
        descending: true,
      );
      AppLogger.info('ShoppingListService: Successfully fetched ${documents.length} shopping lists from Firestore');
      return documents;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error fetching shopping lists from Firestore: $e');
      return [];
    }
  }

  @override
  Future<void> deleteShoppingListFromFirestore(String listId) async {
    try {
      AppLogger.info('ShoppingListService: Deleting shopping list from Firestore: $listId');
      await _firestoreService.deleteFromUserSubcollection(
        subcollectionName: 'shopping_list_entries',
        documentId: listId,
      );
      AppLogger.info('ShoppingListService: Successfully deleted shopping list from Firestore: $listId');
    } catch (e) {
      AppLogger.error('ShoppingListService: Error deleting shopping list from Firestore: $e');
      rethrow;
    }
  }

  @override
  Stream<Map<String, dynamic>?> getShoppingListStream({DateTime? afterTimestamp}) {
    try {
      AppLogger.info('ShoppingListService: Setting up shopping list stream');
      return _firestoreService.getLatestDocumentStream(
        subcollectionName: 'shopping_list_entries',
        orderByField: 'created_at',
        afterTimestamp: afterTimestamp,
      );
    } catch (e) {
      AppLogger.error('ShoppingListService: Error setting up shopping list stream: $e');
      return Stream.error(e);
    }
  }

  /// Get shopping list by ID from Firestore
  Future<Map<String, dynamic>?> getShoppingListById(String listId) async {
    try {
      AppLogger.info('ShoppingListService: Getting shopping list by ID from Firestore: $listId');
      final document = await _firestoreService.getDocumentFromUserSubcollection(
        subcollectionName: 'shopping_list_entries',
        documentId: listId,
      );
      if (document != null) {
        AppLogger.info('ShoppingListService: Successfully retrieved shopping list: $listId');
      } else {
        AppLogger.info('ShoppingListService: Shopping list not found: $listId');
      }
      return document;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error getting shopping list by ID: $e');
      rethrow;
    }
  }

  /// Query shopping lists by date range
  Future<List<Map<String, dynamic>>> getShoppingListsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Querying shopping lists by date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}');

      // This would require a more complex query implementation
      // For now, get all and filter locally
      final allLists = await getShoppingListsFromFirestore();

      final filteredLists = allLists.where((listData) {
        try {
          final startDateStr = listData['start_date'] as String?;
          final endDateStr = listData['end_date'] as String?;

          if (startDateStr != null && endDateStr != null) {
            final listStartDate = DateTime.parse(startDateStr);
            final listEndDate = DateTime.parse(endDateStr);

            // Check if date ranges overlap
            return listStartDate.isBefore(endDate.add(const Duration(days: 1))) &&
                   listEndDate.isAfter(startDate.subtract(const Duration(days: 1)));
          }
          return false;
        } catch (e) {
          AppLogger.warning('ShoppingListService: Error parsing dates for shopping list: $e');
          return false;
        }
      }).toList();

      AppLogger.info('ShoppingListService: Found ${filteredLists.length} shopping lists in date range');
      return filteredLists;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error querying shopping lists by date range: $e');
      return [];
    }
  }

  // Firebase Functions operations (feature-specific implementations)

  @override
  Future<Map<String, dynamic>> callGenerateShoppingListFunction({
    required DateTime startDate,
    required DateTime endDate,
    String? shoppingIdToReplace,
  }) async {
    try {
      final action = shoppingIdToReplace != null ? 'Replacing' : 'Generating';
      AppLogger.info('ShoppingListService: $action shopping list function for date range: ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

      if (shoppingIdToReplace != null) {
        AppLogger.info('ShoppingListService: Replacing shopping list ID: $shoppingIdToReplace');
      }

      final requestData = {
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
      };

      // Add shopping_id for replacement if provided
      if (shoppingIdToReplace != null) {
        requestData['shopping_id'] = shoppingIdToReplace;
      }

      final response = await _functionsService.post(
        endpoint: '/shopping-list/generate-from-meals',
        data: requestData,
        logContext: '${action} shopping list from meals',
      );

      AppLogger.info('ShoppingListService: Shopping list generation function completed successfully');
      return response;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error calling generate shopping list function: $e');
      throw Exception('Failed to generate shopping list from meals: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> callAnalyzeShoppingListFunction({
    required List<String> items,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Calling analyze shopping list function for ${items.length} items');

      final response = await _functionsService.post(
        endpoint: '/shopping-list/analyze',
        data: {
          'items': items,
        },
        logContext: 'Analyzing shopping list items',
      );

      AppLogger.info('ShoppingListService: Shopping list analysis function completed successfully');
      return response;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error calling analyze shopping list function: $e');
      throw Exception('Failed to analyze shopping list: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> callOptimizeShoppingListFunction({
    required String shoppingListId,
    Map<String, dynamic>? preferences,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Calling optimize shopping list function for list: $shoppingListId');

      final response = await _functionsService.post(
        endpoint: '/shopping-list/optimize',
        data: {
          'shopping_list_id': shoppingListId,
          if (preferences != null) 'preferences': preferences,
        },
        logContext: 'Optimizing shopping list',
      );

      AppLogger.info('ShoppingListService: Shopping list optimization function completed successfully');
      return response;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error calling optimize shopping list function: $e');
      throw Exception('Failed to optimize shopping list: $e');
    }
  }

  /// Generate shopping list with custom parameters
  Future<Map<String, dynamic>> generateCustomShoppingList({
    required List<String> mealIds,
    Map<String, dynamic>? customPreferences,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Generating custom shopping list for ${mealIds.length} meals');

      final response = await _functionsService.post(
        endpoint: '/shopping-list/generate-custom',
        data: {
          'meal_ids': mealIds,
          if (customPreferences != null) 'preferences': customPreferences,
        },
        logContext: 'Generating custom shopping list',
      );

      AppLogger.info('ShoppingListService: Custom shopping list generation completed successfully');
      return response;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error generating custom shopping list: $e');
      throw Exception('Failed to generate custom shopping list: $e');
    }
  }

  /// Get shopping list suggestions based on dietary preferences
  Future<Map<String, dynamic>> getShoppingListSuggestions({
    Map<String, dynamic>? dietaryPreferences,
    int? maxItems,
  }) async {
    try {
      AppLogger.info('ShoppingListService: Getting shopping list suggestions');

      final response = await _functionsService.get(
        endpoint: '/shopping-list/suggestions',
        queryParameters: {
          if (dietaryPreferences != null) 'preferences': dietaryPreferences.toString(),
          if (maxItems != null) 'max_items': maxItems.toString(),
        },
        logContext: 'Getting shopping list suggestions',
      );

      AppLogger.info('ShoppingListService: Shopping list suggestions retrieved successfully');
      return response;
    } catch (e) {
      AppLogger.error('ShoppingListService: Error getting shopping list suggestions: $e');
      throw Exception('Failed to get shopping list suggestions: $e');
    }
  }
}
