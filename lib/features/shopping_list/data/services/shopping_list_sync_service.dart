import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/local_storage_service.dart';
import '../../../../core/services/firestore_service.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/timestamp_utils.dart';
import '../models/shopping_list_models.dart';

/// Provider for shopping list sync service
final shoppingListSyncServiceProvider = Provider<ShoppingListSyncService>((ref) {
  final localStorageService = ref.watch(localStorageServiceProvider);
  final firestoreService = ref.watch(firestoreServiceProvider);
  return ShoppingListSyncService(localStorageService, firestoreService);
});

/// Service for syncing shopping list data between local storage and Firestore
class ShoppingListSyncService {
  final AsyncValue<LocalStorageService> _localStorageService;
  final FirestoreService _firestoreService;

  ShoppingListSyncService(this._localStorageService, this._firestoreService);

  /// Sync shopping list data from local storage to Firestore
  Future<bool> syncShoppingListDataToFirestore() async {
    try {
      AppLogger.info('ShoppingListSyncService: Syncing shopping list data to Firestore');

      // Get data from local storage
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );
      final localShoppingListsData = localStorageService.loadAllShoppingLists();

      if (localShoppingListsData.isEmpty) {
        AppLogger.info('ShoppingListSyncService: No shopping list data found in local storage');
        return false;
      }

      // Sync individual shopping list entries using generic method
      for (final shoppingListData in localShoppingListsData) {
        try {
          final shoppingList = ShoppingList.fromJson(shoppingListData);
          await _firestoreService.saveToUserSubcollection(
            subcollectionName: 'shopping_list_entries',
            documentId: shoppingList.id,
            data: shoppingList.toJson(),
          );
        } catch (e) {
          AppLogger.warning('ShoppingListSyncService: Failed to sync shopping list to Firestore: $e');
        }
      }

      AppLogger.info('ShoppingListSyncService: Successfully synced ${localShoppingListsData.length} shopping lists to Firestore');
      return true;
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error syncing shopping list data to Firestore: $e');
      return false;
    }
  }

  /// Sync shopping list data from Firestore to local storage
  Future<bool> syncShoppingListDataFromFirestore() async {
    try {
      AppLogger.info('ShoppingListSyncService: Syncing shopping list data from Firestore');

      // Get shopping list entries from Firestore using generic method
      final shoppingListEntries = await _firestoreService.getUserSubcollectionDocuments(
        subcollectionName: 'shopping_list_entries',
        orderByField: 'created_at',
        descending: true,
      );

      if (shoppingListEntries.isEmpty) {
        AppLogger.info('ShoppingListSyncService: No shopping list data found in Firestore');
        return false;
      }

      // Save to local storage
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );
      var syncedCount = 0;

      // Convert Firestore data to shopping list objects
      final shoppingListsToSave = <Map<String, dynamic>>[];

      for (final entryData in shoppingListEntries) {
        try {
          // Convert Firestore data to proper format for ShoppingList.fromJson()
          final convertedData = _convertFirestoreDataForParsing(entryData);
          final shoppingList = ShoppingList.fromJson(convertedData);
          shoppingListsToSave.add(shoppingList.toJson());
          syncedCount++;
        } catch (e) {
          AppLogger.warning('ShoppingListSyncService: Failed to parse shopping list from Firestore: $e');
          AppLogger.warning('ShoppingListSyncService: Problematic data: $entryData');
        }
      }

      // Save all shopping lists to consolidated storage
      if (shoppingListsToSave.isNotEmpty) {
        final success = await localStorageService.saveAllShoppingLists(shoppingListsToSave);
        if (!success) {
          AppLogger.error('ShoppingListSyncService: Failed to save shopping lists to local storage');
          return false;
        }
      }

      AppLogger.info('ShoppingListSyncService: Successfully synced $syncedCount shopping lists from Firestore to local storage');
      return syncedCount > 0;
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error syncing shopping list data from Firestore: $e');
      return false;
    }
  }

  /// Bidirectional sync - prioritizes Firestore data if available, otherwise syncs local data to Firestore
  Future<bool> bidirectionalSync() async {
    try {
      AppLogger.info('ShoppingListSyncService: Starting bidirectional sync');

      // First, try to sync from Firestore to local storage
      final syncFromFirestoreSuccess = await syncShoppingListDataFromFirestore();
      
      if (syncFromFirestoreSuccess) {
        AppLogger.info('ShoppingListSyncService: Bidirectional sync completed - used Firestore data');
        return true;
      }

      // If no Firestore data, try to sync local data to Firestore
      final syncToFirestoreSuccess = await syncShoppingListDataToFirestore();
      
      if (syncToFirestoreSuccess) {
        AppLogger.info('ShoppingListSyncService: Bidirectional sync completed - synced local data to Firestore');
        return true;
      }

      AppLogger.info('ShoppingListSyncService: Bidirectional sync completed - no data found in either location');
      return false;
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error during bidirectional sync: $e');
      return false;
    }
  }

  /// Sync a shopping list to both Firestore and local storage
  /// Handles both new shopping lists and updates to existing ones
  Future<bool> syncShoppingList(ShoppingList shoppingList, {bool isNewList = false}) async {
    try {
      final action = isNewList ? 'Adding new' : 'Updating';
      AppLogger.info('ShoppingListSyncService: $action shopping list: ${shoppingList.id}');

      // First, save to Firestore using generic method
      await _firestoreService.saveToUserSubcollection(
        subcollectionName: 'shopping_list_entries',
        documentId: shoppingList.id,
        data: shoppingList.toJson(),
      );

      // Then, save to local storage
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );

      final success = isNewList
          ? await localStorageService.addShoppingList(shoppingList.toJson())
          : await localStorageService.updateShoppingList(shoppingList.toJson());

      if (success) {
        AppLogger.info('ShoppingListSyncService: Successfully ${isNewList ? 'added' : 'updated'} shopping list: ${shoppingList.id}');
        return true;
      } else {
        AppLogger.warning('ShoppingListSyncService: Failed to ${isNewList ? 'add' : 'update'} shopping list in local storage: ${shoppingList.id}');
        return false;
      }
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error ${isNewList ? 'adding' : 'updating'} shopping list: $e');
      return false;
    }
  }

  /// Sync a single shopping list update to Firestore and local storage
  /// @deprecated Use syncShoppingList() instead
  Future<bool> syncSingleShoppingListUpdate(ShoppingList shoppingList) async {
    return syncShoppingList(shoppingList, isNewList: false);
  }

  /// Add a new shopping list to both Firestore and local storage
  /// @deprecated Use syncShoppingList() instead
  Future<bool> addNewShoppingList(ShoppingList shoppingList) async {
    return syncShoppingList(shoppingList, isNewList: true);
  }

  /// Clear all shopping list data from local storage
  Future<bool> clearLocalShoppingListData() async {
    try {
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );
      return await localStorageService.clearAllShoppingListData();
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error clearing local shopping list data: $e');
      return false;
    }
  }

  // Background Generation Flag Management

  /// Set the generating shopping list flag
  Future<bool> setGeneratingFlag() async {
    try {
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );

      final success = await localStorageService.setShoppingListLoadingFlag();
      if (success) {
        AppLogger.info('ShoppingListSyncService: Set generating shopping list flag');
      }
      return success;
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error setting generating flag: $e');
      return false;
    }
  }

  /// Remove the generating shopping list flag
  Future<bool> removeGeneratingFlag() async {
    try {
      final localStorageService = _localStorageService.when(
        data: (service) => service,
        loading: () => throw Exception('Local storage service is loading'),
        error: (error, stack) => throw Exception('Local storage service error: $error'),
      );

      final success = await localStorageService.removeShoppingListLoadingFlag();
      if (success) {
        AppLogger.info('ShoppingListSyncService: Removed generating shopping list flag');
      }
      return success;
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error removing generating flag: $e');
      return false;
    }
  }

  /// Check if shopping list is currently being generated
  bool isGenerating() {
    try {
      return _localStorageService.when(
        data: (service) => service.isGeneratingShoppingList(),
        loading: () => false,
        error: (error, stack) => false,
      );
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error checking generating flag: $e');
      return false;
    }
  }

  /// Get the generation flag timestamp (for Firestore listener cutoff)
  int? getGenerationFlagTimestamp() {
    try {
      return _localStorageService.when(
        data: (service) => service.getGeneratingShoppingListFlag(),
        loading: () => null,
        error: (error, stack) => null,
      );
    } catch (e) {
      AppLogger.error('ShoppingListSyncService: Error getting generation flag timestamp: $e');
      return null;
    }
  }



  /// Convert Firestore data to format compatible with ShoppingList.fromJson()
  Map<String, dynamic> _convertFirestoreDataForParsing(Map<String, dynamic> firestoreData) {
    final convertedData = Map<String, dynamic>.from(firestoreData);

    // Convert Firestore Timestamps to ISO 8601 strings
    if (convertedData['created_at'] != null) {
      convertedData['created_at'] = TimestampUtils.convertTimestampToString(convertedData['created_at']);
    }

    if (convertedData['last_modified'] != null) {
      convertedData['last_modified'] = TimestampUtils.convertTimestampToString(convertedData['last_modified']);
    }

    if (convertedData['synced_at'] != null) {
      convertedData['synced_at'] = TimestampUtils.convertTimestampToString(convertedData['synced_at']);
    }

    // Convert start_date and end_date Firestore Timestamps to ISO 8601 strings
    if (convertedData['start_date'] != null) {
      convertedData['start_date'] = TimestampUtils.convertTimestampToString(convertedData['start_date']);
    }

    if (convertedData['end_date'] != null) {
      convertedData['end_date'] = TimestampUtils.convertTimestampToString(convertedData['end_date']);
    }

    // Remove firestore_id if present (it's not part of the ShoppingList model)
    convertedData.remove('firestore_id');

    return convertedData;
  }



}
