import 'package:freezed_annotation/freezed_annotation.dart';

part 'shopping_list_models.freezed.dart';
part 'shopping_list_models.g.dart';

/// Model for a single shopping list item
@freezed
class ShoppingListItem with _$ShoppingListItem {
  const factory ShoppingListItem({
    required String id,
    required String name,
    required String quantity,
    required String unit,
    @Default(false) bool isPurchased,
    String? notes,
  }) = _ShoppingListItem;

  factory ShoppingListItem.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListItemFromJson(json);
}

/// Model for a shopping list category
@freezed
class ShoppingListCategory with _$ShoppingListCategory {
  const factory ShoppingListCategory({
    required String id,
    required String name,
    required String arabicName,
    required List<ShoppingListItem> items,
    String? icon,
    String? description,
  }) = _ShoppingListCategory;

  factory ShoppingListCategory.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListCategoryFromJson(json);
}

/// Extension to add computed properties to ShoppingListCategory
extension ShoppingListCategoryExtension on ShoppingListCategory {
  /// Get the number of purchased items in this category
  int get purchasedItemsCount {
    return items.where((item) => item.isPurchased).length;
  }

  /// Get the total number of items in this category
  int get totalItemsCount {
    return items.length;
  }

  /// Check if all items in this category are purchased
  bool get isCompleted {
    return items.isNotEmpty && items.every((item) => item.isPurchased);
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (items.isEmpty) return 0.0;
    return purchasedItemsCount / totalItemsCount;
  }

  /// Get formatted progress text (e.g., "3 / 5")
  String get progressText {
    return '$purchasedItemsCount / $totalItemsCount';
  }
}

/// Model for the complete shopping list
@freezed
class ShoppingList with _$ShoppingList {
  const factory ShoppingList({
    required String id,
    required String title,
    required DateTime createdAt,
    required List<ShoppingListCategory> categories,
    @Default('') String notes,
    DateTime? lastUpdated,
    // Date range tracking for duplicate detection
    @JsonKey(name: 'start_date') DateTime? startDate,
    @JsonKey(name: 'end_date') DateTime? endDate,
  }) = _ShoppingList;

  factory ShoppingList.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListFromJson(json);
}

/// Extension to add computed properties to ShoppingList
extension ShoppingListExtension on ShoppingList {
  /// Get all items across all categories
  List<ShoppingListItem> get allItems {
    return categories.expand((category) => category.items).toList();
  }

  /// Get the total number of items
  int get totalItemsCount {
    return allItems.length;
  }

  /// Get the number of purchased items
  int get purchasedItemsCount {
    return allItems.where((item) => item.isPurchased).length;
  }

  /// Get the number of remaining items
  int get remainingItemsCount {
    return totalItemsCount - purchasedItemsCount;
  }

  /// Check if the shopping list is completed
  bool get isCompleted {
    return totalItemsCount > 0 && purchasedItemsCount == totalItemsCount;
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (totalItemsCount == 0) return 0.0;
    return purchasedItemsCount / totalItemsCount;
  }

  /// Get formatted progress text (e.g., "15 / 23 عنصر")
  String get progressText {
    return '$purchasedItemsCount / $totalItemsCount عنصر';
  }

  /// Get completion percentage (0.0 to 1.0)
  double get completionPercentage {
    if (totalItemsCount == 0) return 0.0;
    return purchasedItemsCount / totalItemsCount;
  }

  /// Check if this shopping list covers the same date range as another
  bool coversSameDateRange(DateTime? otherStartDate, DateTime? otherEndDate) {
    // If either shopping list doesn't have date range info, can't compare
    if (startDate == null || endDate == null || otherStartDate == null || otherEndDate == null) {
      return false;
    }

    // Compare dates (ignoring time components)
    final thisStart = DateTime(startDate!.year, startDate!.month, startDate!.day);
    final thisEnd = DateTime(endDate!.year, endDate!.month, endDate!.day);
    final otherStart = DateTime(otherStartDate.year, otherStartDate.month, otherStartDate.day);
    final otherEnd = DateTime(otherEndDate.year, otherEndDate.month, otherEndDate.day);

    return thisStart.isAtSameMomentAs(otherStart) && thisEnd.isAtSameMomentAs(otherEnd);
  }

  /// Get formatted date range text for display
  String get dateRangeText {
    if (startDate == null || endDate == null) {
      return '';
    }

    final start = DateTime(startDate!.year, startDate!.month, startDate!.day);
    final end = DateTime(endDate!.year, endDate!.month, endDate!.day);

    if (start.isAtSameMomentAs(end)) {
      return '${start.day}/${start.month}/${start.year}';
    } else {
      return '${start.day}/${start.month}/${start.year} - ${end.day}/${end.month}/${end.year}';
    }
  }


}

/// Model for shopping list generation request
@freezed
class ShoppingListRequest with _$ShoppingListRequest {
  const factory ShoppingListRequest({
    required List<String> mealIds,
    required DateTime startDate,
    required DateTime endDate,
    @Default([]) List<String> excludedIngredients,
    @Default(1) int servingMultiplier,
  }) = _ShoppingListRequest;

  factory ShoppingListRequest.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListRequestFromJson(json);
}

/// Model for shopping list generation response
@freezed
class ShoppingListResponse with _$ShoppingListResponse {
  const factory ShoppingListResponse({
    required ShoppingList shoppingList,
    required String message,
    @Default(true) bool success,
    String? error,
  }) = _ShoppingListResponse;

  factory ShoppingListResponse.fromJson(Map<String, dynamic> json) =>
      _$ShoppingListResponseFromJson(json);
}



/// Predefined shopping list categories
class ShoppingListCategories {
  static const String vegetables = 'vegetables';
  static const String fruits = 'fruits';
  static const String proteins = 'proteins';
  static const String dairy = 'dairy';
  static const String grains = 'grains';
  static const String spices = 'spices';
  static const String oils = 'oils';
  static const String beverages = 'beverages';
  static const String snacks = 'snacks';
  static const String frozen = 'frozen';
  static const String canned = 'canned';
  static const String bakery = 'bakery';
  static const String household = 'household';

  static const Map<String, String> categoryNames = {
    vegetables: 'خضروات',
    fruits: 'فواكه',
    proteins: 'بروتينات',
    dairy: 'ألبان',
    grains: 'حبوب ونشويات',
    spices: 'توابل وبهارات',
    oils: 'زيوت ودهون',
    beverages: 'مشروبات',
    snacks: 'وجبات خفيفة',
    frozen: 'مجمدات',
    canned: 'معلبات',
    bakery: 'مخبوزات',
    household: 'منتجات منزلية',
  };

  static const Map<String, String> categoryIcons = {
    vegetables: '🥬',
    fruits: '🍎',
    proteins: '🥩',
    dairy: '🥛',
    grains: '🌾',
    spices: '🧂',
    oils: '🫒',
    beverages: '🥤',
    snacks: '🍿',
    frozen: '🧊',
    canned: '🥫',
    bakery: '🍞',
    household: '🧽',
  };
}
