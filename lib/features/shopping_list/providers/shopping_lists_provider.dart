import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../../../core/services/firestore_service.dart';
import '../../../core/utils/logger.dart';
import '../data/models/shopping_list_models.dart';
import '../data/services/shopping_list_service.dart';

part 'shopping_lists_provider.freezed.dart';
part 'shopping_lists_provider.g.dart';

@freezed
class PendingGeneration with _$PendingGeneration {
  const factory PendingGeneration({
    required DateTime startDate,
    required DateTime endDate,
    required String duplicateListTitle,
    required String shoppingListId, // ID for replacement
  }) = _PendingGeneration;
}

@freezed
class ShoppingListsState with _$ShoppingListsState {
  const factory ShoppingListsState({
    @Default([]) List<ShoppingList> shoppingLists,
    @Default(false) bool isLoading,
    @Default(false) bool isGenerating,
    @Default(false) bool isRefreshing,
    @Default(false) bool isBackgroundGenerating,
    String? error,
    String? generatingMessage,
    PendingGeneration? pendingGeneration,
  }) = _ShoppingListsState;
}

@riverpod
class ShoppingListsNotifier extends _$ShoppingListsNotifier {
  StreamSubscription<Map<String, dynamic>?>? _firestoreListener;
  Timer? _loadingFlagTimeoutTimer;

  @override
  ShoppingListsState build() {
    // Set up Firestore listener when provider is built
    _setupFirestoreListener();

    // Check for background generation state after a brief delay to ensure all providers are initialized
    Future.microtask(() => _checkBackgroundGenerationState());

    return const ShoppingListsState();
  }

  void dispose() {
    _firestoreListener?.cancel();
    _loadingFlagTimeoutTimer?.cancel();
  }



  /// Set up Firestore listener for real-time shopping list updates
  /// Only listens for newly added shopping lists
  void _setupFirestoreListener() {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);

      AppLogger.info('ShoppingListOverviewNotifier: Setting up listener for latest shopping list');

      // Listen only for shopping lists created after now (to avoid getting existing documents)
      final currentTime = DateTime.now();
      AppLogger.info('ShoppingListOverviewNotifier: Setting up listener for documents created after: ${currentTime.toIso8601String()}');

      _firestoreListener = firestoreService.getLatestDocumentStream(
        subcollectionName: 'shopping_list_entries',
        orderByField: 'created_at',
        afterTimestamp: currentTime, // Only listen for new documents
      ).listen(
        (latestEntry) async {
          if (latestEntry != null) {
            AppLogger.info('ShoppingListOverviewNotifier: Received new shopping list from Firestore: ${latestEntry['id']}');

            // Handle the new shopping list entry using service
            await _handleFirestoreUpdate(latestEntry);
          }
        },
        onError: (error) {
          AppLogger.error('ShoppingListOverviewNotifier: Firestore listener error: $error');
        },
      );

      // Set up periodic check for expired loading flags
      _setupLoadingFlagTimeoutCheck();
    } catch (e) {
      AppLogger.error('ShoppingListOverviewNotifier: Error setting up Firestore listener: $e');
    }
  }

  /// Handle Firestore updates and check for new shopping lists
  Future<void> _handleFirestoreUpdate(Map<String, dynamic> newEntry) async {
    try {
      final shoppingListService = await ref.read(shoppingListServiceProvider.future);

      // Delegate to service for business logic
      final wasCompleted = await shoppingListService.handleFirestoreUpdate(newEntry);

      if (wasCompleted) {
        // Update the UI state
        state = state.copyWith(
          isBackgroundGenerating: false,
          generatingMessage: null,
        );

        // Reload shopping lists to update UI
        await loadShoppingLists();
      }
    } catch (e) {
      AppLogger.error('ShoppingListOverviewNotifier: Error handling Firestore update: $e');
    }
  }

  /// Check for background generation state on provider initialization
  void _checkBackgroundGenerationState() {
    try {
      ref.read(shoppingListServiceProvider.future).then((service) {
        final isGenerating = service.isGenerating();

        if (isGenerating) {
          state = state.copyWith(
            isBackgroundGenerating: true,
            generatingMessage: 'جاري إنشاء قائمة التسوق في الخلفية...',
          );
          AppLogger.info('ShoppingListOverviewNotifier: Detected background generation in progress');
        }
      }).catchError((e) {
        // Handle any errors including uninitialized provider errors
        if (e.toString().contains('uninitialized provider')) {
          AppLogger.warning('ShoppingListOverviewNotifier: Provider not yet initialized, will retry later');
          // Retry after a short delay
          Future.delayed(const Duration(milliseconds: 100), _checkBackgroundGenerationState);
        } else {
          AppLogger.error('ShoppingListOverviewNotifier: Error checking background generation state: $e');
        }
        // Don't rethrow - this is not critical for app functionality
      });
    } catch (e) {
      AppLogger.error('ShoppingListOverviewNotifier: Error checking background generation state: $e');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Set background generating state
  void setBackgroundGeneratingState(bool isGenerating) {
    state = state.copyWith(
      isBackgroundGenerating: isGenerating,
      generatingMessage: isGenerating ? 'جاري إنشاء قائمة التسوق في الخلفية...' : null,
    );

    if (isGenerating) {
      AppLogger.info('ShoppingListOverviewNotifier: Set background generating state to true');
    } else {
      AppLogger.info('ShoppingListOverviewNotifier: Set background generating state to false');
    }
  }

  /// Load shopping lists from local storage (data should already be synced by auth provider)
  Future<void> loadShoppingLists() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final shoppingListService = await ref.read(shoppingListServiceProvider.future);
      final result = await shoppingListService.loadShoppingLists();

      if (result.success) {
        final shoppingLists = result.data as List<ShoppingList>;
        state = state.copyWith(
          shoppingLists: shoppingLists,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحميل قوائم التسوق: $e',
      );
      AppLogger.error('ShoppingListOverviewNotifier: Error loading shopping lists: $e');
    }
  }



  /// Refresh shopping lists (pull-to-refresh)
  Future<void> refreshShoppingLists() async {
    state = state.copyWith(isRefreshing: true, error: null);

    try {
      final shoppingListService = await ref.read(shoppingListServiceProvider.future);
      final result = await shoppingListService.refreshShoppingLists();

      if (result.success) {
        final shoppingLists = result.data as List<ShoppingList>;
        state = state.copyWith(
          shoppingLists: shoppingLists,
          isRefreshing: false,
        );
      } else {
        state = state.copyWith(
          isRefreshing: false,
          error: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isRefreshing: false,
        error: 'فشل في تحديث قوائم التسوق: $e',
      );
      AppLogger.error('ShoppingListOverviewNotifier: Error refreshing shopping lists: $e');
    }
  }

  /// Generate a new shopping list with comprehensive pre-checks
  Future<void> generateNewShoppingList() async {
    try {
      final shoppingListService = await ref.read(shoppingListServiceProvider.future);
      final validationResult = await shoppingListService.validateShoppingListGeneration();

      if (!validationResult.isValid) {
        if (validationResult.duplicateListTitle != null && validationResult.shoppingListId != null) {
          // Show confirmation dialog through state (UI will handle the dialog)
          state = state.copyWith(
            pendingGeneration: PendingGeneration(
              startDate: validationResult.startDate!,
              endDate: validationResult.endDate!,
              duplicateListTitle: validationResult.duplicateListTitle!,
              shoppingListId: validationResult.shoppingListId!, // Include ID for replacement
            ),
          );
        } else {
          state = state.copyWith(error: validationResult.error);
        }
        return;
      }

      // Proceed with generation
      await _executeGeneration(validationResult.startDate!, validationResult.endDate!);
    } catch (e) {
      state = state.copyWith(
        error: 'فشل في إنشاء قائمة التسوق: $e',
      );
      AppLogger.error('ShoppingListOverviewNotifier: Error in generateNewShoppingList: $e');
    }
  }

  /// Confirm generation after duplicate check
  Future<void> confirmGeneration() async {
    final pendingGeneration = state.pendingGeneration;
    if (pendingGeneration != null) {
      state = state.copyWith(pendingGeneration: null);
      // Execute generation with replacement ID
      await _executeGeneration(
        pendingGeneration.startDate,
        pendingGeneration.endDate,
        shoppingIdToReplace: pendingGeneration.shoppingListId,
      );
    }
  }

  /// Cancel pending generation
  void cancelGeneration() {
    state = state.copyWith(pendingGeneration: null);
  }

  /// Execute the actual generation process
  /// Execute shopping list generation (with optional replacement)
  Future<void> _executeGeneration(DateTime startDate, DateTime endDate, {String? shoppingIdToReplace}) async {
    try {
      final isReplacement = shoppingIdToReplace != null;
      final action = isReplacement ? 'استبدال' : 'إنشاء';

      state = state.copyWith(
        isGenerating: true,
        isBackgroundGenerating: true,
        error: null,
        generatingMessage: 'جاري $action قائمة التسوق...',
      );

      final shoppingListService = await ref.read(shoppingListServiceProvider.future);

      // Use unified executeGeneration method for both new and replacement
      final result = await shoppingListService.executeGeneration(
        startDate,
        endDate,
        shoppingIdToReplace: shoppingIdToReplace,
      );

      final success = result.success;
      if (success) {
        // Update state to show background generation is in progress
        state = state.copyWith(
          isGenerating: false, // Clear immediate generation state
          isBackgroundGenerating: true, // Keep background generation state
          generatingMessage: 'جاري $action قائمة التسوق في الخلفية...',
        );

        AppLogger.info('ShoppingListOverviewNotifier: Successfully started shopping list $action');
      } else {
        state = state.copyWith(
          isGenerating: false,
          isBackgroundGenerating: false,
          generatingMessage: null,
          error: result.error ?? 'فشل في $action قائمة التسوق',
        );
      }
    } catch (e) {
      final action = shoppingIdToReplace != null ? 'استبدال' : 'إنشاء';
      state = state.copyWith(
        isGenerating: false,
        isBackgroundGenerating: false,
        generatingMessage: null,
        error: 'فشل في $action قائمة التسوق: $e',
      );
      AppLogger.error('ShoppingListOverviewNotifier: Error executing generation: $e');
    }
  }

  /// Delete a shopping list
  Future<void> deleteShoppingList(String shoppingListId) async {
    try {
      final shoppingListService = await ref.read(shoppingListServiceProvider.future);
      final result = await shoppingListService.deleteShoppingList(shoppingListId);

      if (result.success) {
        // Reload the shopping lists to update UI
        await loadShoppingLists();
      } else {
        state = state.copyWith(error: result.error);
      }
    } catch (e) {
      AppLogger.error('ShoppingListOverviewNotifier: Error deleting shopping list: $e');
      state = state.copyWith(error: 'فشل في حذف قائمة التسوق: $e');
      rethrow;
    }
  }



  /// Set up periodic check for expired loading flags (5 minute timeout)
  void _setupLoadingFlagTimeoutCheck() {
    _loadingFlagTimeoutTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      try {
        final shoppingListService = await ref.read(shoppingListServiceProvider.future);

        if (shoppingListService.isLoadingFlagExpired()) {
          AppLogger.info('ShoppingListOverviewNotifier: Loading flag expired, clearing state');

          await shoppingListService.removeLoadingFlag();

          state = state.copyWith(
            isBackgroundGenerating: false,
            generatingMessage: null,
          );
        }
      } catch (e) {
        AppLogger.error('ShoppingListOverviewNotifier: Error checking loading flag timeout: $e');
      }
    });
  }

}

/// Provider for shopping lists loading state
@riverpod
bool isShoppingListsLoading(Ref ref) {
  final overviewState = ref.watch(shoppingListsNotifierProvider);
  return overviewState.isLoading;
}

/// Provider for shopping lists generating state
@riverpod
bool isShoppingListGenerating(Ref ref) {
  final overviewState = ref.watch(shoppingListsNotifierProvider);
  return overviewState.isGenerating;
}

/// Provider for background shopping list generation state
@riverpod
bool isBackgroundShoppingListGenerating(Ref ref) {
  final overviewState = ref.watch(shoppingListsNotifierProvider);
  return overviewState.isBackgroundGenerating;
}
