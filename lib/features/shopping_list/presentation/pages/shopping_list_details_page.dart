import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/shopping_list_details_provider.dart';
import '../../data/models/shopping_list_models.dart';
import '../widgets/shopping_list_details_loading_widget.dart';
import '../widgets/shopping_list_details_error_widget.dart';
import '../widgets/shopping_list_details_empty_widget.dart';
import '../widgets/shopping_list_header_card_widget.dart';
import '../widgets/shopping_list_category_card_widget.dart';
import '../widgets/shopping_list_details_dialogs.dart';

class ShoppingListDetailsPage extends ConsumerStatefulWidget {
  final ShoppingList? shoppingList;

  const ShoppingListDetailsPage({
    super.key,
    this.shoppingList,
  });

  @override
  ConsumerState<ShoppingListDetailsPage> createState() => _ShoppingListDetailsPageState();
}

class _ShoppingListDetailsPageState extends ConsumerState<ShoppingListDetailsPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializePage();
    });
  }

  Future<void> _initializePage() async {
    // If a shopping list was passed as parameter, use it directly
    if (widget.shoppingList != null) {
      // Set the shopping list in the provider
      ref.read(shoppingListDetailsNotifierProvider.notifier).setCurrentShoppingList(widget.shoppingList!);
      return;
    }

    // Otherwise, try to load existing shopping list first
    await ref.read(shoppingListDetailsNotifierProvider.notifier).loadShoppingListFromStorage();

    // If no existing list, generate a new one
    final currentList = ref.read(currentShoppingListDetailsProvider);
    if (currentList == null) {
      await ref.read(shoppingListDetailsNotifierProvider.notifier).generateShoppingListFromDateRange(
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 7)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final shoppingListState = ref.watch(shoppingListDetailsNotifierProvider);
    final currentList = ref.watch(currentShoppingListDetailsProvider);
    final isLoading = ref.watch(isShoppingListDetailsLoadingProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('قائمة التسوق'),
        backgroundColor: Colors.orange.shade50,
        foregroundColor: Colors.orange.shade800,
        elevation: 0,
        actions: [
          if (currentList != null)
            IconButton(
              icon: Icon(Icons.refresh),
              tooltip: 'إنشاء قائمة جديدة',
              onPressed: () => _showRegenerateDialog(),
            ),
        ],
      ),
      backgroundColor: Colors.orange.shade50,
      body: _buildBody(isLoading, currentList, shoppingListState.error),
    );
  }

  Widget _buildBody(bool isLoading, ShoppingList? currentList, String? error) {
    if (isLoading) {
      return const ShoppingListDetailsLoadingWidget();
    }

    if (error != null) {
      return ShoppingListDetailsErrorWidget(
        error: error,
        onRetry: () => ref.read(shoppingListDetailsNotifierProvider.notifier).generateShoppingListFromDateRange(
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 7)),
        ),
      );
    }

    if (currentList == null) {
      return ShoppingListDetailsEmptyWidget(
        onGeneratePressed: () => ref.read(shoppingListDetailsNotifierProvider.notifier).generateShoppingListFromDateRange(
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 7)),
        ),
      );
    }

    return _buildShoppingListContent(currentList);
  }



  Widget _buildShoppingListContent(ShoppingList shoppingList) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date and Progress Card
          ShoppingListHeaderCardWidget(shoppingList: shoppingList),

          const SizedBox(height: 20),

          // Shopping List Categories
          ...shoppingList.categories.map((category) =>
            ShoppingListCategoryCardWidget(
              category: category,
              onItemToggle: (itemId) => ref.read(shoppingListDetailsNotifierProvider.notifier).toggleItemPurchase(itemId),
            )).toList(),
        ],
      ),
    );
  }





  void _showRegenerateDialog() {
    ShoppingListDetailsDialogs.showRegenerateConfirmation(context).then((confirmed) {
      if (confirmed == true) {
        ref.read(shoppingListDetailsNotifierProvider.notifier).generateShoppingListFromDateRange(
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 7)),
        );
      }
    });
  }
}
