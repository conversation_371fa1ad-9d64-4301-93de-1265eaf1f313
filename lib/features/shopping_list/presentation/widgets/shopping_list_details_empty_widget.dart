import 'package:flutter/material.dart';

/// Widget that displays an empty state for shopping list details
class ShoppingListDetailsEmptyWidget extends StatelessWidget {
  final VoidCallback onGeneratePressed;

  const ShoppingListDetailsEmptyWidget({
    super.key,
    required this.onGeneratePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.orange.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد قائمة تسوق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.orange.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على خطة وجبات لإنشاء قائمة التسوق',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.orange.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onGeneratePressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade600,
                foregroundColor: Colors.white,
              ),
              icon: const Icon(Icons.add_shopping_cart),
              label: const Text('إنشاء قائمة تسوق'),
            ),
          ],
        ),
      ),
    );
  }
}
