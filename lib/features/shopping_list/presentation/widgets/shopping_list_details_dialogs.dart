import 'package:flutter/material.dart';

/// Utility class for showing dialogs in shopping list details
class ShoppingListDetailsDialogs {
  /// Shows a regenerate confirmation dialog
  static Future<bool?> showRegenerateConfirmation(
    BuildContext context,
  ) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء قائمة جديدة'),
        content: const Text('هل تريد إنشاء قائمة تسوق جديدة؟ سيتم حذف القائمة الحالية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
            ),
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }
}
