import 'package:flutter/material.dart';

/// Widget that displays a loading state for shopping lists
class ShoppingListsLoadingWidget extends StatelessWidget {
  const ShoppingListsLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade600),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل قوائم التسوق...',
            style: TextStyle(
              color: Colors.orange.shade800,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
