import 'package:flutter/material.dart';
import '../../data/models/shopping_list_models.dart';
import 'shopping_list_item_widget.dart';

/// Widget that displays a shopping list category with its items
class ShoppingListCategoryCardWidget extends StatelessWidget {
  final ShoppingListCategory category;
  final Function(String itemId) onItemToggle;

  const ShoppingListCategoryCardWidget({
    super.key,
    required this.category,
    required this.onItemToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                if (category.icon != null)
                  Text(
                    category.icon!,
                    style: const TextStyle(fontSize: 24),
                  ),
                if (category.icon != null) const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    category.arabicName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade800,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: category.isCompleted
                        ? Colors.green.shade100
                        : Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    category.progressText,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: category.isCompleted
                          ? Colors.green.shade700
                          : Colors.orange.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Category Items
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: category.items.map((item) =>
                ShoppingListItemWidget(
                  item: item,
                  onToggle: () => onItemToggle(item.id),
                )).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
