import 'package:flutter/material.dart';

/// Widget that displays a loading state for shopping list details
class ShoppingListDetailsLoadingWidget extends StatelessWidget {
  const ShoppingListDetailsLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade600),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري إنشاء قائمة التسوق...',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.orange.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يتم تحليل خطة الوجبات وإعداد قائمة المشتريات',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.orange.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
