import 'package:flutter/material.dart';
import '../../data/models/shopping_list_models.dart';
import '../../providers/shopping_lists_provider.dart';

/// Utility class for showing dialogs in shopping lists
class ShoppingListsDialogs {
  /// Shows a delete confirmation dialog
  static Future<bool?> showDeleteConfirmation(
    BuildContext context,
    ShoppingList shoppingList,
  ) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف قائمة التسوق'),
        content: Text('هل أنت متأكد من حذف قائمة التسوق "${shoppingList.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red.shade600,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// Shows a duplicate confirmation dialog
  static Future<bool?> showDuplicateConfirmation(
    BuildContext context,
    PendingGeneration pendingGeneration,
  ) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قائمة تسوق موجودة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('توجد قائمة تسوق بالفعل لنفس الفترة الزمنية:'),
            const SizedBox(height: 8),
            Text(
              '"${pendingGeneration.duplicateListTitle}"',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('هل تريد إنشاء قائمة تسوق جديدة؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.orange.shade600,
            ),
            child: const Text('إنشاء قائمة جديدة'),
          ),
        ],
      ),
    );
  }
}
