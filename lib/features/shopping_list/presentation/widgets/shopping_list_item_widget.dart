import 'package:flutter/material.dart';
import '../../data/models/shopping_list_models.dart';

/// Widget that displays a single shopping list item with checkbox
class ShoppingListItemWidget extends StatelessWidget {
  final ShoppingListItem item;
  final VoidCallback onToggle;

  const ShoppingListItemWidget({
    super.key,
    required this.item,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onToggle,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: item.isPurchased
              ? Colors.green.shade50
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: item.isPurchased
                ? Colors.green.shade200
                : Colors.grey.shade200,
          ),
        ),
        child: Row(
          children: [
            // Checkbox (visual indicator only, entire item is clickable)
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: item.isPurchased
                    ? Colors.green.shade600
                    : Colors.transparent,
                border: Border.all(
                  color: item.isPurchased
                      ? Colors.green.shade600
                      : Colors.grey.shade400,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child: item.isPurchased
                  ? const Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),

            const SizedBox(width: 12),

            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: item.isPurchased
                          ? Colors.green.shade700
                          : Colors.grey.shade800,
                      decoration: item.isPurchased
                          ? TextDecoration.lineThrough
                          : null,
                    ),
                  ),
                  Text(
                    '${item.quantity} ${item.unit}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: item.isPurchased
                          ? Colors.green.shade600
                          : Colors.grey.shade600,
                    ),
                  ),
                  if (item.notes != null && item.notes!.isNotEmpty)
                    Text(
                      item.notes!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade500,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
