import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/meal_plan_request.dart';

/// Enum to represent the status of a day relative to today
enum DayStatus { past, current, future }

/// Custom SliverPersistentHeaderDelegate for sticky day navigation header
class StickyDayNavigationHeaderDelegate extends SliverPersistentHeaderDelegate {
  final int totalDays;
  final int selectedDayIndex;
  final List<DayMealPlan> days;
  final Function(int) onDayChanged;
  final VoidCallback onGenerateExtraDays;

  const StickyDayNavigationHeaderDelegate({
    required this.totalDays,
    required this.selectedDayIndex,
    required this.days,
    required this.onDayChanged,
    required this.onGenerateExtraDays,
  });

  @override
  double get minExtent => 80.0; // Minimum height when collapsed

  @override
  double get maxExtent => 80.0; // Maximum height when expanded

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          height: maxExtent,
          decoration: BoxDecoration(
            color: _getHeaderBackgroundColor(context),
            border: Border(
              bottom: BorderSide(
                color: Colors.black.withOpacity(0.1),
                width: 0.5,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                offset: const Offset(0, 2),
                blurRadius: 8,
              ),
            ],
          ),
          child: DayNavigationHeader(
            totalDays: totalDays,
            selectedDayIndex: selectedDayIndex,
            days: days,
            onDayChanged: onDayChanged,
            onGenerateExtraDays: onGenerateExtraDays,
          ),
        ),
      ),
    );
  }

  /// Get appropriate background color for the header with frosted glass effect
  Color _getHeaderBackgroundColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    if (brightness == Brightness.dark) {
      return Colors.black.withOpacity(0.8);
    } else {
      return Colors.white.withOpacity(0.8);
    }
  }

  @override
  bool shouldRebuild(covariant StickyDayNavigationHeaderDelegate oldDelegate) {
    return totalDays != oldDelegate.totalDays ||
           selectedDayIndex != oldDelegate.selectedDayIndex ||
           days != oldDelegate.days;
  }
}

class DayNavigationHeader extends StatelessWidget {
  final int totalDays;
  final int selectedDayIndex;
  final List<DayMealPlan> days;
  final Function(int) onDayChanged;
  final VoidCallback onGenerateExtraDays;

  const DayNavigationHeader({
    super.key,
    required this.totalDays,
    required this.selectedDayIndex,
    required this.days,
    required this.onDayChanged,
    required this.onGenerateExtraDays,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Line 1: Navigation arrows with centered date display
          Row(
            children: [
              // Previous Day Button
              _buildCompactNavButton(
                icon: Icons.arrow_back_ios,
                enabled: selectedDayIndex > 0,
                onPressed: selectedDayIndex > 0
                    ? () => onDayChanged(selectedDayIndex - 1)
                    : null,
              ),

              // Date Display
              Expanded(
                child: _buildCompactDateDisplay(context),
              ),

              // Next Day Button
              _buildCompactNavButton(
                icon: Icons.arrow_forward_ios,
                enabled: selectedDayIndex < totalDays,
                onPressed: selectedDayIndex < totalDays
                    ? () {
                        if (selectedDayIndex == totalDays - 1) {
                          onGenerateExtraDays();
                        } else {
                          onDayChanged(selectedDayIndex + 1);
                        }
                      }
                    : null,
              ),
            ],
          ),

          const SizedBox(height: 6),

          // Line 2: Day indicator dots with "Today" quick-navigation button
          _buildCompactDayIndicators(context),
        ],
      ),
    );
  }

  /// Build compact navigation button
  Widget _buildCompactNavButton({
    required IconData icon,
    required bool enabled,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: 32,
      height: 32,
      child: IconButton(
        padding: EdgeInsets.zero,
        onPressed: onPressed,
        icon: Icon(
          icon,
          size: 18,
          color: enabled ? AppColors.primary : Colors.grey.shade400,
        ),
      ),
    );
  }

  /// Build compact date display with relative date logic
  Widget _buildCompactDateDisplay(BuildContext context) {
    if (selectedDayIndex >= totalDays) {
      // Extra days generation view
      return Text(
        'إضافة أيام جديدة',
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
          fontSize: 16,
        ),
        textAlign: TextAlign.center,
      );
    }

    final selectedDay = days[selectedDayIndex];
    final dayStatus = _getDayStatus(selectedDay);
    final relativeDateText = _getRelativeDateText(selectedDay.date);

    Color titleColor = _getDayStatusColor(dayStatus);

    return Text(
      relativeDateText,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: titleColor,
        fontSize: 16,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Get relative date text based on the date
  String _getRelativeDateText(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    final difference = targetDate.difference(today).inDays;

    if (difference == 0) {
      return 'اليوم';
    } else if (difference == 1) {
      return 'غداً';
    } else if (difference == -1) {
      return 'أمس';
    } else if (difference > 1 && difference <= 7) {
      return 'بعد ${difference} ${difference == 2 ? 'يومين' : 'أيام'}';
    } else if (difference < -1 && difference >= -7) {
      final absDiff = difference.abs();
      return 'منذ ${absDiff} ${absDiff == 2 ? 'يومين' : 'أيام'}';
    } else {
      // Beyond 7 days, show formatted date
      final arabicDateFormat = DateFormat('EEEE، d MMMM', 'ar');
      return arabicDateFormat.format(date);
    }
  }

  /// Get color based on day status
  Color _getDayStatusColor(DayStatus status) {
    switch (status) {
      case DayStatus.current:
        return AppColors.primary;
      case DayStatus.past:
        return Colors.grey.shade600;
      case DayStatus.future:
        return Colors.blue.shade600;
    }
  }

  /// Build compact day indicators with "Today" quick-navigation button
  Widget _buildCompactDayIndicators(BuildContext context) {
    final currentDayIndex = _getCurrentDayIndex();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // "Today" quick-navigation button
        if (currentDayIndex >= 0 && currentDayIndex < totalDays) ...[
          GestureDetector(
            onTap: () => onDayChanged(currentDayIndex),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: selectedDayIndex == currentDayIndex
                    ? AppColors.primary
                    : AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                'اليوم',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: selectedDayIndex == currentDayIndex
                      ? Colors.white
                      : AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],

        // Day indicator dots
        for (int i = 0; i < totalDays; i++) ...[
          GestureDetector(
            onTap: () => onDayChanged(i),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: i == selectedDayIndex
                    ? _getDayIndicatorColor(i)
                    : Colors.grey.shade300,
              ),
            ),
          ),
        ],

        // Extra days indicator
        if (totalDays > 0) ...[
          const SizedBox(width: 6),
          GestureDetector(
            onTap: onGenerateExtraDays,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              width: 16,
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: selectedDayIndex >= totalDays
                    ? AppColors.primary
                    : Colors.grey.shade300,
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 0.5,
                ),
              ),
              child: Icon(
                Icons.add,
                size: 6,
                color: selectedDayIndex >= totalDays
                    ? Colors.white
                    : Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Get the index of the current day (today) in the meal plan
  int _getCurrentDayIndex() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (int i = 0; i < days.length; i++) {
      final day = days[i];
      final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

      if (dayDateOnly.isAtSameMomentAs(today)) {
        return i;
      }
    }

    return -1; // Today not found in the meal plan
  }

  Color _getDayIndicatorColor(int dayIndex) {
    if (dayIndex >= days.length) return AppColors.primary;
    
    final day = days[dayIndex];
    final dayStatus = _getDayStatus(day);
    
    switch (dayStatus) {
      case DayStatus.current:
        return AppColors.primary;
      case DayStatus.past:
        return Colors.grey.shade600;
      case DayStatus.future:
        return Colors.blue.shade600;
    }
  }

  DayStatus _getDayStatus(DayMealPlan day) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

    if (dayDateOnly.isBefore(today)) {
      return DayStatus.past;
    } else if (dayDateOnly.isAtSameMomentAs(today)) {
      return DayStatus.current;
    } else {
      return DayStatus.future;
    }
  }
}
