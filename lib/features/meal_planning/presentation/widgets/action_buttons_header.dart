import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../providers/home_layout_provider.dart';

class ActionButtonsHeader extends ConsumerWidget {
  const ActionButtonsHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final layoutState = ref.watch(homeLayoutNotifierProvider);
    final isGridMode = layoutState.layoutMode == HomeLayoutMode.grid;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Title Section (right side)
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الإجراءات السريعة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.right,
              ),
              const SizedBox(height: 4),
              Text(
                'أدوات مساعدة لتتبع نظامك الغذائي',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.right,
              ),
            ],
          ),
        ),

        // Layout Toggle Icon (left side)
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: IconButton(
            onPressed: () {
              ref.read(homeLayoutNotifierProvider.notifier).toggleLayoutMode();
            },
            icon: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return RotationTransition(
                  turns: animation,
                  child: child,
                );
              },
              child: Icon(
                isGridMode ? Icons.view_list : Icons.grid_view,
                key: ValueKey(isGridMode),
                color: AppColors.primary,
                size: 24,
              ),
            ),
            tooltip: isGridMode ? 'عرض قائمة' : 'عرض شبكة',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.primary.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.all(12),
            ),
          ),
        ),
      ],
    );
  }
}
