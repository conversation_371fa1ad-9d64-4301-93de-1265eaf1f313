import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/auto_height_page_view.dart';
import '../../data/models/meal_plan_request.dart';
import 'initial_generation_card.dart';
import 'day_meal_card.dart';
import 'generate_extra_days_card.dart';
import 'action_buttons_section.dart';

class MealPlanPageView extends ConsumerStatefulWidget {
  final MealPlanResponse? plan;
  final Function(GeneratedMeal meal, DayMealPlan dayMealPlan) onMealTap;

  const MealPlanPageView({
    super.key,
    required this.plan,
    required this.onMealTap,
  });

  @override
  ConsumerState<MealPlanPageView> createState() => _MealPlanPageViewState();
}

class _MealPlanPageViewState extends ConsumerState<MealPlanPageView> {
  late PageController _pageController;
  int _currentPageIndex = 0;
  bool _hasAutoFocused = false; // Track if we've already auto-focused

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // Auto-focus on current day after loading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _autoFocusOnCurrentDay();
    });
  }

  @override
  void didUpdateWidget(MealPlanPageView oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // If plan changed from null to non-null, auto-focus on current day
    if (oldWidget.plan == null && widget.plan != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _autoFocusOnCurrentDay();
      });
    }
  }

  /// Auto-focus the PageView on the current day (only once per session)
  void _autoFocusOnCurrentDay() {
    // Only auto-focus once per session to avoid interfering with manual navigation
    if (_hasAutoFocused || widget.plan == null) return;

    final plan = widget.plan!;
    if (plan.mealPlan.days.isEmpty) return;

    final currentDayIndex = _getCurrentDayIndex(plan);

    // Only navigate if we're not already on the current day and the day exists
    if (currentDayIndex != _currentPageIndex &&
        currentDayIndex < plan.mealPlan.days.length) {
      _pageController.animateToPage(
        currentDayIndex,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }

    // Mark that we've auto-focused
    _hasAutoFocused = true;
  }

  /// Get the index of the current day in the meal plan
  int _getCurrentDayIndex(MealPlanResponse plan) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Find the day that matches today's date
    for (int i = 0; i < plan.mealPlan.days.length; i++) {
      final day = plan.mealPlan.days[i];
      final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

      if (dayDateOnly.isAtSameMomentAs(today)) {
        return i;
      }
    }

    // If today is not found, return the first day
    return 0;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If no plan exists, show only the generation page
    if (widget.plan == null) {
      return Column(
        children: [
          AutoHeightPageView(
            itemCount: 1,
            animationDuration: const Duration(milliseconds: 400),
            animationCurve: Curves.easeInOut,
            itemBuilder: (context, index) {
              return const InitialGenerationCard();
            },
          ),
        ],
      );
    }

    final totalDays = widget.plan!.mealPlan.days.length;

    return Column(
      children: [
        // Page indicator and navigation
        _buildPageNavigation(totalDays),

        const SizedBox(height: 16),

        // Auto-height PageView for meal plan days
        AutoHeightPageView(
          controller: _pageController,
          onPageChanged: (index) {
            setState(() {
              _currentPageIndex = index;
            });
          },
          itemCount: totalDays + 1, // Add one for the confirmation page
          animationDuration: const Duration(milliseconds: 400),
          animationCurve: Curves.easeInOut,
          itemBuilder: (context, index) {
            if (index < totalDays) {
              // Regular day card
              final day = widget.plan!.mealPlan.days[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: DayMealCard(
                  day: day,
                  onMealTap: widget.onMealTap,
                ),
              );
            } else {
              // Confirmation page for generating extra days
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: GenerateExtraDaysCard(
                  onBackPressed: () {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  },
                ),
              );
            }
          },
        ),

        // Day counter and info
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            _currentPageIndex < totalDays
                ? 'اليوم ${_currentPageIndex + 1} من ${totalDays}'
                : 'إضافة أيام جديدة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        const SizedBox(height: 16),

        // Action Buttons (only show when meal plan exists)
        const ActionButtonsSection(),
      ],
    );
  }

  Widget _buildPageNavigation(int totalDays) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Navigation button (left)
        IconButton(
          onPressed: _currentPageIndex > 0
              ? () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              : null,
          icon: Icon(
            Icons.arrow_back_ios,
            color: _currentPageIndex > 0 ? AppColors.primary : Colors.grey,
          ),
        ),

        // Page indicator
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              for (int i = 0; i < totalDays + 1; i++) // +1 for confirmation page
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: i == _currentPageIndex
                        ? AppColors.primary
                        : Colors.grey.shade300,
                  ),
                ),
            ],
          ),
        ),

        // Navigation button (right)
        IconButton(
          onPressed: _currentPageIndex < totalDays
              ? () {
                  _pageController.nextPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              : null,
          icon: Icon(
            Icons.arrow_forward_ios,
            color: _currentPageIndex < totalDays
                ? AppColors.primary
                : Colors.grey,
          ),
        ),
      ],
    );
  }
}
