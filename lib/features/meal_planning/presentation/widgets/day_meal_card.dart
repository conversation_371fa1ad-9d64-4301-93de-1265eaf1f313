import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../data/models/meal_plan_request.dart';
import 'nutrition_item_widget.dart';
import 'meal_summary_item.dart';

/// Enum to represent the status of a day relative to today
enum DayStatus { past, current, future }

class DayMealCard extends ConsumerWidget {
  final DayMealPlan day;
  final Function(GeneratedMeal meal, DayMealPlan dayMealPlan) onMealTap;

  const DayMealCard({
    super.key,
    required this.day,
    required this.onMealTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');
    final dayStatus = _getDayStatus(day);

    // Determine header gradient based on day status
    Gradient headerGradient;
    switch (dayStatus) {
      case DayStatus.current:
        headerGradient =
            AppColors.primaryGradient; // Keep green for current day
        break;
      case DayStatus.past:
      case DayStatus.future:
        headerGradient = LinearGradient(
          colors: [Colors.grey.shade400, Colors.grey.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
        break;
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Day Header (Fixed)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: headerGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${day.date.day}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'اليوم ${day.date.day}',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          Text(
                            arabicDateFormat.format(day.date),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.white70,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Nutrition Summary
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      NutritionItemWidget(
                        label: 'السعرات',
                        value: '${day.totalNutrition.calories}',
                        unit: 'سعرة',
                      ),
                      NutritionItemWidget(
                        label: 'البروتين',
                        value: '${day.totalNutrition.protein.toStringAsFixed(0)}',
                        unit: 'جم',
                      ),
                      NutritionItemWidget(
                        label: 'الكربوهيدرات',
                        value: '${day.totalNutrition.carbs.toStringAsFixed(0)}',
                        unit: 'جم',
                      ),
                      NutritionItemWidget(
                        label: 'الدهون',
                        value: '${day.totalNutrition.fat.toStringAsFixed(0)}',
                        unit: 'جم',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Meals Summary
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الوجبات (${day.meals.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),

                // Meals list without scrolling
                ...day.meals
                    .map((meal) => MealSummaryItem(
                          meal: meal,
                          dayMealPlan: day,
                          onTap: () => onMealTap(meal, day),
                        )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Check if a day is in the past, current, or future
  DayStatus _getDayStatus(DayMealPlan day) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dayDateOnly = DateTime(day.date.year, day.date.month, day.date.day);

    if (dayDateOnly.isBefore(today)) {
      return DayStatus.past;
    } else if (dayDateOnly.isAtSameMomentAs(today)) {
      return DayStatus.current;
    } else {
      return DayStatus.future;
    }
  }
}
