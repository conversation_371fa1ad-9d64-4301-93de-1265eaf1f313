import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

/// Utility class for meal type colors and gradients
class MealTypeColors {
  /// Get the primary color for a meal type
  static Color getMealTypeColor(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return Colors.orange;
      case 'lunch':
        return Colors.blue;
      case 'dinner':
        return Colors.indigo;
      case 'snack':
        return Colors.green;
      default:
        return AppColors.primary;
    }
  }

  /// Get the gradient for a meal type based on its primary color
  static LinearGradient getMealTypeGradient(String mealType) {
    final primaryColor = getMealTypeColor(mealType);
    
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF9A56), Color(0xFFFF6B35)],
        );
      case 'lunch':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2196F3), Color(0xFF1565C0)],
        );
      case 'dinner':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF3F51B5), Color(0xFF1A237E)],
        );
      case 'snack':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
        );
      default:
        return LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [primaryColor, primaryColor.withOpacity(0.8)],
        );
    }
  }

  /// Get a lighter version of the meal type color for backgrounds
  static Color getMealTypeLightColor(String mealType) {
    return getMealTypeColor(mealType).withOpacity(0.1);
  }

  /// Get a darker version of the meal type color for borders
  static Color getMealTypeBorderColor(String mealType) {
    return getMealTypeColor(mealType).withOpacity(0.3);
  }

  /// Get the meal icon for a meal type
  static IconData getMealTypeIcon(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return Icons.wb_sunny;
      case 'lunch':
        return Icons.wb_sunny_outlined;
      case 'dinner':
        return Icons.nightlight;
      case 'snack':
        return Icons.local_cafe;
      default:
        return Icons.restaurant;
    }
  }
}
