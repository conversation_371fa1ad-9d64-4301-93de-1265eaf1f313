import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'home_layout_provider.freezed.dart';
part 'home_layout_provider.g.dart';

/// Enum for home layout modes
enum HomeLayoutMode {
  list,
  grid,
}

/// Extension to convert enum to string and vice versa
extension HomeLayoutModeExtension on HomeLayoutMode {
  String get value {
    switch (this) {
      case HomeLayoutMode.list:
        return 'list';
      case HomeLayoutMode.grid:
        return 'grid';
    }
  }

  static HomeLayoutMode fromString(String value) {
    switch (value) {
      case 'list':
        return HomeLayoutMode.list;
      case 'grid':
        return HomeLayoutMode.grid;
      default:
        return HomeLayoutMode.grid; // Default fallback
    }
  }
}

/// State for home layout
@freezed
class HomeLayoutState with _$HomeLayoutState {
  const factory HomeLayoutState({
    @Default(HomeLayoutMode.grid) HomeLayoutMode layoutMode,
    @Default(false) bool isLoading,
    String? error,
  }) = _HomeLayoutState;
}

/// Notifier for managing home layout state
@riverpod
class HomeLayoutNotifier extends _$HomeLayoutNotifier {
  bool _isInitialized = false;

  @override
  HomeLayoutState build() {
    // Return initial state immediately
    // Load layout preference asynchronously after initialization
    if (!_isInitialized) {
      _isInitialized = true;
      Future.microtask(_loadLayoutPreference);
    }
    return const HomeLayoutState();
  }

  /// Load layout preference from local storage
  Future<void> _loadLayoutPreference() async {
    try {
      // Set loading state
      state = state.copyWith(isLoading: true, error: null);

      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final savedLayoutMode = localStorageService.loadHomeLayoutMode();

      if (savedLayoutMode != null) {
        final layoutMode = HomeLayoutModeExtension.fromString(savedLayoutMode);
        state = state.copyWith(
          layoutMode: layoutMode,
          isLoading: false,
        );
      } else {
        // No saved preference, use default (grid)
        state = state.copyWith(
          layoutMode: HomeLayoutMode.grid,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load layout preference: $e',
      );
    }
  }

  /// Toggle between list and grid layout modes
  Future<void> toggleLayoutMode() async {
    try {
      final newLayoutMode = state.layoutMode == HomeLayoutMode.list
          ? HomeLayoutMode.grid
          : HomeLayoutMode.list;

      // Update state immediately for responsive UI
      state = state.copyWith(layoutMode: newLayoutMode, error: null);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final success = await localStorageService.saveHomeLayoutMode(newLayoutMode.value);

      if (!success) {
        // Revert state if saving failed
        final revertedMode = newLayoutMode == HomeLayoutMode.list
            ? HomeLayoutMode.grid
            : HomeLayoutMode.list;
        state = state.copyWith(
          layoutMode: revertedMode,
          error: 'Failed to save layout preference',
        );
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to toggle layout: $e');
    }
  }

  /// Set specific layout mode
  Future<void> setLayoutMode(HomeLayoutMode layoutMode) async {
    try {
      // Update state immediately
      state = state.copyWith(layoutMode: layoutMode, error: null);

      // Save to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final success = await localStorageService.saveHomeLayoutMode(layoutMode.value);

      if (!success) {
        state = state.copyWith(error: 'Failed to save layout preference');
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to set layout mode: $e');
    }
  }

  /// Clear any error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for current layout mode (convenience provider)
@riverpod
HomeLayoutMode currentLayoutMode(Ref ref) {
  final layoutState = ref.watch(homeLayoutNotifierProvider);
  return layoutState.layoutMode;
}

/// Provider for checking if layout is in grid mode
@riverpod
bool isGridLayout(Ref ref) {
  final layoutMode = ref.watch(currentLayoutModeProvider);
  return layoutMode == HomeLayoutMode.grid;
}

/// Provider for checking if layout is in list mode
@riverpod
bool isListLayout(Ref ref) {
  final layoutMode = ref.watch(currentLayoutModeProvider);
  return layoutMode == HomeLayoutMode.list;
}
