import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../core/services/local_storage_service.dart';
import '../data/services/meal_plan_service.dart';
import '../../../core/utils/logger.dart';
import '../../../shared/providers/app_state_provider.dart';
import '../data/models/meal_plan_request.dart';
import '../data/services/meal_plan_service.dart';
import 'meal_consumption_provider.dart';

part '../providers/meal_plan_generation_provider.freezed.dart';
part '../providers/meal_plan_generation_provider.g.dart';

@freezed
class MealPlanGenerationState with _$MealPlanGenerationState {
  const factory MealPlanGenerationState({
    @Default(false) bool isLoading,
    @Default(false) bool isGenerating,
    MealPlanResponse? generatedPlan,
    String? error,
    @Default(MealPlanPreferences()) MealPlanPreferences preferences,
    @Default(3) int duration,
    @Default(false) bool showAdvancedSettings,
  }) = _MealPlanGenerationState;
}

@riverpod
class MealPlanGenerationNotifier extends _$MealPlanGenerationNotifier {
  @override
  MealPlanGenerationState build() {
    return const MealPlanGenerationState();
  }

  /// Update diet type
  void updateDietType(DietType dietType) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietType: dietType),
    );
  }

  /// Update calorie goal
  void updateCalorieGoal(int calories) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(calorieGoal: calories),
    );
  }

  /// Update protein goal
  void updateProteinGoal(int protein) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(proteinGoal: protein),
    );
  }

  /// Update carbs goal
  void updateCarbsGoal(int carbs) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(carbsGoal: carbs),
    );
  }

  /// Update fat goal
  void updateFatGoal(int fat) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(fatGoal: fat),
    );
  }

  /// Update meals per day
  void updateMealsPerDay(int meals) {
    state = state.copyWith(
      preferences: state.preferences.copyWith(mealsPerDay: meals),
    );
  }

  /// Update duration
  void updateDuration(int duration) {
    state = state.copyWith(duration: duration);
  }

  /// Toggle advanced settings visibility
  void toggleAdvancedSettings() {
    state = state.copyWith(showAdvancedSettings: !state.showAdvancedSettings);
  }

  /// Add dietary restriction
  void addDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    if (!restrictions.contains(restriction)) {
      restrictions.add(restriction);
      state = state.copyWith(
        preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
      );
    }
  }

  /// Remove dietary restriction
  void removeDietaryRestriction(String restriction) {
    final restrictions = List<String>.from(state.preferences.dietaryRestrictions);
    restrictions.remove(restriction);
    state = state.copyWith(
      preferences: state.preferences.copyWith(dietaryRestrictions: restrictions),
    );
  }

  /// Add allergy
  void addAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    if (!allergies.contains(allergy)) {
      allergies.add(allergy);
      state = state.copyWith(
        preferences: state.preferences.copyWith(allergies: allergies),
      );
    }
  }

  /// Remove allergy
  void removeAllergy(String allergy) {
    final allergies = List<String>.from(state.preferences.allergies);
    allergies.remove(allergy);
    state = state.copyWith(
      preferences: state.preferences.copyWith(allergies: allergies),
    );
  }

  /// Add cuisine preference
  void addCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    if (!cuisines.contains(cuisine)) {
      cuisines.add(cuisine);
      state = state.copyWith(
        preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
      );
    }
  }

  /// Remove cuisine preference
  void removeCuisinePreference(String cuisine) {
    final cuisines = List<String>.from(state.preferences.cuisinePreferences);
    cuisines.remove(cuisine);
    state = state.copyWith(
      preferences: state.preferences.copyWith(cuisinePreferences: cuisines),
    );
  }

  /// Generate meal plan
  /// Preferences are automatically retrieved from user profile in Firestore
  Future<void> generateMealPlan() async {
    // Testing flag - set to true to use mock data instead of API call
    const bool useMockData = false;

    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return;
    }

    state = state.copyWith(
      isGenerating: true,
      error: null,
      generatedPlan: null,
    );

    try {
      MealPlanResponse? response;

      if (useMockData) {
        // Return mock data for testing
        response = _createMockMealPlanResponse();
      } else {
        final mealPlanService = await ref.read(mealPlanServiceProvider.future);
        final apiResponse = await mealPlanService.generateMealPlan();

        // Check if this is an async processing response
        if (apiResponse is AsyncMealPlanResponse) {
          // API started background processing successfully
          // Set loading flag and wait for real-time sync to handle the results
          final localStorageService = await ref.read(localStorageServiceProvider.future);
          await localStorageService.saveMealGenerationLoading(true);

          state = state.copyWith(
            isGenerating: false,
            error: null,
          );
          return; // Exit early, data will come via Firestore sync
        }

        // Handle synchronous response with meal plan data
        final mealPlanResponse = apiResponse as MealPlanResponse;

        // Apply sequential dates starting from today to the generated days
        final today = DateTime.now();
        final datedDays = mealPlanResponse.mealPlan.days.asMap().entries.map((entry) {
          final index = entry.key;
          final day = entry.value;
          final dayDate = today.add(Duration(days: index));

          return day.copyWith(date: dayDate);
        }).toList();

        response = mealPlanResponse.copyWith(
          mealPlan: mealPlanResponse.mealPlan.copyWith(days: datedDays),
        );
      }

      // Save meal plan to local storage (extract only the meal plan data, not the wrapper)
      // Only save if we have actual meal plan data (not async processing response)
      if (response != null) {
        try {
          final localStorageService = await ref.read(localStorageServiceProvider.future);
          await localStorageService.saveCurrentMealPlan(response.mealPlan.toJson());

          // Sync to Firestore if user is authenticated
          final mealPlanService = await ref.read(mealPlanServiceProvider.future);
          await mealPlanService.syncCurrentMealPlanToFirestore();

          // Load consumption statuses for this meal plan
          final consumptionNotifier = ref.read(mealConsumptionNotifierProvider.notifier);
          await consumptionNotifier.loadConsumptionStatuses(_generateMealPlanId(response));
        } catch (storageError) {
          // Log storage error but don't fail the generation
          print('Failed to save meal plan to local storage: $storageError');
        }
      }

      state = state.copyWith(
        isGenerating: false,
        generatedPlan: response,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  /// Generate extra 3 days for the existing meal plan
  Future<void> generateExtraDays() async {
    const bool useMockData = false;

    final currentPlan = state.generatedPlan;
    if (currentPlan == null) {
      state = state.copyWith(error: 'No existing meal plan to extend');
      return;
    }

    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return;
    }

    state = state.copyWith(isGenerating: true, error: null);

    // Set loading flag in local storage
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveMealGenerationLoading(true);
    } catch (e) {
      print('Failed to save loading flag: $e');
    }

    try {
      if (useMockData) {
        // Generate mock extra days
        final extraDays = _createMockExtraDays(currentPlan.mealPlan.days.length);

        // Calculate starting date for new days based on existing plan
        DateTime startingDate;
        if (currentPlan.mealPlan.days.isNotEmpty) {
          // Start from the day after the last existing day
          final lastExistingDay = currentPlan.mealPlan.days.last;
          startingDate = lastExistingDay.date.add(const Duration(days: 1));
        } else {
          // If no existing days, start from today
          startingDate = DateTime.now();
        }

        // Apply sequential dates to the generated extra days
        final datedExtraDays = extraDays.asMap().entries.map((entry) {
          final index = entry.key;
          final day = entry.value;
          final dayDate = startingDate.add(Duration(days: index));

          return day.copyWith(date: dayDate);
        }).toList();

        // Add extra days to existing plan
        final updatedDays = [...currentPlan.mealPlan.days, ...datedExtraDays];
        final updatedPlan = currentPlan.copyWith(
          mealPlan: currentPlan.mealPlan.copyWith(days: updatedDays),
        );

        // Save updated meal plan to local storage (extract only the meal plan data, not the wrapper)
        try {
          final localStorageService = await ref.read(localStorageServiceProvider.future);
          await localStorageService.saveCurrentMealPlan(updatedPlan.mealPlan.toJson());

          // Sync to Firestore if user is authenticated
          final mealPlanService = await ref.read(mealPlanServiceProvider.future);
          await mealPlanService.syncCurrentMealPlanToFirestore();
        } catch (storageError) {
          // Log storage error but don't fail the generation
          print('Failed to save updated meal plan to local storage: $storageError');
        }

        // Clear loading flag
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.clearMealGenerationLoading();

        state = state.copyWith(
          isGenerating: false,
          generatedPlan: updatedPlan,
        );
      } else {
        // Call API for background processing
        final mealPlanService = await ref.read(mealPlanServiceProvider.future);
        final response = await mealPlanService.generateMealPlan();

        // Check if this is an async processing response
        if (response is AsyncMealPlanResponse) {
          if (response.success) {
            // API started background processing successfully
            // Keep loading flag and wait for real-time sync to handle the results
            state = state.copyWith(
              isGenerating: false,
              error: null,
            );
          } else {
            // API failed to start background processing
            final localStorageService = await ref.read(localStorageServiceProvider.future);
            await localStorageService.clearMealGenerationLoading();

            state = state.copyWith(
              isGenerating: false,
              error: 'فشل في بدء إنشاء الوجبات الإضافية',
            );
          }
        } else {
          // Handle unexpected synchronous response for extra days
          final localStorageService = await ref.read(localStorageServiceProvider.future);
          await localStorageService.clearMealGenerationLoading();

          state = state.copyWith(
            isGenerating: false,
            error: 'استجابة غير متوقعة من الخادم',
          );
        }
      }
    } catch (e) {
      // Clear loading flag on error
      try {
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.clearMealGenerationLoading();
      } catch (_) {}

      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  /// Creates a mock meal plan response for testing
  MealPlanResponse _createMockMealPlanResponse() {
    final now = DateTime.now();

    return MealPlanResponse(
      success: true,
      message: 'خطة وجبات تم إنشاؤها بنجاح (بيانات تجريبية)',
      mealPlan: GeneratedMealPlan(
        days: [
          // Day 1
          DayMealPlan(
            date: DateTime.now(),
            totalNutrition: const NutritionInfo(
              calories: 2100,
              protein: 120.0,
              carbs: 250.0,
              fat: 70.0,
              fiber: 35.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'إفطار',
                description: 'وجبة إفطار صحية ومغذية',
                type: 'breakfast',
                ingredients: [
                  const MealIngredient(name: '½ كوب شوفان', needsPreparation: true, instructions: ['اطبخ الشوفان مع الحليب لمدة 5 دقائق']),
                  const MealIngredient(name: '1 كوب حليب'),
                  const MealIngredient(name: '½ موزة'),
                  const MealIngredient(name: '2 ملعقة كبيرة توت'),
                  const MealIngredient(name: '1 ملعقة كبيرة عسل'),
                  const MealIngredient(name: '2 ملعقة كبيرة لوز'),
                  const MealIngredient(name: 'رشة قرفة'),
                ],
                nutrition: const NutritionInfo(
                  calories: 420,
                  protein: 18.0,
                  carbs: 65.0,
                  fat: 12.0,
                  fiber: 8.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'غداء',
                description: 'وجبة غداء متوازنة وصحية',
                type: 'lunch',
                ingredients: [
                  const MealIngredient(name: '150 جرام دجاج مشوي', needsPreparation: true, instructions: ['تبل الدجاج بالملح والفلفل', 'اشويه لمدة 15 دقيقة', 'قطعه شرائح']),
                  const MealIngredient(name: '½ كوب أرز', needsPreparation: true, instructions: ['اطبخ الأرز في الماء المغلي لمدة 20 دقيقة']),
                  const MealIngredient(name: '2 كوب خس'),
                  const MealIngredient(name: '½ كوب طماطم'),
                  const MealIngredient(name: '¼ كوب خيار'),
                  const MealIngredient(name: '¼ كوب جزر'),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة كبيرة عصير ليمون'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 580,
                  protein: 45.0,
                  carbs: 35.0,
                  fat: 28.0,
                  fiber: 12.0,
                ),
                preparationTime: 25,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'عشاء',
                description: 'وجبة عشاء صحية ولذيذة',
                type: 'dinner',
                ingredients: [
                  const MealIngredient(name: '150 جرام سلمون مشوي', needsPreparation: true, instructions: ['تبل السلمون بالملح والفلفل', 'اشويه لمدة 12 دقيقة']),
                  const MealIngredient(name: '1 كوب بروكلي', needsPreparation: true, instructions: ['اطبخ البروكلي على البخار لمدة 5 دقائق']),
                  const MealIngredient(name: '½ كوب جزر'),
                  const MealIngredient(name: '½ كوب كوسا'),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة صغيرة ثوم'),
                  const MealIngredient(name: 'عصير ½ ليمونة'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 450,
                  protein: 35.0,
                  carbs: 20.0,
                  fat: 25.0,
                  fiber: 8.0,
                ),
                preparationTime: 30,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'وجبة خفيفة',
                description: 'وجبة خفيفة صحية ولذيذة',
                type: 'snack',
                ingredients: [
                  const MealIngredient(name: '1 كوب زبادي'),
                  const MealIngredient(name: '½ كوب توت'),
                  const MealIngredient(name: '1 ملعقة كبيرة عسل'),
                  const MealIngredient(name: '2 ملعقة كبيرة جوز'),
                  const MealIngredient(name: 'رشة فانيليا'),
                ],
                nutrition: const NutritionInfo(
                  calories: 280,
                  protein: 20.0,
                  carbs: 25.0,
                  fat: 12.0,
                  fiber: 4.0,
                ),
                preparationTime: 5,
                difficulty: 'easy',
              ),
            ],
          ),
          // Day 2
          DayMealPlan(
            date: DateTime.now().add(const Duration(days: 1)),
            totalNutrition: const NutritionInfo(
              calories: 2050,
              protein: 115.0,
              carbs: 240.0,
              fat: 68.0,
              fiber: 32.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'إفطار',
                description: 'إفطار غني بالبروتين',
                type: 'breakfast',
                ingredients: [
                  const MealIngredient(name: '3 بيضات', needsPreparation: true, instructions: ['اخفق البيض مع الملح والفلفل', 'اطبخ في المقلاة لمدة 5 دقائق']),
                  const MealIngredient(name: '¼ كوب سبانخ'),
                  const MealIngredient(name: '¼ كوب طماطم'),
                  const MealIngredient(name: '¼ كوب فلفل'),
                  const MealIngredient(name: '30 جرام جبن'),
                  const MealIngredient(name: '1 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 380,
                  protein: 25.0,
                  carbs: 15.0,
                  fat: 26.0,
                  fiber: 4.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'غداء',
                description: 'وجبة غداء مغذية',
                type: 'lunch',
                ingredients: [
                  const MealIngredient(name: '1 كوب عدس', needsPreparation: true, instructions: ['اغسل العدس واطبخه لمدة 25 دقيقة']),
                  const MealIngredient(name: '2 كوب مرق'),
                  const MealIngredient(name: '½ كوب جزر'),
                  const MealIngredient(name: '½ كوب بصل'),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة صغيرة كمون'),
                  const MealIngredient(name: 'ملح وفلفل'),
                  const MealIngredient(name: '2 شريحة خبز'),
                ],
                nutrition: const NutritionInfo(
                  calories: 520,
                  protein: 28.0,
                  carbs: 75.0,
                  fat: 15.0,
                  fiber: 18.0,
                ),
                preparationTime: 45,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'عشاء',
                description: 'وجبة عشاء متوازنة',
                type: 'dinner',
                ingredients: [
                  const MealIngredient(name: '150 جرام دجاج مشوي', needsPreparation: true, instructions: ['تبل الدجاج واشويه لمدة 20 دقيقة']),
                  const MealIngredient(name: '½ كوب أرز', needsPreparation: true, instructions: ['اطبخ الأرز لمدة 20 دقيقة']),
                  const MealIngredient(name: '1 كوب خضار مشكلة', needsPreparation: true, instructions: ['اطبخ الخضار على البخار لمدة 10 دقائق']),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة صغيرة أعشاب'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 480,
                  protein: 42.0,
                  carbs: 45.0,
                  fat: 18.0,
                  fiber: 6.0,
                ),
                preparationTime: 35,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'وجبة خفيفة',
                description: 'وجبة خفيفة صحية',
                type: 'snack',
                ingredients: [
                  const MealIngredient(name: '1 تفاحة', needsPreparation: true, instructions: ['اغسل التفاحة وقطعها شرائح']),
                  const MealIngredient(name: '2 ملعقة كبيرة زبدة لوز'),
                  const MealIngredient(name: 'رشة قرفة'),
                ],
                nutrition: const NutritionInfo(
                  calories: 250,
                  protein: 8.0,
                  carbs: 25.0,
                  fat: 15.0,
                  fiber: 6.0,
                ),
                preparationTime: 5,
                difficulty: 'easy',
              ),
            ],
          ),
          // Day 3
          DayMealPlan(
            date: DateTime.now().add(const Duration(days: 2)),
            totalNutrition: const NutritionInfo(
              calories: 2080,
              protein: 118.0,
              carbs: 245.0,
              fat: 72.0,
              fiber: 30.0,
            ),
            meals: [
              GeneratedMeal(
                name: 'إفطار',
                description: 'مشروب إفطار منعش',
                type: 'breakfast',
                ingredients: [
                  const MealIngredient(name: '1 موزة'),
                  const MealIngredient(name: '½ كوب توت'),
                  const MealIngredient(name: '1 كوب سبانخ'),
                  const MealIngredient(name: '1 كوب حليب لوز'),
                  const MealIngredient(name: '1 ملعقة كبيرة بذور شيا'),
                  const MealIngredient(name: '1 ملعقة كبيرة عسل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 320,
                  protein: 12.0,
                  carbs: 55.0,
                  fat: 8.0,
                  fiber: 12.0,
                ),
                preparationTime: 10,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'غداء',
                description: 'وجبة غداء خفيفة',
                type: 'lunch',
                ingredients: [
                  const MealIngredient(name: '1 علبة تونة', needsPreparation: true, instructions: ['صفي التونة من الماء وفككها']),
                  const MealIngredient(name: '½ كوب حمص', needsPreparation: true, instructions: ['اسلقي الحمص لمدة 30 دقيقة']),
                  const MealIngredient(name: '2 كوب خس'),
                  const MealIngredient(name: '½ كوب خيار'),
                  const MealIngredient(name: '¼ كوب بصل'),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة كبيرة خل'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 450,
                  protein: 35.0,
                  carbs: 30.0,
                  fat: 22.0,
                  fiber: 8.0,
                ),
                preparationTime: 15,
                difficulty: 'easy',
              ),
              GeneratedMeal(
                name: 'عشاء',
                description: 'وجبة عشاء غنية بالبروتين',
                type: 'dinner',
                ingredients: [
                  const MealIngredient(name: '120 جرام لحم بقري', needsPreparation: true, instructions: ['تبل اللحم واشويه لمدة 15 دقيقة']),
                  const MealIngredient(name: '1 حبة بطاطا حلوة', needsPreparation: true, instructions: ['قطع البطاطا واشويها لمدة 25 دقيقة']),
                  const MealIngredient(name: '1 كوب فاصولياء', needsPreparation: true, instructions: ['اطبخ الفاصولياء على البخار لمدة 8 دقائق']),
                  const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
                  const MealIngredient(name: '1 ملعقة صغيرة روزماري'),
                  const MealIngredient(name: 'ملح وفلفل'),
                ],
                nutrition: const NutritionInfo(
                  calories: 520,
                  protein: 38.0,
                  carbs: 45.0,
                  fat: 22.0,
                  fiber: 8.0,
                ),
                preparationTime: 40,
                difficulty: 'medium',
              ),
              GeneratedMeal(
                name: 'وجبة خفيفة',
                description: 'وجبة خفيفة طبيعية',
                type: 'snack',
                ingredients: [
                  const MealIngredient(name: '30 جرام لوز'),
                  const MealIngredient(name: '20 جرام تمر'),
                  const MealIngredient(name: '10 جرام جوز'),
                ],
                nutrition: const NutritionInfo(
                  calories: 280,
                  protein: 8.0,
                  carbs: 22.0,
                  fat: 18.0,
                  fiber: 5.0,
                ),
                preparationTime: 2,
                difficulty: 'easy',
              ),
            ],
          ),
        ],
        rawResponse: 'Mock meal plan generated for testing purposes',
      ),
    );
  }

  /// Creates mock extra days for extending the meal plan
  List<DayMealPlan> _createMockExtraDays(int existingDaysCount) {
    final baseDate = DateTime.now();
    return [
      // Day 4
      DayMealPlan(
        date: baseDate.add(Duration(days: existingDaysCount)),
        totalNutrition: const NutritionInfo(
          calories: 2120,
          protein: 125.0,
          carbs: 255.0,
          fat: 75.0,
          fiber: 38.0,
        ),
        meals: [
          GeneratedMeal(
            name: 'إفطار',
            description: 'فطائر صحية ولذيذة',
            type: 'breakfast',
            ingredients: [
              const MealIngredient(name: '1 كوب شوفان'),
              const MealIngredient(name: '2 موزة', needsPreparation: true, instructions: ['اهرس الموز جيداً']),
              const MealIngredient(name: '2 بيضة', needsPreparation: true, instructions: ['اخفق البيض مع الحليب']),
              const MealIngredient(name: '½ كوب حليب'),
              const MealIngredient(name: '1 ملعقة صغيرة بيكنج باودر'),
              const MealIngredient(name: '1 ملعقة صغيرة فانيليا'),
              const MealIngredient(name: 'رشة ملح'),
            ],
            nutrition: const NutritionInfo(
              calories: 380,
              protein: 16.0,
              carbs: 58.0,
              fat: 12.0,
              fiber: 8.0,
            ),
            preparationTime: 20,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'غداء',
            description: 'سلطة صحية ومغذية',
            type: 'lunch',
            ingredients: [
              const MealIngredient(name: '1 كوب فاصولياء', needsPreparation: true, instructions: ['اسلقي الفاصولياء لمدة 30 دقيقة']),
              const MealIngredient(name: '1 حبة أفوكادو', needsPreparation: true, instructions: ['قطع الأفوكادو مكعبات']),
              const MealIngredient(name: '1 كوب ذرة'),
              const MealIngredient(name: '½ كوب طماطم'),
              const MealIngredient(name: '¼ كوب بصل'),
              const MealIngredient(name: '¼ كوب كزبرة'),
              const MealIngredient(name: '2 ملعقة كبيرة عصير ليمون'),
              const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: 'ملح وفلفل'),
            ],
            nutrition: const NutritionInfo(
              calories: 520,
              protein: 18.0,
              carbs: 65.0,
              fat: 22.0,
              fiber: 18.0,
            ),
            preparationTime: 15,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'سمك القد المشوي مع الخضار الجذرية',
            description: 'وجبة عشاء صحية غنية بأوميجا 3',
            type: 'dinner',
            ingredients: [
              const MealIngredient(name: '150 جرام فيليه سمك القد', needsPreparation: true, instructions: ['تبل السمك بالملح والفلفل وإكليل الجبل']),
              const MealIngredient(name: '1 حبة بطاطس متوسطة', needsPreparation: true, instructions: ['قطع البطاطس قطع متوسطة']),
              const MealIngredient(name: '2 جزر متوسط', needsPreparation: true, instructions: ['قطع الجزر قطع متوسطة']),
              const MealIngredient(name: '1 حبة شمندر صغيرة', needsPreparation: true, instructions: ['قطع الشمندر قطع متوسطة']),
              const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: '1 ملعقة صغيرة إكليل الجبل'),
              const MealIngredient(name: 'ملح وفلفل أسود'),
              const MealIngredient(name: 'عصير ½ ليمونة'),
            ],
            nutrition: const NutritionInfo(
              calories: 480,
              protein: 35.0,
              carbs: 45.0,
              fat: 18.0,
              fiber: 8.0,
            ),
            preparationTime: 45,
            difficulty: 'medium',
          ),
          GeneratedMeal(
            name: 'كرات الطاقة بالتمر والشوكولاتة',
            description: 'وجبة خفيفة طبيعية ومغذية',
            type: 'snack',
            ingredients: [
              const MealIngredient(name: '1 كوب تمر منزوع النوى', needsPreparation: true, instructions: ['انقع التمر في الماء الدافئ لـ 10 دقائق']),
              const MealIngredient(name: '½ كوب لوز نيء'),
              const MealIngredient(name: '2 ملعقة كبيرة كاكاو خام'),
              const MealIngredient(name: '1 ملعقة كبيرة بذور الشيا'),
              const MealIngredient(name: 'رشة ملح البحر'),
            ],
            nutrition: const NutritionInfo(
              calories: 260,
              protein: 8.0,
              carbs: 35.0,
              fat: 12.0,
              fiber: 6.0,
            ),
            preparationTime: 20,
            difficulty: 'easy',
          ),
        ],
      ),
      // Day 5
      DayMealPlan(
        date: baseDate.add(Duration(days: existingDaysCount + 1)),
        totalNutrition: const NutritionInfo(
          calories: 2090,
          protein: 120.0,
          carbs: 248.0,
          fat: 70.0,
          fiber: 35.0,
        ),
        meals: [
          GeneratedMeal(
            name: 'إفطار',
            description: 'إفطار منعش وصحي',
            type: 'breakfast',
            ingredients: [
              const MealIngredient(name: '3 ملاعق كبيرة بذور شيا', needsPreparation: true, instructions: ['انقع بذور الشيا في الحليب طوال الليل']),
              const MealIngredient(name: '1 كوب حليب جوز الهند'),
              const MealIngredient(name: '1 كوب مانجو'),
              const MealIngredient(name: '1 ملعقة كبيرة عسل'),
              const MealIngredient(name: '½ ملعقة صغيرة فانيليا'),
              const MealIngredient(name: '2 ملعقة كبيرة جوز هند'),
            ],
            nutrition: const NutritionInfo(
              calories: 350,
              protein: 12.0,
              carbs: 45.0,
              fat: 16.0,
              fiber: 12.0,
            ),
            preparationTime: 10,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'شوربة العدس الأحمر بالكركم',
            description: 'شوربة دافئة ومضادة للالتهابات',
            type: 'lunch',
            ingredients: [
              const MealIngredient(name: '1 كوب عدس أحمر'),
              const MealIngredient(name: '3 أكواب مرق خضار'),
              const MealIngredient(name: '1 بصلة متوسطة مقطعة'),
              const MealIngredient(name: '2 فص ثوم مفروم'),
              const MealIngredient(name: '1 ملعقة صغيرة كركم'),
              const MealIngredient(name: '½ ملعقة صغيرة كمون'),
              const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: 'ملح وفلفل حسب الذوق'),
              const MealIngredient(name: 'عصير ½ ليمونة'),
            ],
            nutrition: const NutritionInfo(
              calories: 480,
              protein: 24.0,
              carbs: 70.0,
              fat: 14.0,
              fiber: 16.0,
            ),
            preparationTime: 35,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'دجاج تندوري مع الأرز البسمتي',
            description: 'وجبة هندية لذيذة ومتبلة',
            type: 'dinner',
            ingredients: [
              const MealIngredient(name: '150 جرام صدر دجاج', needsPreparation: true, instructions: ['تبل الدجاج بالخليط واتركه لـ 30 دقيقة']),
              const MealIngredient(name: '½ كوب أرز بسمتي', needsPreparation: true, instructions: ['اطبخ الأرز حسب التعليمات']),
              const MealIngredient(name: '¼ كوب زبادي يوناني'),
              const MealIngredient(name: '1 ملعقة صغيرة بابريكا'),
              const MealIngredient(name: '½ ملعقة صغيرة كركم'),
              const MealIngredient(name: '½ ملعقة صغيرة كمون'),
              const MealIngredient(name: '1 فص ثوم مفروم'),
              const MealIngredient(name: '1 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: 'ملح وفلفل'),
            ],
            nutrition: const NutritionInfo(
              calories: 520,
              protein: 42.0,
              carbs: 50.0,
              fat: 16.0,
              fiber: 2.0,
            ),
            preparationTime: 40,
            difficulty: 'medium',
          ),
          GeneratedMeal(
            name: 'عصير أخضر منعش',
            description: 'مشروب صحي غني بالفيتامينات',
            type: 'snack',
            ingredients: [
              const MealIngredient(name: '1 كوب سبانخ طازجة'),
              const MealIngredient(name: '½ خيارة', needsPreparation: true, instructions: ['قطع الخيار قطع صغيرة']),
              const MealIngredient(name: '1 تفاحة خضراء', needsPreparation: true, instructions: ['قطع التفاح قطع صغيرة']),
              const MealIngredient(name: '½ كوب ماء جوز الهند'),
              const MealIngredient(name: 'عصير ½ ليمونة'),
              const MealIngredient(name: '1 ملعقة صغيرة زنجبيل طازج'),
            ],
            nutrition: const NutritionInfo(
              calories: 120,
              protein: 3.0,
              carbs: 28.0,
              fat: 1.0,
              fiber: 5.0,
            ),
            preparationTime: 10,
            difficulty: 'easy',
          ),
        ],
      ),
      // Day 6
      DayMealPlan(
        date: baseDate.add(Duration(days: existingDaysCount + 2)),
        totalNutrition: const NutritionInfo(
          calories: 2110,
          protein: 118.0,
          carbs: 250.0,
          fat: 72.0,
          fiber: 33.0,
        ),
        meals: [
          GeneratedMeal(
            name: 'إفطار',
            description: 'إفطار كلاسيكي وصحي',
            type: 'breakfast',
            ingredients: [
              const MealIngredient(name: '2 شريحة خبز', needsPreparation: true, instructions: ['حمص الخبز حتى يصبح ذهبي']),
              const MealIngredient(name: '1 حبة أفوكادو', needsPreparation: true, instructions: ['اهرس الأفوكادو مع الليمون']),
              const MealIngredient(name: '2 بيضة مسلوقة', needsPreparation: true, instructions: ['اسلقي البيض في ماء مغلي لمدة 8-10 دقائق']),
              const MealIngredient(name: '1 ملعقة صغيرة عصير ليمون'),
              const MealIngredient(name: 'ملح وفلفل'),
              const MealIngredient(name: 'رشة بابريكا'),
              const MealIngredient(name: 'أوراق جرجير'),
            ],
            nutrition: const NutritionInfo(
              calories: 420,
              protein: 18.0,
              carbs: 35.0,
              fat: 25.0,
              fiber: 12.0,
            ),
            preparationTime: 15,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'سلطة الكينوا مع الخضار المشوية',
            description: 'سلطة مغذية ومشبعة',
            type: 'lunch',
            ingredients: [
              const MealIngredient(name: '¾ كوب كينوا مطبوخة', needsPreparation: true, instructions: ['اطبخ الكينوا حسب التعليمات واتركها تبرد']),
              const MealIngredient(name: '1 كوب باذنجان مقطع مكعبات', needsPreparation: true, instructions: ['ادهن الباذنجان بزيت الزيتون وتبله']),
              const MealIngredient(name: '1 كوب كوسا مقطعة', needsPreparation: true, instructions: ['ادهن الكوسا بزيت الزيتون وتبلها']),
              const MealIngredient(name: '½ كوب فلفل ملون', needsPreparation: true, instructions: ['ادهن الفلفل بزيت الزيتون وتبله']),
              const MealIngredient(name: '¼ كوب جبن فيتا مفتت'),
              const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: '1 ملعقة كبيرة خل بلسمي'),
              const MealIngredient(name: 'أوراق نعناع طازجة'),
              const MealIngredient(name: 'ملح وفلفل'),
            ],
            nutrition: const NutritionInfo(
              calories: 550,
              protein: 20.0,
              carbs: 65.0,
              fat: 24.0,
              fiber: 12.0,
            ),
            preparationTime: 40,
            difficulty: 'medium',
          ),
          GeneratedMeal(
            name: 'سمك السلمون بالليمون والأعشاب',
            description: 'وجبة عشاء خفيفة وصحية',
            type: 'dinner',
            ingredients: [
              const MealIngredient(name: '150 جرام فيليه سلمون', needsPreparation: true, instructions: ['رش عصير الليمون والشبت والملح والفلفل']),
              const MealIngredient(name: '1 كوب أرز بني مطبوخ'),
              const MealIngredient(name: '1 كوب بروكلي', needsPreparation: true, instructions: ['اطبخ البروكلي على البخار']),
              const MealIngredient(name: 'عصير 1 ليمونة'),
              const MealIngredient(name: '2 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: '1 ملعقة صغيرة شبت مجفف'),
              const MealIngredient(name: 'ملح وفلفل أسود'),
            ],
            nutrition: const NutritionInfo(
              calories: 480,
              protein: 38.0,
              carbs: 45.0,
              fat: 18.0,
              fiber: 6.0,
            ),
            preparationTime: 25,
            difficulty: 'easy',
          ),
          GeneratedMeal(
            name: 'كوب زبادي بالعسل والجوز',
            description: 'وجبة خفيفة بسيطة ومغذية',
            type: 'snack',
            ingredients: [
              const MealIngredient(name: '1 كوب زبادي يوناني'),
              const MealIngredient(name: '1 ملعقة كبيرة عسل'),
              const MealIngredient(name: '¼ كوب جوز مقطع'),
              const MealIngredient(name: 'رشة قرفة'),
            ],
            nutrition: const NutritionInfo(
              calories: 280,
              protein: 20.0,
              carbs: 25.0,
              fat: 12.0,
              fiber: 2.0,
            ),
            preparationTime: 5,
            difficulty: 'easy',
          ),
        ],
      ),
    ];
  }

  /// Replace a meal with a new suggestion using async Firebase Functions API
  /// Returns true if replacement generation started successfully, false if failed
  /// Actual replacement data will be available via Firestore real-time updates
  Future<bool> replaceMealAsync({
    required GeneratedMeal currentMeal,
    required String dayDocumentId,
    required DateTime mealDate,
    String? customIngredients,
  }) async {
    // Check replacement limit
    if (currentMeal.replacementCount >= 3) {
      state = state.copyWith(error: 'تم الوصول للحد الأقصى من البدائل (3 بدائل)');
      return false;
    }

    // Check if meal is consumed
    if (currentMeal.isConsumed) {
      state = state.copyWith(error: 'لا يمكن استبدال وجبة تم تناولها');
      return false;
    }

    final userProfile = ref.read(currentUserProfileProvider);
    if (userProfile == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    try {
      // Validate that dayDocumentId is provided
      if (dayDocumentId.isEmpty) {
        AppLogger.error('dayDocumentId is required for meal replacement');
        return false;
      }

      // Validate that meal has an ID
      if (currentMeal.id == null || currentMeal.id!.isEmpty) {
        AppLogger.error('Meal ID is required for meal replacement');
        state = state.copyWith(error: 'معرف الوجبة مطلوب للاستبدال');
        return false;
      }

      // Call Firebase Functions API for async meal replacement
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      final response = await mealPlanService.replaceMeal(
        dayDocumentId: dayDocumentId,
        mealId: currentMeal.id!,
        customIngredients: customIngredients,
      );

      final success = response['success'] as bool? ?? false;
      if (success) {
        // Set loading flag in local storage using meal ID
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        final dateKey = mealDate.toIso8601String().split('T')[0];
        await localStorageService.saveMealReplacementLoading(dateKey, currentMeal.id!, true);

        AppLogger.info('Meal replacement request successful for meal ID ${currentMeal.id} in day document $dayDocumentId');
        return true;
      } else {
        final errorMessage = response['error'] as String? ?? 'Unknown error';
        state = state.copyWith(error: errorMessage);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: 'فشل في طلب بديل للوجبة: $e');
      return false;
    }
  }

  /// Replace a meal with a new suggestion (legacy synchronous method)
  /// Returns the new meal if successful, null if failed
  Future<GeneratedMeal?> replaceMeal({
    required GeneratedMeal currentMeal,
    required DateTime mealDate,
    required String mealType,
  }) async {
    // Check replacement limit
    if (currentMeal.replacementCount >= 3) {
      state = state.copyWith(error: 'تم الوصول للحد الأقصى من البدائل (3 بدائل)');
      return null;
    }

    // Check if meal is consumed
    if (currentMeal.isConsumed) {
      state = state.copyWith(error: 'لا يمكن استبدال وجبة تم تناولها');
      return null;
    }

    // Check if it's a past day
    final today = DateTime.now();
    final mealDay = DateTime(mealDate.year, mealDate.month, mealDate.day);
    final todayDay = DateTime(today.year, today.month, today.day);

    if (mealDay.isBefore(todayDay)) {
      state = state.copyWith(error: 'لا يمكن استبدال وجبات الأيام السابقة');
      return null;
    }

    state = state.copyWith(isGenerating: true, error: null);

    try {
      // Generate replacement meal with similar nutrition profile
      final replacementMeal = _generateReplacementMeal(currentMeal);

      // Create replacement record with new structure
      final replacement = MealReplacement(
        id: 'replacement_${DateTime.now().millisecondsSinceEpoch}',
        replacedAt: DateTime.now(),
        reason: 'user_request',
        meal: replacementMeal, // Renamed from replacementMeal
        isOriginal: false,
      );

      // Handle replacement history based on new structure
      List<MealReplacement> updatedReplacementHistory;
      if (currentMeal.replacementHistory.isEmpty) {
        // First replacement: copy current meal as original, then add new replacement
        print('Creating first replacement for meal: ${currentMeal.name}');

        // Create clean copy excluding root-only fields
        final originalMealCopy = GeneratedMeal(
          name: currentMeal.name,
          type: currentMeal.type, // Keep type for context but will be excluded from replacement objects
          ingredients: currentMeal.ingredients,
          nutrition: currentMeal.nutrition,
          description: currentMeal.description,
          preparationTime: currentMeal.preparationTime,
          difficulty: currentMeal.difficulty,
          // Exclude root-only fields: isConsumed, consumedAt, replacementHistory, replacementCount, selectedMealId
        );

        // Create replacement meal copy (also excluding root-only fields)
        final cleanReplacementMeal = GeneratedMeal(
          name: replacementMeal.name,
          type: replacementMeal.type, // Keep type for context
          ingredients: replacementMeal.ingredients,
          nutrition: replacementMeal.nutrition,
          description: replacementMeal.description,
          preparationTime: replacementMeal.preparationTime,
          difficulty: replacementMeal.difficulty,
          // Exclude root-only fields
        );

        final originalReplacement = MealReplacement(
          id: 'original',
          replacedAt: DateTime.now(),
          reason: 'original',
          meal: originalMealCopy,
          isOriginal: true,
        );

        final cleanReplacement = replacement.copyWith(meal: cleanReplacementMeal);

        updatedReplacementHistory = [originalReplacement, cleanReplacement];
        print('Created replacement history with original and new replacement');
        print('Original meal copy excludes: isConsumed, consumedAt, replacementHistory, replacementCount, selectedMealId');
      } else {
        // Add to existing replacement history (ensure replacement meal is clean)
        final cleanReplacementMeal = GeneratedMeal(
          name: replacement.meal.name,
          type: replacement.meal.type,
          ingredients: replacement.meal.ingredients,
          nutrition: replacement.meal.nutrition,
          description: replacement.meal.description,
          preparationTime: replacement.meal.preparationTime,
          difficulty: replacement.meal.difficulty,
          // Exclude root-only fields
        );

        final cleanReplacement = replacement.copyWith(meal: cleanReplacementMeal);
        updatedReplacementHistory = [...currentMeal.replacementHistory, cleanReplacement];
        print('Added replacement to existing history. Total replacements: ${updatedReplacementHistory.length}');
      }

      // Update the current meal with new replacement (don't auto-select, let user choose)
      final updatedReplacementMeal = currentMeal.copyWith(
        replacementHistory: updatedReplacementHistory,
        replacementCount: currentMeal.replacementCount + 1,
        // Note: Don't auto-select the replacement, let user choose
      );

      print('Updated meal structure: ${updatedReplacementMeal.name}');
      print('Replacement history length: ${updatedReplacementMeal.replacementHistory.length}');
      print('Replacement count: ${updatedReplacementMeal.replacementCount}');
      print('Has original in history: ${updatedReplacementMeal.replacementHistory.any((r) => r.isOriginal)}');

      state = state.copyWith(isGenerating: false);
      return updatedReplacementMeal;
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: 'فشل في إنشاء بديل للوجبة: $e',
      );
      return null;
    }
  }

  /// Select a specific meal version to be active
  /// This copies the selected meal's properties to the root meal while preserving root-only fields
  GeneratedMeal? selectMealVersion({
    required GeneratedMeal currentMeal,
    required String selectedMealId,
  }) {
    try {
      final allVersions = currentMeal.getAllMealVersions();
      print('Selecting meal version: $selectedMealId from ${allVersions.length} available versions');

      final selectedVersion = allVersions.where((version) => version.id == selectedMealId).firstOrNull;

      if (selectedVersion == null) {
        print('Selected version not found. Available versions: ${allVersions.map((v) => v.id).toList()}');
        state = state.copyWith(error: 'لم يتم العثور على نسخة الوجبة المطلوبة');
        return null;
      }

      print('Found selected version: ${selectedVersion.title} (${selectedVersion.meal.name})');

      // Copy only non-root fields from the selected meal version to the root meal
      // Preserve root-only fields: isConsumed, consumedAt, type, replacementHistory, replacementCount, selectedMealId
      final selectedMeal = selectedVersion.meal;
      final updatedMeal = currentMeal.copyWith(
        name: selectedMeal.name,
        ingredients: selectedMeal.ingredients,
        nutrition: selectedMeal.nutrition,
        description: selectedMeal.description,
        preparationTime: selectedMeal.preparationTime,
        difficulty: selectedMeal.difficulty,
        selectedMealId: selectedMealId,
        // Note: type is preserved from root meal, not copied from replacement
        // Note: isConsumed and consumedAt are preserved from root meal
      );

      print('Updated root meal with selected version properties');
      return updatedMeal;
    } catch (e) {
      print('Error in selectMealVersion: $e');
      state = state.copyWith(error: 'فشل في اختيار نسخة الوجبة: $e');
      return null;
    }
  }



  /// Generate a replacement meal with similar nutritional profile (mock implementation)
  GeneratedMeal _generateReplacementMeal(GeneratedMeal originalMeal) {
    // Mock replacement meals based on meal type
    final replacementOptions = _getMockReplacementOptions(originalMeal.type);
    final selectedReplacement = replacementOptions[
        DateTime.now().millisecondsSinceEpoch % replacementOptions.length
    ];

    // Adjust nutrition to be similar to original (±10% variance)
    final variance = 0.1;
    final calorieVariance = (originalMeal.nutrition.calories * variance).round();
    final proteinVariance = originalMeal.nutrition.protein * variance;
    final carbsVariance = originalMeal.nutrition.carbs * variance;
    final fatVariance = originalMeal.nutrition.fat * variance;

    final adjustedNutrition = NutritionInfo(
      calories: originalMeal.nutrition.calories +
          (DateTime.now().millisecondsSinceEpoch % 2 == 0 ? calorieVariance : -calorieVariance),
      protein: originalMeal.nutrition.protein +
          (DateTime.now().millisecondsSinceEpoch % 3 == 0 ? proteinVariance : -proteinVariance),
      carbs: originalMeal.nutrition.carbs +
          (DateTime.now().millisecondsSinceEpoch % 4 == 0 ? carbsVariance : -carbsVariance),
      fat: originalMeal.nutrition.fat +
          (DateTime.now().millisecondsSinceEpoch % 5 == 0 ? fatVariance : -fatVariance),
      fiber: originalMeal.nutrition.fiber,
    );

    return selectedReplacement.copyWith(
      nutrition: adjustedNutrition,
      type: originalMeal.type, // Keep the same meal type
    );
  }

  /// Reset state
  void reset() {
    state = const MealPlanGenerationState();
  }

  /// Load user preferences from profile
  void loadUserPreferences() {
    final planPreferences = ref.read(currentPlanPreferencesProvider);
    if (planPreferences == null) return;

    state = state.copyWith(
      preferences: state.preferences.copyWith(
        calorieGoal: planPreferences.dailyCalorieGoal ?? 2000,
        proteinGoal: planPreferences.proteinGoal?.toInt() ?? 0,
        carbsGoal: planPreferences.carbsGoal?.toInt() ?? 0,
        fatGoal: planPreferences.fatGoal?.toInt() ?? 0,
        mealsPerDay: planPreferences.mealsPerDay,
        dietaryRestrictions: planPreferences.dietaryRestrictions,
        allergies: planPreferences.allergies,
      ),
    );
  }

  /// Load meal plan from local storage
  Future<void> loadMealPlanFromStorage() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final mealPlanData = localStorageService.loadCurrentMealPlan();

      if (mealPlanData != null) {
        try {
          // Load the GeneratedMealPlan data and wrap it in a MealPlanResponse for compatibility
          final generatedMealPlan = GeneratedMealPlan.fromJson(mealPlanData);
          final mealPlan = MealPlanResponse(
            success: true,
            message: 'خطة وجبات محملة من التخزين المحلي',
            mealPlan: generatedMealPlan,
          );

          state = state.copyWith(
            generatedPlan: mealPlan,
            isGenerating: false,
            error: null,
          );

          // Load consumption statuses for this meal plan
          final consumptionNotifier = ref.read(mealConsumptionNotifierProvider.notifier);
          await consumptionNotifier.loadConsumptionStatuses(_generateMealPlanId(mealPlan));

        } catch (parseError) {
          // If parsing fails, clear the corrupted data
          await localStorageService.clearCurrentMealPlan();
          state = state.copyWith(
            generatedPlan: null,
            isGenerating: false,
            error: null,
          );
          print('Failed to parse meal plan from local storage, cleared corrupted data: $parseError');
        }
      } else {
        // No meal plan in storage
        state = state.copyWith(
          generatedPlan: null,
          isGenerating: false,
          error: null,
        );
      }
    } catch (e) {
      // Storage access error
      state = state.copyWith(
        generatedPlan: null,
        isGenerating: false,
        error: null,
      );
      print('Failed to load meal plan from local storage: $e');
    }
  }

  /// Clear current meal plan and local storage
  Future<void> clearMealPlan() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearCurrentMealPlan();

      // Clear consumption statuses if there's a current plan
      if (state.generatedPlan != null) {
        final consumptionNotifier = ref.read(mealConsumptionNotifierProvider.notifier);
        await consumptionNotifier.clearConsumptionStatuses(_generateMealPlanId(state.generatedPlan!));
      }

      state = state.copyWith(
        generatedPlan: null,
        isGenerating: false,
        error: null,
      );
    } catch (e) {
      print('Failed to clear meal plan: $e');
    }
  }

  /// Update the generated plan (used for synchronization with current meal plan provider)
  void updateGeneratedPlan(MealPlanResponse updatedPlan) {
    state = state.copyWith(
      generatedPlan: updatedPlan,
    );
    print('MealPlanGenerationProvider: Generated plan updated for UI synchronization');
  }

  /// Get mock replacement options based on meal type
  List<GeneratedMeal> _getMockReplacementOptions(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
      case 'إفطار':
        return [
          GeneratedMeal(
            name: 'إفطار',
            description: 'شوفان مطبوخ مع قطع الفواكه الطازجة والعسل',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '½ كوب شوفان'),
              const MealIngredient(name: '1 كوب حليب قليل الدسم'),
              const MealIngredient(name: '½ موزة مقطعة'),
              const MealIngredient(name: '1 ملعقة كبيرة عسل'),
              const MealIngredient(name: 'حفنة توت أزرق'),
            ],
            nutrition: const NutritionInfo(calories: 320, protein: 12.0, carbs: 58.0, fat: 6.0),
            preparationTime: 10,
          ),
          GeneratedMeal(
            name: 'إفطار',
            description: 'بيضتان مسلوقتان مع شريحة خبز أسمر وخضار',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '2 بيضة مسلوقة', needsPreparation: true, instructions: ['اسلقي البيض لمدة 8 دقائق']),
              const MealIngredient(name: '1 شريحة خبز أسمر'),
              const MealIngredient(name: '½ خيارة مقطعة'),
              const MealIngredient(name: '½ طماطم مقطعة'),
            ],
            nutrition: const NutritionInfo(calories: 280, protein: 18.0, carbs: 25.0, fat: 12.0),
            preparationTime: 15,
          ),
          GeneratedMeal(
            name: 'إفطار',
            description: 'لبنة طازجة مع زعتر وخضار متنوعة',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '3 ملاعق كبيرة لبنة'),
              const MealIngredient(name: '1 ملعقة صغيرة زعتر'),
              const MealIngredient(name: '½ خيارة مقطعة'),
              const MealIngredient(name: '3 حبات زيتون'),
              const MealIngredient(name: '1 شريحة خبز عربي'),
            ],
            nutrition: const NutritionInfo(calories: 250, protein: 10.0, carbs: 30.0, fat: 8.0),
            preparationTime: 5,
          ),
        ];
      case 'lunch':
      case 'غداء':
        return [
          GeneratedMeal(
            name: 'غداء',
            description: 'قطع دجاج مشوية مع خضار ورقية متنوعة',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '150 جم دجاج مشوي', needsPreparation: true, instructions: ['اشوي الدجاج لمدة 20 دقيقة']),
              const MealIngredient(name: '2 كوب خس مقطع'),
              const MealIngredient(name: '½ كوب طماطم كرزية'),
              const MealIngredient(name: '¼ كوب جزر مبشور'),
              const MealIngredient(name: '1 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: '1 ملعقة كبيرة عصير ليمون'),
            ],
            nutrition: const NutritionInfo(calories: 380, protein: 35.0, carbs: 15.0, fat: 20.0),
            preparationTime: 25,
          ),
          GeneratedMeal(
            name: 'غداء',
            description: 'قطعة سمك مشوية مع أرز أبيض وخضار',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '150 جم سمك مشوي', needsPreparation: true, instructions: ['اشوي السمك لمدة 15 دقيقة']),
              const MealIngredient(name: '½ كوب أرز مطبوخ', needsPreparation: true, instructions: ['اطبخ الأرز لمدة 20 دقيقة']),
              const MealIngredient(name: '1 كوب بروكلي مطبوخ', needsPreparation: true, instructions: ['اسلقي البروكلي لمدة 5 دقائق']),
              const MealIngredient(name: '1 ملعقة صغيرة زيت زيتون'),
            ],
            nutrition: const NutritionInfo(calories: 420, protein: 32.0, carbs: 45.0, fat: 12.0),
            preparationTime: 30,
          ),
        ];
      case 'dinner':
      case 'عشاء':
        return [
          GeneratedMeal(
            name: 'عشاء',
            description: 'شوربة عدس دافئة مع سلطة خضار طازجة',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '1 كوب شوربة عدس', needsPreparation: true, instructions: ['اطبخ العدس لمدة 25 دقيقة']),
              const MealIngredient(name: '1 كوب سلطة خضراء'),
              const MealIngredient(name: '1 ملعقة كبيرة زيت زيتون'),
              const MealIngredient(name: '½ ليمونة'),
            ],
            nutrition: const NutritionInfo(calories: 280, protein: 15.0, carbs: 35.0, fat: 8.0),
            preparationTime: 30,
          ),
          GeneratedMeal(
            name: 'عشاء',
            description: 'كوب زبادي طبيعي مع خضار مقطعة',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '1 كوب زبادي طبيعي'),
              const MealIngredient(name: '½ خيارة مقطعة'),
              const MealIngredient(name: '½ طماطم مقطعة'),
              const MealIngredient(name: '1 ملعقة صغيرة زيت زيتون'),
              const MealIngredient(name: 'رشة ملح'),
            ],
            nutrition: const NutritionInfo(calories: 180, protein: 12.0, carbs: 15.0, fat: 8.0),
            preparationTime: 5,
          ),
        ];
      case 'snack':
      case 'وجبة خفيفة':
        return [
          GeneratedMeal(
            name: 'وجبة خفيفة',
            description: 'تفاحة متوسطة مع حفنة لوز',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '1 تفاحة متوسطة'),
              const MealIngredient(name: '10 حبات لوز'),
            ],
            nutrition: const NutritionInfo(calories: 200, protein: 6.0, carbs: 25.0, fat: 10.0),
            preparationTime: 0,
          ),
          GeneratedMeal(
            name: 'وجبة خفيفة',
            description: 'أعواد جزر مع ملعقتين حمص',
            type: mealType,
            ingredients: [
              const MealIngredient(name: '1 جزرة مقطعة أعواد'),
              const MealIngredient(name: '2 ملعقة كبيرة حمص'),
            ],
            nutrition: const NutritionInfo(calories: 120, protein: 4.0, carbs: 18.0, fat: 4.0),
            preparationTime: 5,
          ),
        ];
      default:
        return [];
    }
  }

  /// Generate a unique meal plan ID based on creation time and user
  String _generateMealPlanId(MealPlanResponse response) {
    final userProfile = ref.read(currentUserProfileProvider);
    final userId = userProfile?.id ?? 'anonymous';
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${userId}_$timestamp';
  }
}
