import 'package:freezed_annotation/freezed_annotation.dart';

part 'meal_plan.freezed.dart';
part 'meal_plan.g.dart';

@freezed
class MealPlan with _$MealPlan {
  const factory MealPlan({
    required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id') required String userId,
    required String name,
    String? description,
    @Json<PERSON><PERSON>(name: 'start_date') required DateTime startDate,
    @<PERSON>son<PERSON>ey(name: 'end_date') required DateTime endDate,
    @<PERSON>son<PERSON>ey(name: 'daily_plans') required List<DailyMealPlan> dailyPlans,
    @<PERSON>son<PERSON>ey(name: 'total_calories') required int totalCalories,
    @<PERSON>sonKey(name: 'total_protein') required double totalProtein,
    @<PERSON>sonKey(name: 'total_carbs') required double totalCarbs,
    @<PERSON>son<PERSON>ey(name: 'total_fat') required double totalFat,
    @<PERSON>sonKey(name: 'total_fiber') required double totalFiber,
    @<PERSON>son<PERSON>ey(name: 'is_active') @Default(false) bool isActive,
    @<PERSON>son<PERSON>ey(name: 'created_at') required DateTime createdAt,
    @Json<PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
  }) = _MealPlan;

  factory MealPlan.fromJson(Map<String, dynamic> json) =>
      _$MealPlanFromJson(json);
}

@freezed
class DailyMealPlan with _$DailyMealPlan {
  const factory DailyMealPlan({
    required String id,
    required DateTime date,
    required List<Meal> meals,
    @JsonKey(name: 'total_calories') required int totalCalories,
    @JsonKey(name: 'total_protein') required double totalProtein,
    @JsonKey(name: 'total_carbs') required double totalCarbs,
    @JsonKey(name: 'total_fat') required double totalFat,
    @JsonKey(name: 'total_fiber') required double totalFiber,
  }) = _DailyMealPlan;

  factory DailyMealPlan.fromJson(Map<String, dynamic> json) =>
      _$DailyMealPlanFromJson(json);
}

@freezed
class Meal with _$Meal {
  const factory Meal({
    required String id,
    required String name,
    String? description,
    @JsonKey(name: 'image_url') String? imageUrl,
    required MealType type,
    @JsonKey(name: 'food_items') required List<FoodItem> foodItems,
    required int calories,
    required double protein,
    required double carbs,
    required double fat,
    required double fiber,
    @JsonKey(name: 'preparation_time') required int preparationTime,
    required DifficultyLevel difficulty,
    @Default([]) List<String> instructions,
    @Default([]) List<String> tags,
    @JsonKey(name: 'is_favorite') @Default(false) bool isFavorite,
    @JsonKey(name: 'scheduled_time') DateTime? scheduledTime,
    @JsonKey(name: 'is_completed') @Default(false) bool isCompleted,
  }) = _Meal;

  factory Meal.fromJson(Map<String, dynamic> json) => _$MealFromJson(json);
}

@freezed
class FoodItem with _$FoodItem {
  const factory FoodItem({
    required String id,
    required String name,
    required double quantity,
    required String unit,
    required int calories,
    required double protein,
    required double carbs,
    required double fat,
    required double fiber,
    String? imageUrl,
    @Default([]) List<String> allergens,
  }) = _FoodItem;

  factory FoodItem.fromJson(Map<String, dynamic> json) =>
      _$FoodItemFromJson(json);
}

enum MealType {
  breakfast,
  lunch,
  dinner,
  snack,
}

enum DifficultyLevel {
  easy,
  medium,
  hard,
}
