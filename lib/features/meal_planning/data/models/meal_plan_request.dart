import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'meal_plan_request.freezed.dart';
part 'meal_plan_request.g.dart';

/// Custom JSON converter to handle both Firestore Timestamp and ISO String formats for DateTime
class TimestampOrStringConverter implements JsonConverter<DateTime, dynamic> {
  const TimestampOrStringConverter();

  @override
  DateTime fromJson(dynamic json) {
    if (json == null) {
      throw ArgumentError('Cannot convert null to DateTime');
    }
    if (json is Timestamp) {
      // Handle Firestore Timestamp objects
      return json.toDate();
    } else if (json is String) {
      // Handle ISO string format for backward compatibility
      return DateTime.parse(json);
    } else if (json is Map<String, dynamic>) {
      // Handle Firestore Timestamp as Map (when serialized)
      if (json.containsKey('seconds') && json.containsKey('nanoseconds')) {
        final seconds = json['seconds'] as int;
        final nanoseconds = json['nanoseconds'] as int;
        return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round(),
        );
      }
    }
    throw ArgumentError('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime dateTime) {
    // Always serialize as ISO string for consistency
    return dateTime.toIso8601String();
  }
}

/// Custom JSON converter to handle nullable DateTime fields with Firestore Timestamp and ISO String formats
class NullableTimestampOrStringConverter implements JsonConverter<DateTime?, dynamic> {
  const NullableTimestampOrStringConverter();

  @override
  DateTime? fromJson(dynamic json) {
    if (json == null) {
      return null;
    }
    if (json is Timestamp) {
      // Handle Firestore Timestamp objects
      return json.toDate();
    } else if (json is String) {
      // Handle ISO string format for backward compatibility
      return DateTime.parse(json);
    } else if (json is Map<String, dynamic>) {
      // Handle Firestore Timestamp as Map (when serialized)
      if (json.containsKey('seconds') && json.containsKey('nanoseconds')) {
        final seconds = json['seconds'] as int;
        final nanoseconds = json['nanoseconds'] as int;
        return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round(),
        );
      }
    }
    throw ArgumentError('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime? dateTime) {
    // Always serialize as ISO string for consistency, or null if null
    return dateTime?.toIso8601String();
  }
}

@freezed
class MealPlanRequest with _$MealPlanRequest {
  const factory MealPlanRequest({
    required String userId,
    required MealPlanPreferences preferences,
    @Default(3) int duration, // days
  }) = _MealPlanRequest;

  factory MealPlanRequest.fromJson(Map<String, dynamic> json) =>
      _$MealPlanRequestFromJson(json);
}

@freezed
class MealPlanPreferences with _$MealPlanPreferences {
  const factory MealPlanPreferences({
    @JsonKey(name: 'dietary_restrictions') @Default([]) List<String> dietaryRestrictions,
    @Default([]) List<String> allergies,
    @JsonKey(name: 'cuisine_preferences') @Default([]) List<String> cuisinePreferences,
    @JsonKey(name: 'meals_per_day') @Default(3) int mealsPerDay,
    @JsonKey(name: 'calorie_goal') @Default(2000) int calorieGoal,
    @JsonKey(name: 'protein_goal') @Default(0) int proteinGoal,
    @JsonKey(name: 'carbs_goal') @Default(0) int carbsGoal,
    @JsonKey(name: 'fat_goal') @Default(0) int fatGoal,
    @JsonKey(name: 'diet_type') @Default(DietType.normal) DietType dietType,
  }) = _MealPlanPreferences;

  factory MealPlanPreferences.fromJson(Map<String, dynamic> json) =>
      _$MealPlanPreferencesFromJson(json);
}

@freezed
class MealPlanResponse with _$MealPlanResponse {
  const factory MealPlanResponse({
    required bool success,
    @JsonKey(name: 'mealPlan') required GeneratedMealPlan mealPlan,
    required String message,
  }) = _MealPlanResponse;

  factory MealPlanResponse.fromJson(Map<String, dynamic> json) =>
      _$MealPlanResponseFromJson(json);
}

@freezed
class AsyncMealPlanResponse with _$AsyncMealPlanResponse {
  const factory AsyncMealPlanResponse({
    required bool success,
    required String message,
    @Default(true) bool isAsync,
  }) = _AsyncMealPlanResponse;

  factory AsyncMealPlanResponse.fromJson(Map<String, dynamic> json) =>
      _$AsyncMealPlanResponseFromJson(json);
}

@freezed
class GeneratedMealPlan with _$GeneratedMealPlan {
  const factory GeneratedMealPlan({
    required List<DayMealPlan> days,
    @JsonKey(name: 'raw_response') String? rawResponse,
  }) = _GeneratedMealPlan;

  factory GeneratedMealPlan.fromJson(Map<String, dynamic> json) =>
      _$GeneratedMealPlanFromJson(json);
}

@freezed
class DayMealPlan with _$DayMealPlan {
  const factory DayMealPlan({
    @TimestampOrStringConverter() required DateTime date,
    required List<GeneratedMeal> meals,
    @JsonKey(name: 'total_nutrition') required NutritionInfo totalNutrition,
    @JsonKey(name: 'day_document_id') String? dayDocumentId, // Firestore document ID for this day
  }) = _DayMealPlan;

  factory DayMealPlan.fromJson(Map<String, dynamic> json) =>
      _$DayMealPlanFromJson(json);
}

@freezed
class GeneratedMeal with _$GeneratedMeal {
  const factory GeneratedMeal({
    @JsonKey(name: 'id') String? id, // Unique identifier for this meal
    required String name,
    @Default('breakfast') String type, // breakfast, lunch, dinner, snack - made optional with default for replacement objects
    required List<MealIngredient> ingredients,
    required NutritionInfo nutrition,
    String? description,
    @JsonKey(name: 'consumed_at') @NullableTimestampOrStringConverter() DateTime? consumedAt,
    @JsonKey(name: 'selected_meal_id') String? selectedMealId, // ID of the currently selected meal version
    @JsonKey(name: 'preparation_time') @Default(30) int preparationTime,
    @Default('easy') String difficulty,
    // Consumption tracking embedded in the meal
    @JsonKey(name: 'is_consumed') @Default(false) bool isConsumed,
    // Meal replacement tracking
    @JsonKey(name: 'replacement_history') @Default([]) List<MealReplacement> replacementHistory,
    @JsonKey(name: 'replacement_count') @Default(0) int replacementCount,
  }) = _GeneratedMeal;

  factory GeneratedMeal.fromJson(Map<String, dynamic> json) =>
      _$GeneratedMealFromJson(json);

  /// Factory method to create GeneratedMeal from JSON with backward compatibility
  factory GeneratedMeal.fromJsonWithCompatibility(Map<String, dynamic> json) {
    // Handle backward compatibility and missing fields
    final cleanedJson = Map<String, dynamic>.from(json);

    // Ensure required fields have defaults if missing
    cleanedJson['name'] ??= 'وجبة غير محددة';
    cleanedJson['type'] ??= 'breakfast';
    cleanedJson['ingredients'] ??= [];
    cleanedJson['nutrition'] ??= {
      'calories': 0,
      'protein': 0.0,
      'carbs': 0.0,
      'fat': 0.0,
      'fiber': 0.0,
    };

    // Handle backward compatibility for old replacement structure
    if (cleanedJson.containsKey('originalMeal')) {
      // Remove the old originalMeal field if it exists
      cleanedJson.remove('originalMeal');
    }

    // Handle old replacementMeal field in replacement history
    if (cleanedJson['replacementHistory'] is List) {
      final replacementHistory = cleanedJson['replacementHistory'] as List;
      for (int i = 0; i < replacementHistory.length; i++) {
        final replacement = replacementHistory[i] as Map<String, dynamic>;

        // Handle old replacementMeal field name
        if (replacement.containsKey('replacementMeal') && !replacement.containsKey('meal')) {
          replacement['meal'] = replacement['replacementMeal'];
          replacement.remove('replacementMeal');
        }

        // Ensure replacement meal has required fields with fallbacks
        if (replacement['meal'] is Map<String, dynamic>) {
          final mealData = replacement['meal'] as Map<String, dynamic>;
          mealData['name'] ??= 'وجبة بديلة';
          mealData['type'] ??= cleanedJson['type'] ?? 'breakfast'; // Inherit from parent
          mealData['ingredients'] ??= [];
          mealData['nutrition'] ??= cleanedJson['nutrition'];
        }
      }
    }

    return GeneratedMeal.fromJson(cleanedJson);
  }
}

extension GeneratedMealExtensions on GeneratedMeal {
  /// Get all available meal versions (original + all replacements)
  List<MealVersion> getAllMealVersions() {
    final versions = <MealVersion>[];

    if (replacementHistory.isEmpty) {
      // If no replacement history, this meal IS the original
      versions.add(MealVersion(
        id: 'original',
        meal: this,
        title: 'الوجبة الأصلية',
        isOriginal: true,
        createdAt: DateTime.now(), // We don't have creation time for original
      ));
    } else {
      // Add all meals from replacement history (first one is original, rest are replacements)
      for (int i = 0; i < replacementHistory.length; i++) {
        final replacement = replacementHistory[i];
        final isOriginal = replacement.isOriginal ?? (i == 0); // First item is original if not explicitly set
        versions.add(MealVersion(
          id: replacement.id,
          meal: replacement.meal,
          title: isOriginal ? 'الوجبة الأصلية' : 'البديل $i',
          isOriginal: isOriginal,
          createdAt: replacement.replacedAt,
        ));
      }
    }

    return versions;
  }

  /// Get the currently selected meal version
  MealVersion? getSelectedMealVersion() {
    final allVersions = getAllMealVersions();
    if (selectedMealId == null) {
      // If no selection is made, return the original meal (first version)
      return allVersions.isNotEmpty ? allVersions.first : null;
    }

    return allVersions.where((version) => version.id == selectedMealId).firstOrNull;
  }

  /// Get the currently active meal (the meal data that should be displayed)
  /// This returns the root meal with properties from the selected version
  /// while preserving root-only fields (type, isConsumed, consumedAt, etc.)
  GeneratedMeal getActiveMeal() {
    final selectedVersion = getSelectedMealVersion();
    if (selectedVersion == null) return this;

    // If the selected version is the root meal itself, return as-is
    if (selectedVersion.id == 'original' && replacementHistory.isEmpty) {
      return this;
    }

    // Otherwise, return root meal with selected version's properties
    // while preserving root-only fields
    final selectedMeal = selectedVersion.meal;
    return copyWith(
      name: selectedMeal.name,
      ingredients: selectedMeal.ingredients,
      nutrition: selectedMeal.nutrition,
      description: selectedMeal.description,
      preparationTime: selectedMeal.preparationTime,
      difficulty: selectedMeal.difficulty,
      // Note: type, isConsumed, consumedAt, replacementHistory, replacementCount are preserved from root
    );
  }

  /// Check if this meal has any replacement options
  bool get hasReplacements => replacementHistory.isNotEmpty;

  /// Check if more replacements can be generated
  bool get canGenerateMoreReplacements => replacementCount < 3;

  /// Get the total number of available meal versions
  int get totalVersionsCount => replacementHistory.isEmpty ? 1 : replacementHistory.length;
}

@freezed
class MealIngredient with _$MealIngredient {
  const factory MealIngredient({
    required String name,
    @Default([]) List<String> instructions, // Specific instructions for this ingredient
    @JsonKey(name: 'needs_preparation') @Default(false) bool needsPreparation, // Whether this ingredient needs preparation
  }) = _MealIngredient;

  factory MealIngredient.fromJson(Map<String, dynamic> json) =>
      _$MealIngredientFromJson(json);
}

@freezed
class NutritionInfo with _$NutritionInfo {
  const factory NutritionInfo({
    required int calories,
    required double protein,
    required double carbs,
    required double fat,
    @Default(0.0) double fiber,
  }) = _NutritionInfo;

  factory NutritionInfo.fromJson(Map<String, dynamic> json) =>
      _$NutritionInfoFromJson(json);
}

@freezed
class MealVersion with _$MealVersion {
  const factory MealVersion({
    required String id,
    required GeneratedMeal meal,
    required String title,
    required bool isOriginal,
    required DateTime createdAt,
  }) = _MealVersion;

  factory MealVersion.fromJson(Map<String, dynamic> json) =>
      _$MealVersionFromJson(json);
}

@freezed
class MealReplacement with _$MealReplacement {
  const factory MealReplacement({
    required String id,
    @JsonKey(name: 'replaced_at') @TimestampOrStringConverter() required DateTime replacedAt,
    required String reason, // e.g., "user_request", "nutritional_adjustment"
    @JsonKey(name: 'meal') required GeneratedMeal meal, // Renamed from replacementMeal
    @JsonKey(name: 'is_original') @Default(false) bool isOriginal, // Whether this is the original meal
  }) = _MealReplacement;

  factory MealReplacement.fromJson(Map<String, dynamic> json) =>
      _$MealReplacementFromJson(json);
}

@freezed
class MealConsumptionStatus with _$MealConsumptionStatus {
  const factory MealConsumptionStatus({
    required String mealPlanId,
    required DateTime mealDate,
    required String mealType, // breakfast, lunch, dinner, snack
    required String mealName,
    DateTime? consumedAt,
    @Default(false) bool isConsumed,
  }) = _MealConsumptionStatus;

  const MealConsumptionStatus._();

  factory MealConsumptionStatus.fromJson(Map<String, dynamic> json) =>
      _$MealConsumptionStatusFromJson(json);

  /// Generate unique key for this meal consumption status
  String get uniqueKey {
    final dateString = mealDate.toIso8601String().split('T')[0]; // YYYY-MM-DD
    return '${mealPlanId}_${dateString}_${mealType}';
  }
}

enum DietType {
  @JsonValue('normal')
  normal,
  @JsonValue('keto')
  keto,
  @JsonValue('mediterranean')
  mediterranean,
  @JsonValue('vegetarian')
  vegetarian,
  @JsonValue('vegan')
  vegan,
  @JsonValue('paleo')
  paleo,
  @JsonValue('low_carb')
  lowCarb,
  @JsonValue('high_protein')
  highProtein,
}

extension DietTypeExtension on DietType {
  String get displayName {
    switch (this) {
      case DietType.normal:
        return 'عادي';
      case DietType.keto:
        return 'كيتو';
      case DietType.mediterranean:
        return 'البحر الأبيض المتوسط';
      case DietType.vegetarian:
        return 'نباتي';
      case DietType.vegan:
        return 'نباتي صرف';
      case DietType.paleo:
        return 'باليو';
      case DietType.lowCarb:
        return 'قليل الكربوهيدرات';
      case DietType.highProtein:
        return 'عالي البروتين';
    }
  }

  String get description {
    switch (this) {
      case DietType.normal:
        return 'نظام غذائي متوازن ومتنوع';
      case DietType.keto:
        return 'عالي الدهون، قليل الكربوهيدرات';
      case DietType.mediterranean:
        return 'غني بالخضار والأسماك وزيت الزيتون';
      case DietType.vegetarian:
        return 'بدون لحوم، يشمل منتجات الألبان';
      case DietType.vegan:
        return 'نباتي بالكامل، بدون منتجات حيوانية';
      case DietType.paleo:
        return 'أطعمة طبيعية غير معالجة';
      case DietType.lowCarb:
        return 'تقليل الكربوهيدرات لفقدان الوزن';
      case DietType.highProtein:
        return 'عالي البروتين لبناء العضلات';
    }
  }
}
