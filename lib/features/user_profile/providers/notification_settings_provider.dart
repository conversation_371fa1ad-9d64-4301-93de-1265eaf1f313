import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/shared/providers/app_state_provider.dart';
import 'package:easydietai/features/user_profile/data/models/notification_settings_models.dart';
import 'package:easydietai/features/user_profile/data/services/notification_settings_service.dart';

part 'notification_settings_provider.freezed.dart';
part 'notification_settings_provider.g.dart';

@freezed
class NotificationSettingsState with _$NotificationSettingsState {
  const factory NotificationSettingsState({
    NotificationSettingsData? notificationSettings,
    @Default(false) bool isLoading,
    @Default(false) bool isUpdating,
    @Default(false) bool isRefreshing,
    String? error,
    String? updatingMessage,
  }) = _NotificationSettingsState;
}

@riverpod
class NotificationSettingsNotifier extends _$NotificationSettingsNotifier {
  StreamSubscription<Map<String, dynamic>?>? _firestoreListener;
  Timer? _loadingFlagTimeoutTimer;

  @override
  NotificationSettingsState build() {
    // Set up Firestore listener when provider is built
    // _setupFirestoreListener();

    // Load initial data
    Future.microtask(() => loadNotificationSettings());

    return const NotificationSettingsState();
  }

  /// Set up Firestore listener for real-time updates
  // void _setupFirestoreListener() {
  //   final currentUser = ref.read(currentUserProvider);
  //   if (currentUser == null) return;
  //
  //   AppLogger.info('Setting up Firestore listener for notification settings');
  //
  //   _firestoreListener = ref
  //       .read(firestoreServiceProvider)
  //       .listenToNotificationSettings()
  //       .listen(
  //     (Map<String, dynamic>? data) {
  //       if (data != null && data.isNotEmpty) {
  //         AppLogger.info('Received notification settings update from Firestore');
  //         _handleFirestoreUpdate(data);
  //       }
  //     },
  //     onError: (error) {
  //       AppLogger.error('Firestore listener error: $error');
  //       state = state.copyWith(error: 'Failed to sync notification settings: $error');
  //     },
  //   );
  // }

  /// Handle Firestore updates
  // Future<void> _handleFirestoreUpdate(Map<String, dynamic> data) async {
  //   try {
  //     final settings = NotificationSettingsData.fromJson(data);
  //
  //     // Update local storage
  //     final localStorageService = await ref.read(localStorageServiceProvider.future);
  //     await localStorageService.saveNotificationSettings(data);
  //
  //     // Update state
  //     state = state.copyWith(
  //       notificationSettings: settings,
  //       isLoading: false,
  //       error: null,
  //     );
  //
  //     AppLogger.info('Notification settings updated from Firestore');
  //   } catch (e) {
  //     AppLogger.error('Failed to handle Firestore update: $e');
  //     state = state.copyWith(error: 'Failed to process notification settings update: $e');
  //   }
  // }

  /// Load notification settings
  Future<void> loadNotificationSettings() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = await ref.read(notificationSettingsServiceProvider.future);
      final result = await service.loadNotificationSettings();

      if (result.success && result.data != null) {
        state = state.copyWith(
          notificationSettings: result.data,
          isLoading: false,
          error: null,
        );
        AppLogger.info('Notification settings loaded successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error ?? 'Failed to load notification settings',
        );
        AppLogger.error('Failed to load notification settings: ${result.error}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load notification settings: $e',
      );
      AppLogger.error('Exception loading notification settings: $e');
    }
  }

  /// Update a specific category setting
  Future<void> updateCategorySetting(NotificationCategorySetting setting) async {
    if (state.isUpdating) return;

    state = state.copyWith(
      isUpdating: true,
      updatingMessage: 'جاري تحديث إعدادات ${setting.category.displayName}...',
      error: null,
    );

    try {
      final service = await ref.read(notificationSettingsServiceProvider.future);
      final result = await service.updateCategorySetting(setting);

      if (result.success && result.data != null) {
        state = state.copyWith(
          notificationSettings: result.data,
          isUpdating: false,
          updatingMessage: null,
          error: null,
        );
        AppLogger.info('Category setting updated successfully: ${setting.category}');
      } else {
        state = state.copyWith(
          isUpdating: false,
          updatingMessage: null,
          error: result.error ?? 'Failed to update category setting',
        );
        AppLogger.error('Failed to update category setting: ${result.error}');
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        updatingMessage: null,
        error: 'Failed to update category setting: $e',
      );
      AppLogger.error('Exception updating category setting: $e');
    }
  }

  /// Initialize default settings for new users
  Future<void> initializeDefaultSettings() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final service = await ref.read(notificationSettingsServiceProvider.future);
      final result = await service.initializeDefaultSettings();

      if (result.success && result.data != null) {
        state = state.copyWith(
          notificationSettings: result.data,
          isLoading: false,
          error: null,
        );
        AppLogger.info('Default notification settings initialized successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error ?? 'Failed to initialize default settings',
        );
        AppLogger.error('Failed to initialize default settings: ${result.error}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to initialize default settings: $e',
      );
      AppLogger.error('Exception initializing default settings: $e');
    }
  }

  /// Refresh notification settings from Firestore
  Future<void> refreshNotificationSettings() async {
    if (state.isRefreshing) return;

    state = state.copyWith(isRefreshing: true, error: null);

    try {
      // Force reload from Firestore
      final firestoreData = await ref.read(firestoreServiceProvider).getNotificationSettings();

      if (firestoreData != null && firestoreData.isNotEmpty) {
        final settings = NotificationSettingsData.fromJson(firestoreData);

        // Update local storage
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.saveNotificationSettings(firestoreData);

        state = state.copyWith(
          notificationSettings: settings,
          isRefreshing: false,
          error: null,
        );
        AppLogger.info('Notification settings refreshed from Firestore');
      } else {
        // Initialize default if no settings exist
        await initializeDefaultSettings();
        state = state.copyWith(isRefreshing: false);
      }
    } catch (e) {
      state = state.copyWith(
        isRefreshing: false,
        error: 'Failed to refresh notification settings: $e',
      );
      AppLogger.error('Exception refreshing notification settings: $e');
    }
  }

  /// Clear notification settings
  Future<void> clearNotificationSettings() async {
    try {
      final service = await ref.read(notificationSettingsServiceProvider.future);
      await service.clearNotificationSettings();
      
      state = state.copyWith(
        notificationSettings: null,
        isLoading: false,
        isUpdating: false,
        isRefreshing: false,
        error: null,
        updatingMessage: null,
      );
      
      AppLogger.info('Notification settings cleared');
    } catch (e) {
      AppLogger.error('Failed to clear notification settings: $e');
      state = state.copyWith(error: 'Failed to clear notification settings: $e');
    }
  }

  void dispose() {
    _firestoreListener?.cancel();
    _loadingFlagTimeoutTimer?.cancel();
  }
}

/// Provider for current notification settings data
@riverpod
NotificationSettingsData? currentNotificationSettingsData(CurrentNotificationSettingsDataRef ref) {
  final state = ref.watch(notificationSettingsNotifierProvider);
  return state.notificationSettings;
}

/// Provider for a specific category setting
@riverpod
NotificationCategorySetting? notificationCategorySetting(
  NotificationCategorySettingRef ref,
  NotificationCategory category,
) {
  final settings = ref.watch(currentNotificationSettingsDataProvider);
  return settings?.getCategorySetting(category);
}
