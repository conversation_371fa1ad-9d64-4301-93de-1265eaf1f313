import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_settings_models.freezed.dart';
part 'notification_settings_models.g.dart';

/// Enum for notification categories
enum NotificationCategory {
  @JsonValue('meal_reminders')
  mealReminders,
  @JsonValue('water_reminders')
  waterReminders,
  @JsonValue('workout_reminders')
  workoutReminders,
  @JsonValue('progress_updates')
  progressUpdates,
}

/// Extension for notification category display
extension NotificationCategoryExtension on NotificationCategory {
  String get displayName {
    switch (this) {
      case NotificationCategory.mealReminders:
        return 'تذكير الوجبات';
      case NotificationCategory.waterReminders:
        return 'تذكير شرب الماء';
      case NotificationCategory.workoutReminders:
        return 'تذكير التمارين';
      case NotificationCategory.progressUpdates:
        return 'تحديثات التقدم';
    }
  }

  String get description {
    switch (this) {
      case NotificationCategory.mealReminders:
        return 'احصل على تذكير عندما يحين وقت تناول وجباتك';
      case NotificationCategory.waterReminders:
        return 'احصل على تذكير لشرب الماء بانتظام';
      case NotificationCategory.workoutReminders:
        return 'احصل على تذكير لممارسة التمارين الرياضية';
      case NotificationCategory.progressUpdates:
        return 'احصل على تحديثات حول تقدمك وإنجازاتك';
    }
  }

  String get icon {
    switch (this) {
      case NotificationCategory.mealReminders:
        return '🍽️';
      case NotificationCategory.waterReminders:
        return '💧';
      case NotificationCategory.workoutReminders:
        return '💪';
      case NotificationCategory.progressUpdates:
        return '📈';
    }
  }
}

/// Model for meal reminder settings
@freezed
class MealReminderSettings with _$MealReminderSettings {
  const factory MealReminderSettings({
    @JsonKey(name: 'breakfast_time') @Default('08:00') String breakfastTime,
    @JsonKey(name: 'lunch_time') @Default('13:00') String lunchTime,
    @JsonKey(name: 'dinner_time') @Default('19:00') String dinnerTime,
    @JsonKey(name: 'snack_reminders') @Default(true) bool snackReminders,
    @JsonKey(name: 'reminder_minutes_before') @Default(15) int reminderMinutesBefore,
  }) = _MealReminderSettings;

  factory MealReminderSettings.fromJson(Map<String, dynamic> json) =>
      _$MealReminderSettingsFromJson(json);
}

/// Model for water reminder settings
@freezed
class WaterReminderSettings with _$WaterReminderSettings {
  const factory WaterReminderSettings({
    @JsonKey(name: 'interval_hours') @Default(2) int intervalHours,
    @JsonKey(name: 'start_time') @Default('07:00') String startTime,
    @JsonKey(name: 'end_time') @Default('22:00') String endTime,
    @JsonKey(name: 'daily_goal_ml') @Default(2000) int dailyGoalMl,
    @JsonKey(name: 'smart_reminders') @Default(true) bool smartReminders,
  }) = _WaterReminderSettings;

  factory WaterReminderSettings.fromJson(Map<String, dynamic> json) =>
      _$WaterReminderSettingsFromJson(json);
}

/// Model for workout reminder settings
@freezed
class WorkoutReminderSettings with _$WorkoutReminderSettings {
  const factory WorkoutReminderSettings({
    @JsonKey(name: 'preferred_time') @Default('18:00') String preferredTime,
    @JsonKey(name: 'reminder_days') @Default(['monday', 'wednesday', 'friday']) List<String> reminderDays,
    @JsonKey(name: 'rest_day_reminders') @Default(false) bool restDayReminders,
    @JsonKey(name: 'motivational_quotes') @Default(true) bool motivationalQuotes,
  }) = _WorkoutReminderSettings;

  factory WorkoutReminderSettings.fromJson(Map<String, dynamic> json) =>
      _$WorkoutReminderSettingsFromJson(json);
}

/// Model for progress update settings
@freezed
class ProgressUpdateSettings with _$ProgressUpdateSettings {
  const factory ProgressUpdateSettings({
    @JsonKey(name: 'preferred_unit') @Default('kg') String preferredUnit,
    @JsonKey(name: 'reminder_interval_days') @Default(7) int reminderIntervalDays,
    @JsonKey(name: 'show_progress_chart') @Default(true) bool showProgressChart,
    @JsonKey(name: 'show_weight_change_indicators') @Default(true) bool showWeightChangeIndicators,
    @JsonKey(name: 'weekly_summary') @Default(true) bool weeklySummary,
  }) = _ProgressUpdateSettings;

  factory ProgressUpdateSettings.fromJson(Map<String, dynamic> json) =>
      _$ProgressUpdateSettingsFromJson(json);
}

/// Model for a single notification category setting
@freezed
class NotificationCategorySetting with _$NotificationCategorySetting {
  const factory NotificationCategorySetting({
    required NotificationCategory category,
    @Default(true) bool isActive,
    MealReminderSettings? mealSettings,
    WaterReminderSettings? waterSettings,
    WorkoutReminderSettings? workoutSettings,
    ProgressUpdateSettings? progressSettings,
  }) = _NotificationCategorySetting;

  factory NotificationCategorySetting.fromJson(Map<String, dynamic> json) =>
      _$NotificationCategorySettingFromJson(json);
}

/// Extension for notification category setting
extension NotificationCategorySettingExtension on NotificationCategorySetting {
  /// Get the specific settings for this category
  dynamic get specificSettings {
    switch (category) {
      case NotificationCategory.mealReminders:
        return mealSettings ?? const MealReminderSettings();
      case NotificationCategory.waterReminders:
        return waterSettings ?? const WaterReminderSettings();
      case NotificationCategory.workoutReminders:
        return workoutSettings ?? const WorkoutReminderSettings();
      case NotificationCategory.progressUpdates:
        return progressSettings ?? const ProgressUpdateSettings();
    }
  }
}

/// Main notification settings model
@freezed
class NotificationSettingsData with _$NotificationSettingsData {
  const factory NotificationSettingsData({
    @JsonKey(name: 'meal_reminders') @Default(NotificationCategorySetting(category: NotificationCategory.mealReminders)) NotificationCategorySetting mealReminders,
    @JsonKey(name: 'water_reminders') @Default(NotificationCategorySetting(category: NotificationCategory.waterReminders)) NotificationCategorySetting waterReminders,
    @JsonKey(name: 'workout_reminders') @Default(NotificationCategorySetting(category: NotificationCategory.workoutReminders)) NotificationCategorySetting workoutReminders,
    @JsonKey(name: 'progress_updates') @Default(NotificationCategorySetting(category: NotificationCategory.progressUpdates)) NotificationCategorySetting progressUpdates,
    @JsonKey(name: 'updated_at') DateTime? updatedAt,
  }) = _NotificationSettingsData;

  factory NotificationSettingsData.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsDataFromJson(json);
}

/// Extension for notification settings data
extension NotificationSettingsDataExtension on NotificationSettingsData {
  /// Get all notification categories as a list
  List<NotificationCategorySetting> get allCategories {
    return [mealReminders, waterReminders, workoutReminders, progressUpdates];
  }

  /// Get a specific category setting
  NotificationCategorySetting getCategorySetting(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.mealReminders:
        return mealReminders;
      case NotificationCategory.waterReminders:
        return waterReminders;
      case NotificationCategory.workoutReminders:
        return workoutReminders;
      case NotificationCategory.progressUpdates:
        return progressUpdates;
    }
  }

  /// Update a specific category setting
  NotificationSettingsData updateCategorySetting(NotificationCategorySetting setting) {
    switch (setting.category) {
      case NotificationCategory.mealReminders:
        return copyWith(mealReminders: setting, updatedAt: DateTime.now());
      case NotificationCategory.waterReminders:
        return copyWith(waterReminders: setting, updatedAt: DateTime.now());
      case NotificationCategory.workoutReminders:
        return copyWith(workoutReminders: setting, updatedAt: DateTime.now());
      case NotificationCategory.progressUpdates:
        return copyWith(progressUpdates: setting, updatedAt: DateTime.now());
    }
  }
}

/// Factory for creating default notification settings
class NotificationSettingsFactory {
  static NotificationSettingsData createDefault() {
    return NotificationSettingsData(
      mealReminders: NotificationCategorySetting(
        category: NotificationCategory.mealReminders,
        isActive: true,
        mealSettings: const MealReminderSettings(),
      ),
      waterReminders: NotificationCategorySetting(
        category: NotificationCategory.waterReminders,
        isActive: true,
        waterSettings: const WaterReminderSettings(),
      ),
      workoutReminders: NotificationCategorySetting(
        category: NotificationCategory.workoutReminders,
        isActive: true,
        workoutSettings: const WorkoutReminderSettings(),
      ),
      progressUpdates: NotificationCategorySetting(
        category: NotificationCategory.progressUpdates,
        isActive: true,
        progressSettings: const ProgressUpdateSettings(),
      ),
      updatedAt: DateTime.now(),
    );
  }
}
