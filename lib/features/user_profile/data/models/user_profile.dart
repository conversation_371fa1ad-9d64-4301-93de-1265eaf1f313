import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'display_name') required String displayName,
    @Json<PERSON>ey(name: 'photo_url') String? photoUrl,
    @J<PERSON><PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
    @Json<PERSON><PERSON>(name: 'preferred_language') @Default('ar') String preferredLanguage,
    @<PERSON><PERSON><PERSON>ey(name: 'unit_system') @Default('metric') String unitSystem,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_premium') @Default(false) bool isPremium,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'onboarding_completed') @Default(false) bool onboardingCompleted,

    // Timestamps
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login_at') DateTime? lastLoginAt,
    @Json<PERSON><PERSON>(name: 'premium_expiry_date') DateTime? premiumExpiryDate,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}

enum Gender {
  @JsonValue('male')
  male,
  @JsonValue('female')
  female,
  @JsonValue('not_specified')
  notSpecified,
}

enum ActivityLevel {
  @JsonValue('sedentary')
  sedentary,
  @JsonValue('light')
  light,
  @JsonValue('moderate')
  moderate,
  @JsonValue('active')
  active,
  @JsonValue('very_active')
  veryActive,
}

enum HealthGoal {
  @JsonValue('lose_weight')
  loseWeight,
  @JsonValue('maintain')
  maintain,
  @JsonValue('gain_weight')
  gainWeight,
  @JsonValue('build_muscle')
  buildMuscle,
  @JsonValue('improve_health')
  improveHealth,
}
