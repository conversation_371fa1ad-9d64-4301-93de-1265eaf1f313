import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../../../../core/services/local_storage_service.dart';
import '../../../../core/services/firestore_service.dart';
import '../../../../core/utils/logger.dart';
import '../models/notification_settings_models.dart';

/// Provider for notification settings service
final notificationSettingsServiceProvider = FutureProvider<NotificationSettingsService>((ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  
  return FirebaseNotificationSettingsService(
    localStorageService,
    firestoreService,
  );
});

/// Result class for notification settings operations
class NotificationSettingsOperationResult {
  const NotificationSettingsOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final NotificationSettingsData? data;
  final String? error;
}

/// Abstract base class for notification settings service
abstract class NotificationSettingsService {
  /// Load notification settings from local storage first, then Firestore if not found
  Future<NotificationSettingsOperationResult> loadNotificationSettings();
  
  /// Save notification settings to both local storage and Firestore
  Future<NotificationSettingsOperationResult> saveNotificationSettings(NotificationSettingsData settings);
  
  /// Update a specific category setting
  Future<NotificationSettingsOperationResult> updateCategorySetting(NotificationCategorySetting setting);
  
  /// Initialize default settings for new users
  Future<NotificationSettingsOperationResult> initializeDefaultSettings();
  
  /// Clear all notification settings
  Future<void> clearNotificationSettings();
}

/// Firebase implementation of notification settings service
class FirebaseNotificationSettingsService implements NotificationSettingsService {
  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;

  const FirebaseNotificationSettingsService(
    this._localStorageService,
    this._firestoreService,
  );

  @override
  Future<NotificationSettingsOperationResult> loadNotificationSettings() async {
    try {
      AppLogger.info('Loading notification settings...');
      
      // Try local storage first
      final localData = _localStorageService.loadNotificationSettings();
      if (localData != null && localData.isNotEmpty) {
        AppLogger.info('Found notification settings in local storage');
        final settings = NotificationSettingsData.fromJson(localData);
        return NotificationSettingsOperationResult(success: true, data: settings);
      }

      // Fallback to Firestore
      AppLogger.info('No local notification settings found, checking Firestore...');
      final firestoreData = await _firestoreService.getNotificationSettings();
      if (firestoreData != null && firestoreData.isNotEmpty) {
        AppLogger.info('Found notification settings in Firestore, syncing to local storage');
        final settings = NotificationSettingsData.fromJson(firestoreData);
        
        // Save to local storage for future use
        await _localStorageService.saveNotificationSettings(firestoreData);
        
        return NotificationSettingsOperationResult(success: true, data: settings);
      }

      // No settings found, return default
      AppLogger.info('No notification settings found, using defaults');
      final defaultSettings = NotificationSettingsFactory.createDefault();
      return NotificationSettingsOperationResult(success: true, data: defaultSettings);
      
    } catch (e) {
      AppLogger.error('Failed to load notification settings: $e');
      return NotificationSettingsOperationResult(
        success: false,
        error: 'Failed to load notification settings: $e',
      );
    }
  }

  @override
  Future<NotificationSettingsOperationResult> saveNotificationSettings(NotificationSettingsData settings) async {
    try {
      AppLogger.info('Saving notification settings...');
      
      final settingsJson = settings.copyWith(updatedAt: DateTime.now()).toJson();
      
      // Save to local storage first
      await _localStorageService.saveNotificationSettings(settingsJson);
      AppLogger.info('Notification settings saved to local storage');
      
      // Save to Firestore
      await _firestoreService.saveNotificationSettings(settingsJson);
      AppLogger.info('Notification settings saved to Firestore');
      
      return NotificationSettingsOperationResult(success: true, data: settings);
      
    } catch (e) {
      AppLogger.error('Failed to save notification settings: $e');
      return NotificationSettingsOperationResult(
        success: false,
        error: 'Failed to save notification settings: $e',
      );
    }
  }

  @override
  Future<NotificationSettingsOperationResult> updateCategorySetting(NotificationCategorySetting setting) async {
    try {
      AppLogger.info('Updating notification category setting: ${setting.category}');
      
      // Load current settings
      final loadResult = await loadNotificationSettings();
      if (!loadResult.success || loadResult.data == null) {
        return NotificationSettingsOperationResult(
          success: false,
          error: 'Failed to load current settings: ${loadResult.error}',
        );
      }
      
      // Update the specific category
      final updatedSettings = loadResult.data!.updateCategorySetting(setting);
      
      // Save updated settings
      return await saveNotificationSettings(updatedSettings);
      
    } catch (e) {
      AppLogger.error('Failed to update category setting: $e');
      return NotificationSettingsOperationResult(
        success: false,
        error: 'Failed to update category setting: $e',
      );
    }
  }

  @override
  Future<NotificationSettingsOperationResult> initializeDefaultSettings() async {
    try {
      AppLogger.info('Initializing default notification settings...');
      
      final defaultSettings = NotificationSettingsFactory.createDefault();
      return await saveNotificationSettings(defaultSettings);
      
    } catch (e) {
      AppLogger.error('Failed to initialize default settings: $e');
      return NotificationSettingsOperationResult(
        success: false,
        error: 'Failed to initialize default settings: $e',
      );
    }
  }

  @override
  Future<void> clearNotificationSettings() async {
    try {
      AppLogger.info('Clearing notification settings...');
      
      // Clear from local storage
      await _localStorageService.clearNotificationSettings();
      AppLogger.info('Notification settings cleared from local storage');
      
    } catch (e) {
      AppLogger.error('Failed to clear notification settings: $e');
      rethrow;
    }
  }
}
