import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/core/utils/timestamp_utils.dart';
import 'package:easydietai/features/user_profile/data/models/user_profile.dart';
import 'package:easydietai/features/onboarding/data/models/plan_preferences.dart';
import 'package:easydietai/features/user_profile/data/models/notification_settings_models.dart';

/// Provider for user data service
final userDataServiceProvider = Provider<UserDataService>((ref) {
  final firestoreService = ref.watch(firestoreServiceProvider);
  return FirebaseUserDataService(firestoreService);
});

/// Complete user data from Firestore
class CompleteUserData {
  const CompleteUserData({
    this.userProfile,
    this.planPreferences,
    this.notificationSettingsData,
  });

  final UserProfile? userProfile;
  final PlanPreferences? planPreferences;
  final NotificationSettingsData? notificationSettingsData;
}

/// Abstract user data service interface
abstract class UserDataService {
  // User profile operations
  Future<void> saveUserProfile(UserProfile userProfile);
  Future<void> saveUserProfileWithoutPreferences(UserProfile userProfile);
  Future<UserProfile?> getUserProfile([String? userId]);
  Future<CompleteUserData?> getCompleteUserData([String? userId]);
  
  // Plan preferences operations
  Future<void> savePlanPreferences(Map<String, dynamic> planPreferences);
  Future<Map<String, dynamic>?> getPlanPreferences([String? userId]);
  Future<void> updatePlanPreferencesField(String fieldName, dynamic value);
  
  // Notification settings operations
  Future<void> saveNotificationSettings(Map<String, dynamic> notificationSettings);
  Future<Map<String, dynamic>?> getNotificationSettings([String? userId]);
  Stream<Map<String, dynamic>?> listenToNotificationSettings([String? userId]);
  
  // Real-time operations
  Stream<UserProfile?> listenToUserProfile([String? userId]);
}

/// Firebase implementation of user data service
class FirebaseUserDataService implements UserDataService {
  FirebaseUserDataService(this._firestoreService);

  final FirestoreService _firestoreService;

  String? get _currentUserId => FirebaseAuth.instance.currentUser?.uid;

  @override
  Future<void> saveUserProfile(UserProfile userProfile) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Saving complete user profile (with preferences) for $userId');

      // Save the complete profile data to the root level of the user document
      await _firestoreService.saveToUserDocument(
        data: userProfile.toJson(),
      );

      AppLogger.info('UserDataService: Complete user profile saved successfully');
    } catch (e) {
      AppLogger.error('UserDataService: Error saving complete user profile: $e');
      rethrow;
    }
  }

  @override
  Future<void> saveUserProfileWithoutPreferences(UserProfile userProfile) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Saving user profile without preferences for $userId');

      // Create a copy of the profile data and remove preference fields
      final profileJson = Map<String, dynamic>.from(userProfile.toJson());
      
      // Remove plan preference fields (should be in users/{userId}/plan_preferences)
      profileJson.remove('age');
      profileJson.remove('gender');
      profileJson.remove('height');
      profileJson.remove('weight');
      profileJson.remove('activity_level');
      profileJson.remove('goal');
      profileJson.remove('target_weight');
      profileJson.remove('weekly_goal');
      profileJson.remove('diet_type');
      profileJson.remove('allergies');
      profileJson.remove('disliked_foods');
      profileJson.remove('preferred_cuisines');
      profileJson.remove('cooking_time');
      profileJson.remove('budget_range');
      profileJson.remove('meals_per_day');
      profileJson.remove('snacks_per_day');
      profileJson.remove('daily_burned_calories');
      profileJson.remove('use_manual_calories');

      // Notification fields (should be in users/{userId}/notification_settings)
      profileJson.remove('meal_reminders');
      profileJson.remove('water_reminders');
      profileJson.remove('workout_reminders');
      profileJson.remove('progress_updates');

      // Save the cleaned profile data to the root level of the user document
      await _firestoreService.saveToUserDocument(
        data: profileJson,
      );

      AppLogger.info('UserDataService: User profile saved successfully without preferences');
    } catch (e) {
      AppLogger.error('UserDataService: Error saving user profile without preferences: $e');
      rethrow;
    }
  }

  @override
  Future<UserProfile?> getUserProfile([String? userId]) async {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Getting user profile for $uid');

      final userDoc = await _firestoreService.getDocument('users/$uid');

      if (userDoc == null) {
        AppLogger.info('UserDataService: User profile not found');
        return null;
      }

      final data = userDoc;

      // Get nested plan preferences and notification settings
      final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
      final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

      // Log the onboarding completion status from Firestore
      AppLogger.info('UserDataService: onboarding_completed from Firestore: ${data['onboarding_completed']}');

      // Merge plan preferences data into main profile data
      final mergedData = Map<String, dynamic>.from(data);
      if (planPreferences != null) {
        AppLogger.info('UserDataService: Merging plan preferences: ${planPreferences.keys.toList()}');
        _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
      }
      if (notificationSettings != null) {
        AppLogger.info('UserDataService: Merging notification settings: ${notificationSettings.keys.toList()}');
        _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
      }

      AppLogger.info('UserDataService: Final merged data onboarding_completed: ${mergedData['onboarding_completed']}');
      AppLogger.info('UserDataService: User profile retrieved and merged successfully');

      // Convert Firestore Timestamp objects to ISO string format
      final convertedData = TimestampUtils.convertFirestoreTimestampsToStrings(mergedData);
      return UserProfile.fromJson(convertedData);
    } catch (e) {
      AppLogger.error('UserDataService: Error getting user profile: $e');
      rethrow;
    }
  }

  @override
  Future<CompleteUserData?> getCompleteUserData([String? userId]) async {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Getting complete user data for $uid');

      final userDoc = await _firestoreService.getDocument('users/$uid');

      if (userDoc == null) {
        AppLogger.info('UserDataService: User profile not found');
        return null;
      }

      final data = userDoc;

      // Extract the separate components
      final userData = Map<String, dynamic>.from(data);
      final planPreferences = userData.remove('plan_preferences') as Map<String, dynamic>?;
      final notificationSettings = userData.remove('notification_settings') as Map<String, dynamic>?;

      // Convert Firestore Timestamp objects to ISO string format for all components
      final convertedUserData = TimestampUtils.convertFirestoreTimestampsToStrings(userData);
      final convertedPlanPreferences = planPreferences != null ? TimestampUtils.convertFirestoreTimestampsToStrings(planPreferences) : null;
      final convertedNotificationSettings = notificationSettings != null ? TimestampUtils.convertFirestoreTimestampsToStrings(notificationSettings) : null;

      // Create models from the separate data
      UserProfile? userProfile;
      PlanPreferences? planPreferencesModel;
      NotificationSettingsData? notificationSettingsModel;

      if (convertedUserData.isNotEmpty) {
        userProfile = UserProfile.fromJson(convertedUserData);
      }

      if (convertedPlanPreferences != null) {
        planPreferencesModel = PlanPreferences.fromJson(convertedPlanPreferences);
      }

      if (convertedNotificationSettings != null) {
        notificationSettingsModel = NotificationSettingsData.fromJson(convertedNotificationSettings);
      }

      return CompleteUserData(
        userProfile: userProfile,
        planPreferences: planPreferencesModel,
        notificationSettingsData: notificationSettingsModel,
      );
    } catch (e) {
      AppLogger.error('UserDataService: Error getting complete user data: $e');
      rethrow;
    }
  }

  @override
  Future<void> savePlanPreferences(Map<String, dynamic> planPreferences) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Saving plan preferences for $userId');

      // Update plan preferences with timestamp
      final planPreferencesData = {
        ...planPreferences,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await _firestoreService.updateDocument(
        path: 'users/$userId',
        data: {
          'plan_preferences': planPreferencesData,
          'updated_at': FieldValue.serverTimestamp(),
        },
        merge: true,
      );

      AppLogger.info('UserDataService: Plan preferences saved successfully');
    } catch (e) {
      AppLogger.error('UserDataService: Error saving plan preferences: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getPlanPreferences([String? userId]) async {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Getting plan preferences for $uid');

      final userDoc = await _firestoreService.getDocument('users/$uid');

      if (userDoc == null) {
        AppLogger.info('UserDataService: User document not found');
        return null;
      }

      return userDoc['plan_preferences'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('UserDataService: Error getting plan preferences: $e');
      rethrow;
    }
  }

  @override
  Future<void> updatePlanPreferencesField(String fieldName, dynamic value) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Updating plan preferences field $fieldName for $userId');

      // Update specific field in plan preferences
      await _firestoreService.updateDocument(
        path: 'users/$userId',
        data: {
          'plan_preferences.$fieldName': value,
          'plan_preferences.updated_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
        },
      );

      AppLogger.info('UserDataService: Plan preferences field updated successfully');
    } catch (e) {
      AppLogger.error('UserDataService: Error updating plan preferences field: $e');
      rethrow;
    }
  }

  @override
  Future<void> saveNotificationSettings(Map<String, dynamic> notificationSettings) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Saving notification settings for $userId');

      // Update notification settings with timestamp
      final notificationData = {
        ...notificationSettings,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await _firestoreService.updateDocument(
        path: 'users/$userId',
        data: {
          'notification_settings': notificationData,
          'updated_at': FieldValue.serverTimestamp(),
        },
        merge: true,
      );

      AppLogger.info('UserDataService: Notification settings saved successfully');
    } catch (e) {
      AppLogger.error('UserDataService: Error saving notification settings: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getNotificationSettings([String? userId]) async {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('UserDataService: Getting notification settings for $uid');

      final userDoc = await _firestoreService.getDocument('users/$uid');

      if (userDoc == null) {
        AppLogger.info('UserDataService: User document not found');
        return null;
      }

      return userDoc['notification_settings'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('UserDataService: Error getting notification settings: $e');
      rethrow;
    }
  }

  @override
  Stream<Map<String, dynamic>?> listenToNotificationSettings([String? userId]) {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        AppLogger.warning('UserDataService: No authenticated user for notification settings listener');
        return Stream.value(null);
      }

      AppLogger.info('UserDataService: Setting up notification settings listener for $uid');

      return _firestoreService.getDocumentStream('users/$uid').map((snapshot) {
        if (snapshot == null) {
          return null;
        }

        return snapshot['notification_settings'] as Map<String, dynamic>?;
      });
    } catch (e) {
      AppLogger.error('UserDataService: Error setting up notification settings listener: $e');
      return Stream.value(null);
    }
  }

  @override
  Stream<UserProfile?> listenToUserProfile([String? userId]) {
    try {
      final uid = userId ?? _currentUserId;
      if (uid == null) {
        AppLogger.warning('UserDataService: No authenticated user for user profile listener');
        return Stream.value(null);
      }

      AppLogger.info('UserDataService: Setting up user profile listener for $uid');

      return _firestoreService.getDocumentStream('users/$uid').map((snapshot) {
        if (snapshot == null) {
          return null;
        }

        final data = snapshot;

        // Get nested plan preferences and notification settings
        final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
        final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

        // Merge plan preferences data into main profile data
        final mergedData = Map<String, dynamic>.from(data);
        if (planPreferences != null) {
          _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
        }
        if (notificationSettings != null) {
          _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
        }

        // Convert Firestore Timestamp objects to ISO string format
        final convertedData = TimestampUtils.convertFirestoreTimestampsToStrings(mergedData);
        return UserProfile.fromJson(convertedData);
      });
    } catch (e) {
      AppLogger.error('UserDataService: Error setting up user profile listener: $e');
      return Stream.value(null);
    }
  }

  /// Merge plan preferences data into profile data for UserProfile creation
  void _mergePlanPreferencesIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> planPreferences) {
    if (planPreferences.containsKey('age')) profileData['age'] = planPreferences['age'];
    if (planPreferences.containsKey('gender')) profileData['gender'] = planPreferences['gender'];
    if (planPreferences.containsKey('height')) profileData['height'] = planPreferences['height'];
    if (planPreferences.containsKey('weight')) profileData['weight'] = planPreferences['weight'];
    if (planPreferences.containsKey('activity_level')) profileData['activity_level'] = planPreferences['activity_level'];
    if (planPreferences.containsKey('goal')) profileData['goal'] = planPreferences['goal'];
    if (planPreferences.containsKey('target_weight')) profileData['target_weight'] = planPreferences['target_weight'];
    if (planPreferences.containsKey('weekly_goal')) profileData['weekly_goal'] = planPreferences['weekly_goal'];
    if (planPreferences.containsKey('diet_type')) profileData['diet_type'] = planPreferences['diet_type'];
    if (planPreferences.containsKey('allergies')) profileData['allergies'] = planPreferences['allergies'];
    if (planPreferences.containsKey('disliked_foods')) profileData['disliked_foods'] = planPreferences['disliked_foods'];
    if (planPreferences.containsKey('preferred_cuisines')) profileData['preferred_cuisines'] = planPreferences['preferred_cuisines'];
    if (planPreferences.containsKey('cooking_time')) profileData['cooking_time'] = planPreferences['cooking_time'];
    if (planPreferences.containsKey('budget_range')) profileData['budget_range'] = planPreferences['budget_range'];
    if (planPreferences.containsKey('meals_per_day')) profileData['meals_per_day'] = planPreferences['meals_per_day'];
    if (planPreferences.containsKey('snacks_per_day')) profileData['snacks_per_day'] = planPreferences['snacks_per_day'];
    if (planPreferences.containsKey('daily_burned_calories')) profileData['daily_burned_calories'] = planPreferences['daily_burned_calories'];
    if (planPreferences.containsKey('use_manual_calories')) profileData['use_manual_calories'] = planPreferences['use_manual_calories'];
  }

  /// Merge notification settings data into profile data for UserProfile creation
  void _mergeNotificationSettingsIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> notificationSettings) {
    if (notificationSettings.containsKey('meal_reminders')) profileData['meal_reminders'] = notificationSettings['meal_reminders'];
    if (notificationSettings.containsKey('water_reminders')) profileData['water_reminders'] = notificationSettings['water_reminders'];
    if (notificationSettings.containsKey('workout_reminders')) profileData['workout_reminders'] = notificationSettings['workout_reminders'];
    if (notificationSettings.containsKey('progress_updates')) profileData['progress_updates'] = notificationSettings['progress_updates'];
  }


}
