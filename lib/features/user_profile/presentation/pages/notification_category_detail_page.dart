import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../data/models/notification_settings_models.dart';
import '../../providers/notification_settings_provider.dart';
import '../widgets/meal_reminder_settings_widget.dart';
import '../widgets/water_reminder_settings_widget.dart';
import '../widgets/workout_reminder_settings_widget.dart';
import '../widgets/progress_update_settings_widget.dart';

class NotificationCategoryDetailPage extends ConsumerStatefulWidget {
  final String categoryName;

  const NotificationCategoryDetailPage({
    super.key,
    required this.categoryName,
  });

  @override
  ConsumerState<NotificationCategoryDetailPage> createState() => _NotificationCategoryDetailPageState();
}

class _NotificationCategoryDetailPageState extends ConsumerState<NotificationCategoryDetailPage> {
  NotificationCategory? _category;

  @override
  void initState() {
    super.initState();
    _category = _getCategoryFromName(widget.categoryName);
  }

  NotificationCategory? _getCategoryFromName(String name) {
    try {
      return NotificationCategory.values.firstWhere(
        (category) => category.name == name,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    if (_category == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('خطأ'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
        ),
        body: const Center(
          child: Text('نوع الإشعار غير صحيح'),
        ),
      );
    }

    final notificationState = ref.watch(notificationSettingsNotifierProvider);
    final categorySetting = ref.watch(notificationCategorySettingProvider(_category!));

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          _category!.displayName,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          if (notificationState.isUpdating)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: notificationState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : notificationState.error != null
              ? _buildErrorState(context, notificationState.error!)
              : categorySetting == null
                  ? const Center(child: Text('لا توجد إعدادات لهذا النوع'))
                  : _buildCategorySettings(context, categorySetting),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل الإعدادات',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => ref.read(notificationSettingsNotifierProvider.notifier).loadNotificationSettings(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySettings(BuildContext context, NotificationCategorySetting setting) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main toggle switch
          _buildMainToggle(context, setting),
          
          const SizedBox(height: 24),
          
          // Category-specific settings
          if (setting.isActive) ...[
            Text(
              'إعدادات ${setting.category.displayName}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSpecificSettings(context, setting),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'قم بتفعيل ${setting.category.displayName} لعرض الإعدادات المتاحة',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMainToggle(BuildContext context, NotificationCategorySetting setting) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: setting.isActive 
                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                  : Theme.of(context).colorScheme.outline.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              setting.category.icon,
              style: const TextStyle(fontSize: 24),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  setting.category.displayName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  setting.category.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: setting.isActive,
            onChanged: (value) => _updateCategoryActive(setting, value),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecificSettings(BuildContext context, NotificationCategorySetting setting) {
    switch (setting.category) {
      case NotificationCategory.mealReminders:
        return MealReminderSettingsWidget(
          settings: setting.mealSettings ?? const MealReminderSettings(),
          onSettingsChanged: (newSettings) => _updateCategorySettings(setting, mealSettings: newSettings),
        );
      case NotificationCategory.waterReminders:
        return WaterReminderSettingsWidget(
          settings: setting.waterSettings ?? const WaterReminderSettings(),
          onSettingsChanged: (newSettings) => _updateCategorySettings(setting, waterSettings: newSettings),
        );
      case NotificationCategory.workoutReminders:
        return WorkoutReminderSettingsWidget(
          settings: setting.workoutSettings ?? const WorkoutReminderSettings(),
          onSettingsChanged: (newSettings) => _updateCategorySettings(setting, workoutSettings: newSettings),
        );
      case NotificationCategory.progressUpdates:
        return ProgressUpdateSettingsWidget(
          settings: setting.progressSettings ?? const ProgressUpdateSettings(),
          onSettingsChanged: (newSettings) => _updateCategorySettings(setting, progressSettings: newSettings),
        );
    }
  }

  void _updateCategoryActive(NotificationCategorySetting setting, bool isActive) {
    final updatedSetting = setting.copyWith(isActive: isActive);
    ref.read(notificationSettingsNotifierProvider.notifier).updateCategorySetting(updatedSetting);
  }

  void _updateCategorySettings(
    NotificationCategorySetting setting, {
    MealReminderSettings? mealSettings,
    WaterReminderSettings? waterSettings,
    WorkoutReminderSettings? workoutSettings,
    ProgressUpdateSettings? progressSettings,
  }) {
    final updatedSetting = setting.copyWith(
      mealSettings: mealSettings,
      waterSettings: waterSettings,
      workoutSettings: workoutSettings,
      progressSettings: progressSettings,
    );
    ref.read(notificationSettingsNotifierProvider.notifier).updateCategorySetting(updatedSetting);
  }
}
