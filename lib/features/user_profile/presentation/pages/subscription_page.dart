import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/providers/app_state_provider.dart';

class SubscriptionPage extends ConsumerWidget {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProfile = ref.watch(currentUserProfileProvider);
    final isPremium = userProfile?.isPremium ?? false;

    return Scaffold(
      appBar: AppBar(
        title: const Text('الاشتراك المميز'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Premium Status Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: isPremium 
                    ? AppColors.primaryGradient
                    : LinearGradient(
                        colors: [AppColors.secondary.withOpacity(0.1), AppColors.secondary.withOpacity(0.05)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isPremium ? AppColors.primary : AppColors.secondary.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    isPremium ? Icons.star : Icons.star_outline,
                    size: 48,
                    color: isPremium ? Colors.white : AppColors.secondary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    isPremium ? 'عضو مميز' : 'اشترك الآن',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isPremium ? Colors.white : AppColors.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isPremium 
                        ? 'أنت تستمتع بجميع المزايا المميزة'
                        : 'احصل على مزايا إضافية مع الاشتراك المميز',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isPremium ? Colors.white.withOpacity(0.9) : AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Features Section
            _buildFeaturesSection(context, isPremium),

            const SizedBox(height: 24),

            // Pricing Section (only show if not premium)
            if (!isPremium) _buildPricingSection(context),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context, bool isPremium) {
    final features = [
      FeatureItem(
        icon: Icons.restaurant_menu,
        title: 'خطط وجبات غير محدودة',
        description: 'إنشاء خطط وجبات متنوعة بلا حدود',
        isIncluded: true,
      ),
      FeatureItem(
        icon: Icons.analytics,
        title: 'تحليل غذائي متقدم',
        description: 'تقارير مفصلة عن العناصر الغذائية',
        isIncluded: isPremium,
      ),
      FeatureItem(
        icon: Icons.shopping_cart,
        title: 'قوائم تسوق ذكية',
        description: 'إنشاء قوائم تسوق تلقائية من خطط الوجبات',
        isIncluded: isPremium,
      ),
      FeatureItem(
        icon: Icons.fitness_center,
        title: 'تتبع التمارين',
        description: 'ربط النظام الغذائي بالتمارين الرياضية',
        isIncluded: isPremium,
      ),
      FeatureItem(
        icon: Icons.cloud_sync,
        title: 'مزامنة عبر الأجهزة',
        description: 'الوصول لبياناتك من أي جهاز',
        isIncluded: isPremium,
      ),
      FeatureItem(
        icon: Icons.support_agent,
        title: 'دعم فني أولوية',
        description: 'استجابة سريعة للاستفسارات والمشاكل',
        isIncluded: isPremium,
      ),
    ];

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'المزايا المتاحة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...features.map((feature) => _buildFeatureItem(context, feature)),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, FeatureItem feature) {
    return ListTile(
      leading: Icon(
        feature.icon,
        color: feature.isIncluded ? AppColors.primary : AppColors.textSecondary,
      ),
      title: Text(
        feature.title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: feature.isIncluded ? AppColors.textPrimary : AppColors.textSecondary,
        ),
      ),
      subtitle: Text(
        feature.description,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: Icon(
        feature.isIncluded ? Icons.check_circle : Icons.lock,
        color: feature.isIncluded ? AppColors.success : AppColors.textSecondary,
      ),
    );
  }

  Widget _buildPricingSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          Text(
            'خطط الاشتراك',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Monthly Plan
          _buildPricingCard(
            context,
            'شهري',
            '29.99 ريال',
            'شهرياً',
            false,
          ),
          
          const SizedBox(height: 12),
          
          // Yearly Plan (Popular)
          _buildPricingCard(
            context,
            'سنوي',
            '299.99 ريال',
            'سنوياً (وفر 17%)',
            true,
          ),
          
          const SizedBox(height: 20),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _handleSubscription(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'ابدأ الاشتراك المميز',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'يمكنك إلغاء الاشتراك في أي وقت',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingCard(
    BuildContext context,
    String title,
    String price,
    String period,
    bool isPopular,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isPopular ? AppColors.primary.withOpacity(0.1) : AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPopular ? AppColors.primary : AppColors.border,
          width: isPopular ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isPopular) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'الأكثر شعبية',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  period,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            price,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isPopular ? AppColors.primary : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _handleSubscription(BuildContext context) {
    // TODO: Implement subscription logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الاشتراك قيد التطوير'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}

class FeatureItem {
  final IconData icon;
  final String title;
  final String description;
  final bool isIncluded;

  FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.isIncluded,
  });
}
