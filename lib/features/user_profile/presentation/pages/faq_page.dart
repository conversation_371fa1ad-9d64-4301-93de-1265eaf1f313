import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';

class FaqPage extends StatefulWidget {
  const FaqPage({super.key});

  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage> {
  final List<FaqItem> _faqItems = [
    FaqItem(
      question: 'كيف يعمل تطبيق EasyDietAI؟',
      answer: 'يستخدم التطبيق الذكاء الاصطناعي لإنشاء خطط وجبات مخصصة بناءً على أهدافك الصحية ومعلوماتك الشخصية. يمكنك تتبع وجباتك، مراقبة تقدمك، وإدارة قوائم التسوق بسهولة.',
    ),
    FaqItem(
      question: 'كيف يمكنني تغيير خطة الوجبات؟',
      answer: 'يمكنك تغيير خطة الوجبات من خلال الذهاب إلى صفحة الملف الشخصي، ثم "إدارة الخطة"، وتعديل معلوماتك الشخصية أو أهدافك الصحية. سيتم إنشاء خطة جديدة تلقائياً.',
    ),
    FaqItem(
      question: 'هل يمكنني استبدال وجبة معينة؟',
      answer: 'نعم، يمكنك استبدال أي وجبة بالضغط على زر "استبدال الوجبة" في صفحة تفاصيل الوجبة. سيقوم الذكاء الاصطناعي بإنشاء بدائل مناسبة لك.',
    ),
    FaqItem(
      question: 'كيف يتم حساب السعرات الحرارية؟',
      answer: 'يتم حساب السعرات الحرارية بناءً على معلوماتك الشخصية (العمر، الوزن، الطول، مستوى النشاط) وأهدافك الصحية. نستخدم معادلات علمية معتمدة لضمان الدقة.',
    ),
    FaqItem(
      question: 'هل يمكنني تتبع وزني في التطبيق؟',
      answer: 'نعم، يحتوي التطبيق على ميزة تتبع الوزن التي تسمح لك بتسجيل وزنك بانتظام ومراقبة تقدمك نحو هدفك.',
    ),
    FaqItem(
      question: 'كيف أقوم بإنشاء قائمة تسوق؟',
      answer: 'يمكنك إنشاء قائمة تسوق تلقائياً من خطة الوجبات الخاصة بك. اذهب إلى قسم "قوائم التسوق" واختر الفترة الزمنية المطلوبة.',
    ),
    FaqItem(
      question: 'هل يدعم التطبيق الأنظمة الغذائية الخاصة؟',
      answer: 'نعم، يدعم التطبيق عدة أنظمة غذائية مثل الكيتو، النباتي، عالي البروتين، وقليل الكربوهيدرات. يمكنك اختيار النظام المناسب لك أثناء الإعداد.',
    ),
    FaqItem(
      question: 'كيف يمكنني تغيير إعدادات الإشعارات؟',
      answer: 'اذهب إلى الملف الشخصي، ثم "الإشعارات" لتخصيص إعدادات الإشعارات حسب تفضيلاتك.',
    ),
    FaqItem(
      question: 'هل بياناتي آمنة؟',
      answer: 'نعم، نحن نأخذ أمان بياناتك على محمل الجد. جميع البيانات محمية ومشفرة، ولا نشارك معلوماتك الشخصية مع أطراف ثالثة.',
    ),
    FaqItem(
      question: 'كيف يمكنني التواصل مع الدعم الفني؟',
      answer: 'يمكنك التواصل معنا من خلال البريد الإلكتروني أو من خلال قسم "المساعدة والدعم" في التطبيق.',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأسئلة الشائعة'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.primary.withOpacity(0.2)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.help_outline,
                    size: 48,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'الأسئلة الشائعة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'احصل على إجابات للأسئلة الأكثر شيوعاً حول التطبيق',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // FAQ Items
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _faqItems.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                return _buildFaqItem(_faqItems[index]);
              },
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildFaqItem(FaqItem item) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: ExpansionTile(
        title: Text(
          item.question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        iconColor: AppColors.primary,
        collapsedIconColor: AppColors.textSecondary,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              item.answer,
              style: const TextStyle(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class FaqItem {
  final String question;
  final String answer;

  FaqItem({
    required this.question,
    required this.answer,
  });
}
