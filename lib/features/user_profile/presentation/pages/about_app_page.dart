import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_colors.dart';

class AboutAppPage extends StatelessWidget {
  const AboutAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حول التطبيق'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // App Logo and Info
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.restaurant_menu,
                      size: 40,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'EasyDietAI',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'الإصدار 1.0.0',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // App Description
            _buildSectionCard(
              context,
              'عن التطبيق',
              [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'EasyDietAI هو تطبيق ذكي يستخدم الذكاء الاصطناعي لمساعدتك في إنشاء خطط غذائية مخصصة وصحية. '
                    'يهدف التطبيق إلى تبسيط رحلتك نحو نمط حياة صحي من خلال توفير وجبات متوازنة ومتنوعة تناسب أهدافك الشخصية.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      height: 1.6,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Features
            _buildSectionCard(
              context,
              'المزايا الرئيسية',
              [
                _buildFeatureItem(
                  context,
                  'خطط وجبات ذكية',
                  'إنشاء خطط مخصصة بالذكاء الاصطناعي',
                  Icons.smart_toy,
                ),
                _buildFeatureItem(
                  context,
                  'تحليل غذائي',
                  'تتبع السعرات والعناصر الغذائية',
                  Icons.analytics,
                ),
                _buildFeatureItem(
                  context,
                  'قوائم التسوق',
                  'إنشاء قوائم تسوق تلقائية',
                  Icons.shopping_cart,
                ),
                _buildFeatureItem(
                  context,
                  'تتبع الوزن',
                  'مراقبة تقدمك نحو هدفك',
                  Icons.monitor_weight,
                ),
                _buildFeatureItem(
                  context,
                  'واجهة عربية',
                  'تصميم مناسب للمستخدم العربي',
                  Icons.language,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Team & Credits
            _buildSectionCard(
              context,
              'الفريق والشكر',
              [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تم تطوير هذا التطبيق بواسطة فريق متخصص في التغذية والتكنولوجيا، '
                        'بهدف توفير حلول غذائية ذكية ومبتكرة.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.6,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'شكر خاص لجميع المستخدمين الذين ساهموا في تحسين التطبيق من خلال ملاحظاتهم واقتراحاتهم.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.6,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Legal Links
            _buildSectionCard(
              context,
              'الشروط والأحكام',
              [
                _buildLegalItem(
                  context,
                  'سياسة الخصوصية',
                  Icons.privacy_tip,
                  () => _launchPrivacyPolicy(),
                ),
                _buildLegalItem(
                  context,
                  'شروط الاستخدام',
                  Icons.description,
                  () => _launchTermsOfService(),
                ),
                _buildLegalItem(
                  context,
                  'تراخيص المكتبات',
                  Icons.code,
                  () => _showLicenses(context),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Copyright
            Text(
              '© 2024 EasyDietAI. جميع الحقوق محفوظة.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(BuildContext context, String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    String title,
    String description,
    IconData icon,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        description,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildLegalItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.info,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
      trailing: const Icon(
        Icons.open_in_new,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Future<void> _launchPrivacyPolicy() async {
    final Uri uri = Uri.parse('https://easydietai.com/privacy-policy');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _launchTermsOfService() async {
    final Uri uri = Uri.parse('https://easydietai.com/terms-of-service');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  void _showLicenses(BuildContext context) {
    showLicensePage(
      context: context,
      applicationName: 'EasyDietAI',
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 EasyDietAI. جميع الحقوق محفوظة.',
    );
  }
}
