import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../onboarding/data/models/plan_preferences.dart';

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  PlanPreferences? _localPlanPreferences;

  @override
  void initState() {
    super.initState();
    _loadLocalPlanPreferences();
  }

  /// Load plan preferences directly from local storage as fallback
  Future<void> _loadLocalPlanPreferences() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final data = localStorageService.loadPlanPreferences();
      print('ProfilePage: Direct local storage load: ${data != null ? 'Found' : 'NULL'}');
      if (data != null) {
        print('ProfilePage: Raw data keys: ${data.keys.toList()}');
        final preferences = PlanPreferences.fromJson(data);
        setState(() {
          _localPlanPreferences = preferences;
        });
        print('ProfilePage: Successfully loaded local plan preferences');
      }
    } catch (e) {
      print('ProfilePage: Error loading local plan preferences: $e');
    }
  }

  /// Calculate BMI from height and weight
  double? _calculateBMI(double? height, double? weight) {
    if (height == null || weight == null || height <= 0) return null;
    // BMI = weight (kg) / (height (m))^2
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  /// Format weight display
  String _formatWeight(double? weight, String unitSystem) {
    if (weight == null) return 'غير محدد';
    if (unitSystem == 'imperial') {
      final pounds = weight * 2.20462;
      return '${pounds.toStringAsFixed(1)} رطل';
    }
    return '${weight.toStringAsFixed(1)} كيلو';
  }

  /// Format height display
  String _formatHeight(double? height, String unitSystem) {
    if (height == null) return 'غير محدد';
    if (unitSystem == 'imperial') {
      final totalInches = height / 2.54;
      final feet = totalInches ~/ 12;
      final inches = totalInches % 12;
      return '${feet}\'${inches.toStringAsFixed(0)}"';
    }
    return '${height.toStringAsFixed(0)} سم';
  }

  /// Format BMI display
  String _formatBMI(double? bmi) {
    if (bmi == null) return 'غير محدد';
    return bmi.toStringAsFixed(1);
  }

  /// Format gender display
  String _formatGender(String? gender) {
    if (gender == null) return 'غير محدد';
    switch (gender.toLowerCase()) {
      case 'male':
        return 'ذكر';
      case 'female':
        return 'أنثى';
      default:
        return 'غير محدد';
    }
  }

  /// Format health goal display
  String _formatHealthGoal(String? healthGoal) {
    if (healthGoal == null) return 'غير محدد';
    switch (healthGoal.toLowerCase()) {
      case 'lose_weight':
        return 'فقدان الوزن';
      case 'maintain':
        return 'الحفاظ على الوزن';
      case 'gain_weight':
        return 'زيادة الوزن';
      case 'build_muscle':
        return 'بناء العضلات';
      case 'improve_health':
        return 'تحسين الصحة';
      default:
        return 'غير محدد';
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    final currentUser = ref.watch(currentUserProvider);
    final userProfile = ref.watch(currentUserProfileProvider);
    final planPreferences = ref.watch(currentPlanPreferencesProvider);

    // Use local preferences as fallback if provider returns null
    final effectivePlanPreferences = planPreferences ?? _localPlanPreferences;

    // Debug: Check what data we have
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('ProfilePage Debug:');
      print('  isAuthenticated: $isAuthenticated');
      print('  currentUser: ${currentUser?.uid}');
      print('  userProfile: ${userProfile?.displayName}');
      print('  planPreferences (provider): ${planPreferences != null ? 'Found' : 'NULL'}');
      print('  _localPlanPreferences: ${_localPlanPreferences != null ? 'Found' : 'NULL'}');
      print('  effectivePlanPreferences: ${effectivePlanPreferences != null ? 'Found' : 'NULL'}');
      if (effectivePlanPreferences != null) {
        print('    weight: ${effectivePlanPreferences.weight}');
        print('    height: ${effectivePlanPreferences.height}');
        print('    gender: ${effectivePlanPreferences.gender}');
        print('    healthGoal: ${effectivePlanPreferences.healthGoal}');
      }
    });

    // If user is not authenticated, show login widget
    if (!isAuthenticated) {
      // redirect to /auth
      // return
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.profile),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    backgroundImage: currentUser?.photoURL != null
                        ? NetworkImage(currentUser!.photoURL!)
                        : null,
                    child: currentUser?.photoURL == null
                        ? const Icon(
                            Icons.person,
                            size: 50,
                            color: AppColors.primary,
                          )
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    currentUser?.displayName ?? userProfile?.displayName ?? 'مستخدم',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    currentUser?.email ?? userProfile?.email ?? 'لا يوجد إيميل',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primaryWithOpacity,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'عضو مميز',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Stats Cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'الوزن الحالي',
                    _formatWeight(effectivePlanPreferences?.weight, userProfile?.unitSystem ?? 'metric'),
                    Icons.monitor_weight,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'الهدف',
                    _formatHealthGoal(effectivePlanPreferences?.healthGoal.name),
                    Icons.flag,
                    AppColors.secondary,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'الطول',
                    _formatHeight(effectivePlanPreferences?.height, userProfile?.unitSystem ?? 'metric'),
                    Icons.height,
                    AppColors.info,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'مؤشر كتلة الجسم',
                    _formatBMI(_calculateBMI(effectivePlanPreferences?.height, effectivePlanPreferences?.weight)),
                    Icons.analytics,
                    AppColors.success,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'الجنس',
                    _formatGender(effectivePlanPreferences?.gender.name),
                    Icons.person,
                    Colors.purple,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'الوزن المستهدف',
                    _formatWeight(effectivePlanPreferences?.targetWeight, userProfile?.unitSystem ?? 'metric'),
                    Icons.track_changes,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Menu Items
            _buildMenuItem(
              context,
              'إدارة الخطة',
              Icons.edit_note,
              () {
                // Navigate to plan management settings
                context.push('/profile/settings');
              },
            ),
            _buildMenuItem(
              context,
              'الإشعارات',
              Icons.notifications,
              () {
                // Navigate to notifications settings
                context.push('/profile/notifications');
              },
            ),
            _buildMenuItem(
              context,
              'إعدادات التطبيق',
              Icons.settings,
              () {
                context.push('/profile/app-settings');
              },
            ),
            _buildMenuItem(
              context,
              'الأسئلة الشائعة',
              Icons.help_outline,
              () {
                context.push('/profile/faq');
              },
            ),
            _buildMenuItem(
              context,
              'الاشتراك المميز',
              Icons.star,
              () {
                context.push('/profile/subscription');
              },
            ),
            _buildMenuItem(
              context,
              'المساعدة والدعم',
              Icons.support_agent,
              () {
                context.push('/profile/help-support');
              },
            ),
            _buildMenuItem(
              context,
              'حول التطبيق',
              Icons.info,
              () {
                context.push('/profile/about');
              },
            ),

            const SizedBox(height: 24),

            // Logout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  _showLogoutDialog(context, ref);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: Text(l10n.logout),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: AppColors.primary,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: AppColors.textSecondary,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: AppColors.surface,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _handleLogout(context, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleLogout(BuildContext context, WidgetRef ref) async {
    try {
      AppLogger.info('ProfilePage: Starting logout process');

      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري تسجيل الخروج...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Call the auth provider's signOut method
      await ref.read(appStateNotifierProvider.notifier).signOut();

      AppLogger.info('ProfilePage: Logout successful');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج بنجاح'),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      AppLogger.warning('ProfilePage: Logout failed: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تسجيل الخروج: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
