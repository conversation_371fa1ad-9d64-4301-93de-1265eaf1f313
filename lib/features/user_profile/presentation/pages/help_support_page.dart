import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_colors.dart';

class HelpSupportPage extends StatelessWidget {
  const HelpSupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المساعدة والدعم'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.info.withOpacity(0.2)),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.support_agent,
                    size: 48,
                    color: AppColors.info,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'نحن هنا لمساعدتك',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.info,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'تواصل معنا في أي وقت للحصول على المساعدة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Contact Methods
            _buildSectionCard(
              context,
              'طرق التواصل',
              [
                _buildContactItem(
                  context,
                  'البريد الإلكتروني',
                  '<EMAIL>',
                  Icons.email,
                  () => _launchEmail('<EMAIL>'),
                ),
                _buildContactItem(
                  context,
                  'واتساب',
                  '+966 50 123 4567',
                  Icons.chat,
                  () => _launchWhatsApp('+966501234567'),
                ),
                _buildContactItem(
                  context,
                  'تويتر',
                  '@EasyDietAI',
                  Icons.alternate_email,
                  () => _launchTwitter('EasyDietAI'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Quick Help
            _buildSectionCard(
              context,
              'مساعدة سريعة',
              [
                _buildHelpItem(
                  context,
                  'دليل الاستخدام',
                  'تعلم كيفية استخدام جميع ميزات التطبيق',
                  Icons.menu_book,
                  () => _showUserGuide(context),
                ),
                _buildHelpItem(
                  context,
                  'الإبلاغ عن مشكلة',
                  'أخبرنا عن أي مشكلة تواجهها',
                  Icons.bug_report,
                  () => _showReportIssue(context),
                ),
                _buildHelpItem(
                  context,
                  'اقتراح ميزة',
                  'شاركنا أفكارك لتحسين التطبيق',
                  Icons.lightbulb,
                  () => _showFeatureSuggestion(context),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // App Info
            _buildSectionCard(
              context,
              'معلومات التطبيق',
              [
                _buildInfoItem(
                  context,
                  'إصدار التطبيق',
                  '1.0.0',
                  Icons.info,
                ),
                _buildInfoItem(
                  context,
                  'آخر تحديث',
                  '2024-01-15',
                  Icons.update,
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(BuildContext context, String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: const Icon(
        Icons.open_in_new,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildHelpItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.secondary,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.info,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
      trailing: Text(
        value,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=مساعدة EasyDietAI',
    );
    
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  Future<void> _launchWhatsApp(String phoneNumber) async {
    final Uri whatsappUri = Uri.parse('https://wa.me/$phoneNumber');
    
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  Future<void> _launchTwitter(String username) async {
    final Uri twitterUri = Uri.parse('https://twitter.com/$username');
    
    if (await canLaunchUrl(twitterUri)) {
      await launchUrl(twitterUri, mode: LaunchMode.externalApplication);
    }
  }

  void _showUserGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دليل الاستخدام'),
        content: const Text(
          'سيتم إضافة دليل الاستخدام التفاعلي قريباً. '
          'في الوقت الحالي، يمكنك استكشاف التطبيق أو التواصل معنا للمساعدة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showReportIssue(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن مشكلة'),
        content: const Text(
          'لإبلاغنا عن مشكلة، يرجى التواصل معنا عبر البريد الإلكتروني أو واتساب '
          'مع وصف مفصل للمشكلة وخطوات إعادة إنتاجها.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _launchEmail('<EMAIL>');
            },
            child: const Text('إرسال بريد إلكتروني'),
          ),
        ],
      ),
    );
  }

  void _showFeatureSuggestion(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اقتراح ميزة'),
        content: const Text(
          'نحن نقدر اقتراحاتك لتحسين التطبيق! '
          'يرجى مراسلتنا بأفكارك وسنأخذها في الاعتبار للتحديثات القادمة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _launchEmail('<EMAIL>');
            },
            child: const Text('إرسال اقتراح'),
          ),
        ],
      ),
    );
  }
}

class FeatureItem {
  final IconData icon;
  final String title;
  final String description;
  final bool isIncluded;

  FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.isIncluded,
  });
}
