import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../onboarding/providers/onboarding_provider.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../onboarding/data/models/diet_type.dart';
import '../../data/models/user_profile.dart';

part 'plan_settings_page.g.dart';

// Provider for loading settings data from local storage
@riverpod
Future<OnboardingData?> planSettingsData(PlanSettingsDataRef ref) async {
  try {
    final localStorageService = await ref.read(localStorageServiceProvider.future);
    final data = localStorageService.loadOnboardingData();
    if (data != null) {
      return OnboardingData.fromJson(data);
    }
    return null;
  } catch (e) {
    return null;
  }
}

class PlanSettingsPage extends ConsumerWidget {
  const PlanSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context);
    final settingsDataAsync = ref.watch(planSettingsDataProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إعدادات الخطة',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: settingsDataAsync.when(
          data: (settingsData) => _buildContent(context, ref, settingsData),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, WidgetRef ref, OnboardingData? settingsData) {
    if (settingsData == null) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(planSettingsDataProvider);
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Page description
            Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Text(
                'يمكنك مراجعة وتعديل إعدادات خطتك الشخصية من هنا',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),

            // Personal Information Section
            _buildPersonalInfoSection(context, ref, settingsData),
            const SizedBox(height: 16),

            // Physical Measurements Section
            _buildPhysicalMeasurementsSection(context, ref, settingsData),
            const SizedBox(height: 16),

            // Diet Preferences Section
            _buildDietPreferencesSection(context, ref, settingsData),
            const SizedBox(height: 16),

            // Food Preferences Section
            _buildFoodPreferencesSection(context, ref, settingsData),
            const SizedBox(height: 16),

            // Health Information Section
            _buildHealthInfoSection(context, ref, settingsData),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.settings_outlined,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد إعدادات محفوظة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى إكمال عملية الإعداد الأولي أولاً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل الإعدادات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection(BuildContext context, WidgetRef ref, OnboardingData data) {
    return _buildSettingsCard(
      context,
      title: 'المعلومات الشخصية',
      icon: Icons.person,
      onEdit: () => context.push('/profile/settings/personal?step=${OnboardingStep.personalInfo.name}&edit=true'),
      children: [
        if (data.dateOfBirth != null)
          _buildDataRow('تاريخ الميلاد', DateFormat('dd/MM/yyyy').format(data.dateOfBirth!)),
        _buildDataRow('الجنس', _getGenderText(data.gender)),
      ],
    );
  }

  Widget _buildPhysicalMeasurementsSection(BuildContext context, WidgetRef ref, OnboardingData data) {
    return _buildSettingsCard(
      context,
      title: 'القياسات الجسدية',
      icon: Icons.fitness_center,
      onEdit: () => context.push('/profile/settings/personal?step=${OnboardingStep.physicalInfo.name}&edit=true'),
      children: [
        if (data.height != null)
          _buildDataRow('الطول', '${data.height!.toStringAsFixed(0)} سم'),
        if (data.weight != null)
          _buildDataRow('الوزن', '${data.weight!.toStringAsFixed(1)} كجم'),
        _buildDataRow('مستوى النشاط', _getActivityLevelText(data.activityLevel)),
        if (data.useManualCalories && data.dailyBurnedCalories != null)
          _buildDataRow('السعرات المحروقة يومياً', '${data.dailyBurnedCalories} سعرة'),
        _buildDataRow('نظام القياس', data.unitSystem == 'metric' ? 'متري' : 'إمبراطوري'),
      ],
    );
  }

  Widget _buildDietPreferencesSection(BuildContext context, WidgetRef ref, OnboardingData data) {
    return _buildSettingsCard(
      context,
      title: 'التفضيلات الغذائية',
      icon: Icons.restaurant_menu,
      onEdit: () => context.push('/profile/settings/personal?step=${OnboardingStep.goals.name}&edit=true'),
      children: [
        if (data.selectedDietType != null)
          _buildDataRow('نوع النظام الغذائي', _getDietTypeText(data.selectedDietType!)),
        if (data.dailyCalorieGoal != null)
          _buildDataRow('الهدف اليومي للسعرات', '${data.dailyCalorieGoal} سعرة'),
        if (data.targetWeight != null)
          _buildDataRow('الوزن المستهدف', '${data.targetWeight!.toStringAsFixed(1)} كجم'),
        if (data.customMacroDistribution != null) ...[
          _buildDataRow('البروتين', '${data.customMacroDistribution!.proteinPercentage.toStringAsFixed(0)}%'),
          _buildDataRow('الكربوهيدرات', '${data.customMacroDistribution!.carbsPercentage.toStringAsFixed(0)}%'),
          _buildDataRow('الدهون', '${data.customMacroDistribution!.fatPercentage.toStringAsFixed(0)}%'),
        ],
        if (data.selectedDietType == null && data.customMacroDistribution == null)
          _buildDataRow('الحالة', 'لم يتم تحديد نوع النظام الغذائي'),
      ],
    );
  }

  Widget _buildFoodPreferencesSection(BuildContext context, WidgetRef ref, OnboardingData data) {
    return _buildSettingsCard(
      context,
      title: 'تفضيلات الطعام',
      icon: Icons.favorite,
      onEdit: () => context.push('/profile/settings/personal?step=${OnboardingStep.preferences.name}&edit=true'),
      children: [
        _buildDataRow('عدد الوجبات يومياً', '${data.mealsPerDay}'),
        _buildDataRow('عدد الوجبات الخفيفة', '${data.snacksPerDay}'),
        if (data.favoriteIngredients.isNotEmpty)
          _buildDataRow('المكونات المفضلة', data.favoriteIngredients.take(3).join('، ') +
            (data.favoriteIngredients.length > 3 ? '...' : '')),
        if (data.dislikedIngredients.isNotEmpty)
          _buildDataRow('المكونات المكروهة', data.dislikedIngredients.take(3).join('، ') +
            (data.dislikedIngredients.length > 3 ? '...' : '')),
        if (data.favoriteCuisines.isNotEmpty)
          _buildDataRow('المأكولات المفضلة', data.favoriteCuisines.take(3).join('، ') +
            (data.favoriteCuisines.length > 3 ? '...' : '')),
      ],
    );
  }

  Widget _buildHealthInfoSection(BuildContext context, WidgetRef ref, OnboardingData data) {
    return _buildSettingsCard(
      context,
      title: 'المعلومات الصحية',
      icon: Icons.health_and_safety,
      onEdit: () => context.push('/profile/settings/personal?step=${OnboardingStep.healthInfo.name}&edit=true'),
      children: [
        if (data.allergies.isNotEmpty)
          _buildDataRow('الحساسية', data.allergies.join('، ')),
        if (data.dietaryRestrictions.isNotEmpty)
          _buildDataRow('القيود الغذائية', data.dietaryRestrictions.join('، ')),
        if (data.healthConditions.isNotEmpty)
          _buildDataRow('الحالات الصحية', data.healthConditions.join('، ')),
        if (data.medications.isNotEmpty)
          _buildDataRow('الأدوية', data.medications.join('، ')),
        if (data.allergies.isEmpty && data.dietaryRestrictions.isEmpty &&
            data.healthConditions.isEmpty && data.medications.isEmpty)
          _buildDataRow('الحالة', 'لا توجد قيود صحية'),
      ],
    );
  }
  Widget _buildSettingsCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onEdit,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primaryWithOpacity,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ),
              IconButton(
                onPressed: onEdit,
                icon: Icon(
                  Icons.edit,
                  size: 20,
                  color: AppColors.primary,
                ),
                tooltip: 'تعديل',
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Section content
          ...children,
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  String _getGenderText(Gender gender) {
    switch (gender) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
      case Gender.notSpecified:
        return 'غير محدد';
    }
  }

  String _getActivityLevelText(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'قليل الحركة';
      case ActivityLevel.light:
        return 'نشاط خفيف';
      case ActivityLevel.moderate:
        return 'نشاط متوسط';
      case ActivityLevel.active:
        return 'نشط';
      case ActivityLevel.veryActive:
        return 'نشط جداً';
    }
  }

  String _getDietTypeText(DietType dietType) {
    switch (dietType) {
      case DietType.balanced:
        return 'متوازن';
      case DietType.highProtein:
        return 'عالي البروتين';
      case DietType.ketogenic:
        return 'كيتو';
      case DietType.lowCarb:
        return 'قليل الكربوهيدرات';
      case DietType.mediterranean:
        return 'متوسطي';
      case DietType.plantBased:
        return 'نباتي';
      case DietType.custom:
        return 'مخصص';
    }
  }
}
