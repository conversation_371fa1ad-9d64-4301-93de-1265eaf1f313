import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/notification_settings_models.dart';

class WorkoutReminderSettingsWidget extends ConsumerWidget {
  final WorkoutReminderSettings settings;
  final ValueChanged<WorkoutReminderSettings> onSettingsChanged;

  const WorkoutReminderSettingsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  static const Map<String, String> _dayNames = {
    'monday': 'الاثنين',
    'tuesday': 'الثلاثاء',
    'wednesday': 'الأربعاء',
    'thursday': 'الخميس',
    'friday': 'الجمعة',
    'saturday': 'السبت',
    'sunday': 'الأحد',
  };

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Preferred time
        _buildTimeSetting(
          context: context,
          title: 'الوقت المفضل للتمرين',
          time: settings.preferredTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(preferredTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // Reminder days
        _buildDaysSelection(context),
        
        const SizedBox(height: 16),
        
        // Rest day reminders toggle
        _buildToggleSetting(
          context: context,
          title: 'تذكير أيام الراحة',
          subtitle: 'احصل على تذكير بأهمية الراحة في أيام عدم التمرين',
          value: settings.restDayReminders,
          onChanged: (value) => onSettingsChanged(settings.copyWith(restDayReminders: value)),
        ),
        
        const SizedBox(height: 16),
        
        // Motivational quotes toggle
        _buildToggleSetting(
          context: context,
          title: 'اقتباسات تحفيزية',
          subtitle: 'اعرض اقتباسات تحفيزية مع تذكيرات التمرين',
          value: settings.motivationalQuotes,
          onChanged: (value) => onSettingsChanged(settings.copyWith(motivationalQuotes: value)),
        ),
      ],
    );
  }

  Widget _buildTimeSetting({
    required BuildContext context,
    required String title,
    required String time,
    required ValueChanged<String> onTimeChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showTimePicker(context, time, onTimeChanged),
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  Widget _buildDaysSelection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أيام التذكير',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _dayNames.entries.map((entry) {
              final isSelected = settings.reminderDays.contains(entry.key);
              return FilterChip(
                label: Text(entry.value),
                selected: isSelected,
                onSelected: (selected) => _toggleDay(entry.key, selected),
                selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                checkmarkColor: Theme.of(context).colorScheme.primary,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  void _toggleDay(String day, bool selected) {
    final updatedDays = List<String>.from(settings.reminderDays);
    if (selected) {
      if (!updatedDays.contains(day)) {
        updatedDays.add(day);
      }
    } else {
      updatedDays.remove(day);
    }
    onSettingsChanged(settings.copyWith(reminderDays: updatedDays));
  }

  Future<void> _showTimePicker(BuildContext context, String currentTime, ValueChanged<String> onTimeChanged) async {
    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final formattedTime = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
      onTimeChanged(formattedTime);
    }
  }
}
