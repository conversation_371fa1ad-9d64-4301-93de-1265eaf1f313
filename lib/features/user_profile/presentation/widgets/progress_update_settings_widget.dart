import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/notification_settings_models.dart';

class ProgressUpdateSettingsWidget extends ConsumerWidget {
  final ProgressUpdateSettings settings;
  final ValueChanged<ProgressUpdateSettings> onSettingsChanged;

  const ProgressUpdateSettingsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  static const Map<String, String> _unitOptions = {
    'kg': 'كيلوجرام',
    'lb': 'باوند',
  };

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Preferred unit
        _buildDropdownSetting(
          context: context,
          title: 'وحدة الوزن المفضلة',
          value: settings.preferredUnit,
          options: _unitOptions,
          onChanged: (value) => onSettingsChanged(settings.copyWith(preferredUnit: value)),
        ),
        
        const SizedBox(height: 16),
        
        // Reminder interval
        _buildSliderSetting(
          context: context,
          title: 'فترة التذكير',
          subtitle: 'كل ${settings.reminderIntervalDays} أيام',
          value: settings.reminderIntervalDays.toDouble(),
          min: 1,
          max: 30,
          divisions: 29,
          onChanged: (value) => onSettingsChanged(settings.copyWith(reminderIntervalDays: value.round())),
        ),
        
        const SizedBox(height: 16),
        
        // Show progress chart toggle
        _buildToggleSetting(
          context: context,
          title: 'عرض مخطط التقدم',
          subtitle: 'اعرض مخططاً بصرياً لتقدمك في الوزن',
          value: settings.showProgressChart,
          onChanged: (value) => onSettingsChanged(settings.copyWith(showProgressChart: value)),
        ),
        
        const SizedBox(height: 16),
        
        // Show weight change indicators toggle
        _buildToggleSetting(
          context: context,
          title: 'مؤشرات تغيير الوزن',
          subtitle: 'اعرض مؤشرات لزيادة أو نقصان الوزن',
          value: settings.showWeightChangeIndicators,
          onChanged: (value) => onSettingsChanged(settings.copyWith(showWeightChangeIndicators: value)),
        ),
        
        const SizedBox(height: 16),
        
        // Weekly summary toggle
        _buildToggleSetting(
          context: context,
          title: 'ملخص أسبوعي',
          subtitle: 'احصل على ملخص أسبوعي لتقدمك وإنجازاتك',
          value: settings.weeklySummary,
          onChanged: (value) => onSettingsChanged(settings.copyWith(weeklySummary: value)),
        ),
      ],
    );
  }

  Widget _buildDropdownSetting({
    required BuildContext context,
    required String title,
    required String value,
    required Map<String, String> options,
    required ValueChanged<String> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value: value,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: options.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(entry.value),
              );
            }).toList(),
            onChanged: (newValue) {
              if (newValue != null) {
                onChanged(newValue);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSliderSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}
