import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/notification_settings_models.dart';

class MealReminderSettingsWidget extends ConsumerWidget {
  final MealReminderSettings settings;
  final ValueChanged<MealReminderSettings> onSettingsChanged;

  const MealReminderSettingsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Breakfast time
        _buildTimeSetting(
          context: context,
          title: 'وقت الإفطار',
          time: settings.breakfastTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(breakfastTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // Lunch time
        _buildTimeSetting(
          context: context,
          title: 'وقت الغداء',
          time: settings.lunchTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(lunchTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // Dinner time
        _buildTimeSetting(
          context: context,
          title: 'وقت العشاء',
          time: settings.dinnerTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(dinnerTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // Snack reminders toggle
        _buildToggleSetting(
          context: context,
          title: 'تذكير الوجبات الخفيفة',
          subtitle: 'احصل على تذكير للوجبات الخفيفة بين الوجبات الرئيسية',
          value: settings.snackReminders,
          onChanged: (value) => onSettingsChanged(settings.copyWith(snackReminders: value)),
        ),
        
        const SizedBox(height: 16),
        
        // Reminder minutes before
        _buildSliderSetting(
          context: context,
          title: 'التذكير قبل الوجبة',
          subtitle: '${settings.reminderMinutesBefore} دقيقة',
          value: settings.reminderMinutesBefore.toDouble(),
          min: 5,
          max: 60,
          divisions: 11,
          onChanged: (value) => onSettingsChanged(settings.copyWith(reminderMinutesBefore: value.round())),
        ),
      ],
    );
  }

  Widget _buildTimeSetting({
    required BuildContext context,
    required String title,
    required String time,
    required ValueChanged<String> onTimeChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showTimePicker(context, time, onTimeChanged),
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSliderSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Future<void> _showTimePicker(BuildContext context, String currentTime, ValueChanged<String> onTimeChanged) async {
    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final formattedTime = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
      onTimeChanged(formattedTime);
    }
  }
}
