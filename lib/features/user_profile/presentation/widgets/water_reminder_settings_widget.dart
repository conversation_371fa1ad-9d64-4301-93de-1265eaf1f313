import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/notification_settings_models.dart';

class WaterReminderSettingsWidget extends ConsumerWidget {
  final WaterReminderSettings settings;
  final ValueChanged<WaterReminderSettings> onSettingsChanged;

  const WaterReminderSettingsWidget({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Interval hours
        _buildSliderSetting(
          context: context,
          title: 'فترة التذكير',
          subtitle: 'كل ${settings.intervalHours} ساعة',
          value: settings.intervalHours.toDouble(),
          min: 1,
          max: 6,
          divisions: 5,
          onChanged: (value) => onSettingsChanged(settings.copyWith(intervalHours: value.round())),
        ),
        
        const SizedBox(height: 16),
        
        // Start time
        _buildTimeSetting(
          context: context,
          title: 'وقت البداية',
          time: settings.startTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(startTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // End time
        _buildTimeSetting(
          context: context,
          title: 'وقت النهاية',
          time: settings.endTime,
          onTimeChanged: (time) => onSettingsChanged(settings.copyWith(endTime: time)),
        ),
        
        const SizedBox(height: 16),
        
        // Daily goal
        _buildSliderSetting(
          context: context,
          title: 'الهدف اليومي',
          subtitle: '${settings.dailyGoalMl} مل',
          value: settings.dailyGoalMl.toDouble(),
          min: 1000,
          max: 4000,
          divisions: 30,
          onChanged: (value) => onSettingsChanged(settings.copyWith(dailyGoalMl: value.round())),
        ),
        
        const SizedBox(height: 16),
        
        // Smart reminders toggle
        _buildToggleSetting(
          context: context,
          title: 'التذكير الذكي',
          subtitle: 'تقليل التذكيرات عند شرب الماء مؤخراً',
          value: settings.smartReminders,
          onChanged: (value) => onSettingsChanged(settings.copyWith(smartReminders: value)),
        ),
      ],
    );
  }

  Widget _buildTimeSetting({
    required BuildContext context,
    required String title,
    required String time,
    required ValueChanged<String> onTimeChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showTimePicker(context, time, onTimeChanged),
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSliderSetting({
    required BuildContext context,
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Future<void> _showTimePicker(BuildContext context, String currentTime, ValueChanged<String> onTimeChanged) async {
    final timeParts = currentTime.split(':');
    final initialTime = TimeOfDay(
      hour: int.parse(timeParts[0]),
      minute: int.parse(timeParts[1]),
    );

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      final formattedTime = '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}';
      onTimeChanged(formattedTime);
    }
  }
}
