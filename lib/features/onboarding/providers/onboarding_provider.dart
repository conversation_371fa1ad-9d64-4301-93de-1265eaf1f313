import 'dart:math';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/features/onboarding/data/models/diet_type.dart';
import 'package:easydietai/features/user_profile/data/models/user_profile.dart';
import 'package:easydietai/features/onboarding/data/models/plan_preferences.dart';

import 'package:easydietai/shared/providers/app_state_provider.dart';

part 'onboarding_provider.freezed.dart';
part 'onboarding_provider.g.dart';

/// Onboarding step enum
enum OnboardingStep {
  personalInfo,
  physicalInfo,
  healthInfo,
  goals,
  preferences,
  complete,
}

/// Onboarding form data
@freezed
class OnboardingData with _$OnboardingData {
  const factory OnboardingData({
    // Personal Information
    @JsonKey(name: 'date_of_birth') DateTime? dateOfBirth,
    @Default(Gender.notSpecified) Gender gender,

    // Physical Information
    double? height,
    double? weight,
    @JsonKey(name: 'activity_level') @Default(ActivityLevel.moderate) ActivityLevel activityLevel,
    @JsonKey(name: 'daily_burned_calories') int? dailyBurnedCalories, // manually entered daily burned calories
    @JsonKey(name: 'use_manual_calories') @Default(false) bool useManualCalories, // whether to use manual calories instead of activity level
    @JsonKey(name: 'unit_system') @Default('metric') String unitSystem,

    // Health Information
    @Default([]) List<String> allergies,
    @JsonKey(name: 'dietary_restrictions') @Default([]) List<String> dietaryRestrictions,
    @JsonKey(name: 'health_conditions') @Default([]) List<String> healthConditions,
    @Default([]) List<String> medications,

    // Goals
    @JsonKey(name: 'health_goal') @Default(HealthGoal.maintain) HealthGoal healthGoal,
    @JsonKey(name: 'target_weight') double? targetWeight,
    @JsonKey(name: 'daily_calorie_goal') int? dailyCalorieGoal,
    @JsonKey(name: 'selected_calorie_recommendation') String? selectedCalorieRecommendation, // Track which recommendation option is selected
    @JsonKey(name: 'protein_goal') double? proteinGoal,
    @JsonKey(name: 'carbs_goal') double? carbsGoal,
    @JsonKey(name: 'fat_goal') double? fatGoal,
    @JsonKey(name: 'fiber_goal') double? fiberGoal,

    // Diet Type & Macros
    @JsonKey(name: 'selected_diet_type') DietType? selectedDietType,
    @JsonKey(
      name: 'custom_macro_distribution',
      includeIfNull: false,
      fromJson: _macroDistributionFromJson,
      toJson: _macroDistributionToJson,
    ) MacroDistribution? customMacroDistribution, // Only used when selectedDietType is custom
    @JsonKey(name: 'use_custom_macros') @Default(false) bool useCustomMacros, // Whether user chose custom macros over predefined diet

    // Preferences
    @JsonKey(name: 'favorite_ingredients') @Default([]) List<String> favoriteIngredients,
    @JsonKey(name: 'disliked_ingredients') @Default([]) List<String> dislikedIngredients,
    @JsonKey(name: 'favorite_cuisines') @Default([]) List<String> favoriteCuisines,
    @JsonKey(name: 'meals_per_day') @Default(3) int mealsPerDay,
    @JsonKey(name: 'snacks_per_day') @Default(2) int snacksPerDay,
  }) = _OnboardingData;

  factory OnboardingData.fromJson(Map<String, dynamic> json) =>
      _$OnboardingDataFromJson(json);
}

/// Helper functions for MacroDistribution JSON conversion
MacroDistribution? _macroDistributionFromJson(Map<String, dynamic>? json) {
  if (json == null) return null;
  return MacroDistribution.fromJson(json);
}

Map<String, dynamic>? _macroDistributionToJson(MacroDistribution? macroDistribution) {
  return macroDistribution?.toJson();
}

/// Onboarding state
@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(OnboardingStep.personalInfo) OnboardingStep currentStep,
    @Default(OnboardingData()) OnboardingData data,
    @Default({}) Map<OnboardingStep, bool> stepValidation,
    @Default({}) Map<String, String> errors,
    @Default(false) bool isLoading,
  }) = _OnboardingState;
}

/// Onboarding notifier
@riverpod
class OnboardingNotifier extends _$OnboardingNotifier {
  @override
  OnboardingState build() {
    // Initialize with personalInfo as the first step
    final initialState = const OnboardingState(
      currentStep: OnboardingStep.personalInfo,
    );

    // Load saved data and validate steps asynchronously
    Future.microtask(() {
      _loadSavedData();
      _validateAllSteps();
    });

    return initialState;
  }

  /// Load saved plan preferences from local storage
  Future<void> _loadSavedData() async {
    try {
      print('_loadSavedData: Starting to load saved data...'); // Debug log
      final localStorageService = await ref.read(localStorageServiceProvider.future);

      // Load plan preferences (main onboarding data)
      final savedPlanData = localStorageService.loadPlanPreferences();
      print('_loadSavedData: Plan data loaded: ${savedPlanData != null}'); // Debug log

      if (savedPlanData != null) {
        print('_loadSavedData: Plan data keys: ${savedPlanData.keys.toList()}'); // Debug log
        final onboardingData = OnboardingData.fromJson(savedPlanData);
        state = state.copyWith(data: onboardingData);
        print('_loadSavedData: State updated with loaded data'); // Debug log

        // Validate all steps based on loaded data
        _validateAllSteps();
      } else {
        print('_loadSavedData: No saved plan data found'); // Debug log
      }
    } catch (e) {
      print('_loadSavedData: Error loading saved data: $e'); // Debug log
      // If loading fails, continue with default state
    }
  }

  /// Save current onboarding data with conditional persistence logic
  /// - [isCompleteSave]: true for complete form submission (wrapper page), false for individual step changes
  /// - Complete saves: Use full conditional persistence (Firestore + local storage for authenticated users)
  /// - Incremental saves: Only save to local storage, skip Firestore operations
  Future<void> _saveData({bool isCompleteSave = false}) async {
    print('OnboardingProvider: Starting _saveData operation - isCompleteSave: $isCompleteSave'); // Debug log

    try {
      // Prepare data for saving (only plan preferences now)
      final planPreferences = state.data.toJson();

      print('OnboardingProvider: Data prepared - planPreferences keys: ${planPreferences.keys.toList()}'); // Debug log

      if (isCompleteSave) {
        // Complete Save: Use full conditional persistence logic
        final isAuthenticated = ref.read(isAuthenticatedProvider);
        final currentUser = ref.read(currentUserProvider);

        print('OnboardingProvider: Complete save - User authentication status - isAuthenticated: $isAuthenticated, userId: ${currentUser?.uid}'); // Debug log

        if (isAuthenticated && currentUser != null) {
          // Authenticated User Flow: Save to Firestore first, then local storage
          await _saveDataForAuthenticatedUser(planPreferences);
        } else {
          // Guest User Flow: Save only to local storage
          await _saveDataForGuestUser(planPreferences);
        }
      } else {
        // Incremental Save: Only save to local storage (skip Firestore)
        print('OnboardingProvider: Incremental save - saving to local storage only'); // Debug log
        await _saveToLocalStorage(planPreferences);
      }

      print('OnboardingProvider: _saveData operation completed successfully'); // Debug log
    } catch (e) {
      print('OnboardingProvider: Error in _saveData: $e'); // Debug log
      // Handle save error - still attempt local storage save as fallback
      try {
        // Prepare data again for fallback save
        final planPreferences = state.data.toJson();

        await _saveToLocalStorage(planPreferences);
        print('OnboardingProvider: Fallback save to local storage succeeded'); // Debug log
      } catch (fallbackError) {
        print('OnboardingProvider: Fallback save also failed: $fallbackError'); // Debug log
      }
    }
  }

  /// Public method to save onboarding data
  /// - [isCompleteSave]: true for complete form submission (wrapper page), false for individual step changes
  Future<void> saveOnboardingData({bool isCompleteSave = false}) async {
    await _saveData(isCompleteSave: isCompleteSave);
  }

  /// Save data for authenticated users: Firestore first, then local storage as backup
  Future<void> _saveDataForAuthenticatedUser(
    Map<String, dynamic> planPreferences,
  ) async {
    print('OnboardingProvider: Saving data for authenticated user'); // Debug log

    bool firestoreSaveSuccessful = false;
    String? firestoreError;

    try {
      // Attempt to save to Firestore first
      final firestoreService = ref.read(firestoreServiceProvider);

      // Save plan preferences to Firestore
      if (planPreferences.isNotEmpty) {
        await firestoreService.savePlanPreferences(planPreferences);
        print('OnboardingProvider: Plan preferences saved to Firestore successfully'); // Debug log
      }

      firestoreSaveSuccessful = true;
      print('OnboardingProvider: Firestore save completed successfully'); // Debug log

    } catch (e) {
      firestoreError = e.toString();
      print('OnboardingProvider: Firestore save failed: $firestoreError'); // Debug log
      // Continue to local storage save as fallback
    }

    // Always save to local storage as backup (even if Firestore succeeded)
    try {
      await _saveToLocalStorage(planPreferences);
      print('OnboardingProvider: Local storage backup save completed'); // Debug log
    } catch (localStorageError) {
      print('OnboardingProvider: Local storage backup save failed: $localStorageError'); // Debug log

      // If both Firestore and local storage failed, throw error
      if (!firestoreSaveSuccessful) {
        throw Exception('Both Firestore and local storage saves failed. Firestore: $firestoreError, Local: $localStorageError');
      }
    }
  }

  /// Save data for guest users: Local storage only
  Future<void> _saveDataForGuestUser(
    Map<String, dynamic> planPreferences,
  ) async {
    print('OnboardingProvider: Saving data for guest user (local storage only)'); // Debug log

    try {
      await _saveToLocalStorage(planPreferences);
      print('OnboardingProvider: Guest user data saved to local storage successfully'); // Debug log
    } catch (e) {
      print('OnboardingProvider: Guest user local storage save failed: $e'); // Debug log
      rethrow;
    }
  }

  /// Helper method to save data to local storage
  Future<void> _saveToLocalStorage(
    Map<String, dynamic> planPreferences,
  ) async {
    final localStorageService = await ref.read(localStorageServiceProvider.future);

    // Save plan preferences
    if (planPreferences.isNotEmpty) {
      await localStorageService.savePlanPreferences(planPreferences);
      print('OnboardingProvider: Plan preferences saved to local storage'); // Debug log
    }
  }

  /// Public method to load existing onboarding data (for edit mode)
  Future<void> loadExistingOnboardingData() async {
    try {
      print('Loading existing onboarding data for edit mode...'); // Debug log
      await _loadSavedData();
      print('Successfully loaded existing onboarding data'); // Debug log
    } catch (e) {
      print('Error loading existing onboarding data: $e'); // Debug log
      rethrow;
    }
  }

  /// Validate all steps
  void _validateAllSteps() {
    _validatePersonalInfo();
    _validatePhysicalInfo();
    _validateHealthInfo();
    _validateGoals();
    _validatePreferences();
  }

  /// Go to next step
  void nextStep() {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    if (currentIndex < OnboardingStep.values.length - 1) {
      final nextStep = OnboardingStep.values[currentIndex + 1];
      state = state.copyWith(currentStep: nextStep);
    }
  }

  /// Go to previous step
  void previousStep() {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    if (currentIndex > 0) {
      final previousStep = OnboardingStep.values[currentIndex - 1];
      state = state.copyWith(currentStep: previousStep);
    }
  }

  /// Go to specific step
  void goToStep(OnboardingStep step) {
    try {
      state = state.copyWith(currentStep: step);
      print('OnboardingNotifier: Successfully navigated to step: $step'); // Debug log

      // Auto-validate steps that don't require user input
      switch (step) {
        case OnboardingStep.healthInfo:
          _validateHealthInfo();
          break;
        case OnboardingStep.preferences:
          _validatePreferences();
          break;
        case OnboardingStep.personalInfo:
          _validatePersonalInfo();
          break;
        case OnboardingStep.physicalInfo:
          _validatePhysicalInfo();
          break;
        case OnboardingStep.goals:
          _validateGoals();
          break;
        case OnboardingStep.complete:
          // Complete step doesn't need validation
          break;
      }
    } catch (e) {
      print('OnboardingNotifier: Error navigating to step $step: $e'); // Debug log
    }
  }

  /// Update personal information
  void updatePersonalInfo({
    DateTime? dateOfBirth,
    Gender? gender,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        dateOfBirth: dateOfBirth ?? state.data.dateOfBirth,
        gender: gender ?? state.data.gender,
      ),
    );
    _validatePersonalInfo();
    _saveData(); // Incremental save - local storage only
  }

  /// Update physical information
  void updatePhysicalInfo({
    double? height,
    double? weight,
    ActivityLevel? activityLevel,
    int? dailyBurnedCalories,
    bool? useManualCalories,
    String? unitSystem,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        height: height ?? state.data.height,
        weight: weight ?? state.data.weight,
        activityLevel: activityLevel ?? state.data.activityLevel,
        dailyBurnedCalories: dailyBurnedCalories ?? state.data.dailyBurnedCalories,
        useManualCalories: useManualCalories ?? state.data.useManualCalories,
        unitSystem: unitSystem ?? state.data.unitSystem,
      ),
    );
    _validatePhysicalInfo();
    _saveData(); // Incremental save - local storage only
  }

  /// Update health information
  void updateHealthInfo({
    List<String>? allergies,
    List<String>? dietaryRestrictions,
    List<String>? healthConditions,
    List<String>? medications,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        allergies: allergies ?? state.data.allergies,
        dietaryRestrictions: dietaryRestrictions ?? state.data.dietaryRestrictions,
        healthConditions: healthConditions ?? state.data.healthConditions,
        medications: medications ?? state.data.medications,
      ),
    );
    _validateHealthInfo();
    _saveData(); // Incremental save - local storage only
  }

  /// Update goals
  void updateGoals({
    HealthGoal? healthGoal,
    double? targetWeight,
    int? dailyCalorieGoal,
    String? selectedCalorieRecommendation,
    bool clearSelectedRecommendation = false,
    bool autoSyncRecommendation = false,
  }) {
    // Handle explicit null for selectedCalorieRecommendation
    String? newSelectedRecommendation;
    if (clearSelectedRecommendation) {
      newSelectedRecommendation = null;
    } else if (selectedCalorieRecommendation != null) {
      newSelectedRecommendation = selectedCalorieRecommendation;
    } else {
      newSelectedRecommendation = state.data.selectedCalorieRecommendation;
    }

    // Auto-sync recommendation if requested and calorie goal is provided
    if (autoSyncRecommendation && dailyCalorieGoal != null) {
      newSelectedRecommendation = _findMatchingRecommendationKey(dailyCalorieGoal);
    }

    // Auto-select recommended option when health goal changes
    // Only if no explicit calorie goal is being set in this call
    int? finalCalorieGoal = dailyCalorieGoal;
    if (healthGoal != null &&
        healthGoal != state.data.healthGoal &&
        dailyCalorieGoal == null && // Don't override explicit calorie goal
        !clearSelectedRecommendation &&
        selectedCalorieRecommendation == null) {
      final recommendedCalories = _calculateDailyCalorieNeedsForGoal(healthGoal);
      if (recommendedCalories != null) {
        // Set both the calorie goal and the recommended selection
        final recommendedKey = _getRecommendedOptionKey(healthGoal);
        newSelectedRecommendation = recommendedKey;
        finalCalorieGoal = recommendedCalories;
      }
    }

    // Check if any values actually changed to prevent unnecessary updates
    final newHealthGoal = healthGoal ?? state.data.healthGoal;
    final newTargetWeight = targetWeight ?? state.data.targetWeight;
    final newDailyCalorieGoal = finalCalorieGoal ?? state.data.dailyCalorieGoal;

    // Only update if something actually changed
    if (newHealthGoal != state.data.healthGoal ||
        newTargetWeight != state.data.targetWeight ||
        newDailyCalorieGoal != state.data.dailyCalorieGoal ||
        newSelectedRecommendation != state.data.selectedCalorieRecommendation) {

      state = state.copyWith(
        data: state.data.copyWith(
          healthGoal: newHealthGoal,
          targetWeight: newTargetWeight,
          dailyCalorieGoal: newDailyCalorieGoal,
          selectedCalorieRecommendation: newSelectedRecommendation,
        ),
      );
      _validateGoals();
      _saveData(); // Incremental save - local storage only
    }
  }

  /// Update diet type and macro distribution
  void updateDietType({
    DietType? selectedDietType,
    MacroDistribution? customMacroDistribution,
    bool? useCustomMacros,
  }) {
    // Handle explicit null values for clearing fields
    final newSelectedDietType = selectedDietType == null && useCustomMacros == true
        ? null
        : selectedDietType ?? state.data.selectedDietType;

    final newCustomMacroDistribution = customMacroDistribution == null && useCustomMacros == false
        ? null
        : customMacroDistribution ?? state.data.customMacroDistribution;

    state = state.copyWith(
      data: state.data.copyWith(
        selectedDietType: newSelectedDietType,
        customMacroDistribution: newCustomMacroDistribution,
        useCustomMacros: useCustomMacros ?? state.data.useCustomMacros,
      ),
    );
    _validateGoals(); // Diet type affects goals validation
    _saveData(); // Incremental save - local storage only
  }

  /// Find matching recommendation key for a given calorie value
  String? _findMatchingRecommendationKey(int calories) {
    final recommendations = _getCalorieRecommendations();

    for (final rec in recommendations) {
      if ((rec['calories'] as int) == calories) {
        return rec['key'] as String;
      }
    }
    return null;
  }

  /// Get the recommended option key for a given health goal
  String _getRecommendedOptionKey(HealthGoal healthGoal) {
    switch (healthGoal) {
      case HealthGoal.loseWeight:
        return 'lose_moderate';
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        return 'gain_moderate';
      case HealthGoal.maintain:
      case HealthGoal.improveHealth:
        return 'maintain_balanced';
    }
  }

  /// Get calorie recommendations based on current user data
  List<Map<String, dynamic>> _getCalorieRecommendations() {
    final baseCalories = _calculateDailyCalorieNeeds();
    if (baseCalories == null) return [];

    final recommendations = <Map<String, dynamic>>[];

    switch (state.data.healthGoal) {
      case HealthGoal.loseWeight:
        recommendations.addAll([
          {
            'key': 'lose_fast',
            'calories': (baseCalories * 0.7).round(),
            'label': 'فقدان سريع',
            'description': 'فقدان 0.7-1 كيلو أسبوعياً (قاسي)',
            'isRecommended': false,
          },
          {
            'key': 'lose_moderate',
            'calories': baseCalories,
            'label': 'فقدان متوسط',
            'description': 'فقدان 0.5 كيلو أسبوعياً (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'lose_slow',
            'calories': (baseCalories * 1.1).round(),
            'label': 'فقدان بطيء',
            'description': 'فقدان 0.25 كيلو أسبوعياً (مستدام)',
            'isRecommended': false,
          },
        ]);
        break;
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        recommendations.addAll([
          {
            'key': 'gain_slow',
            'calories': (baseCalories * 0.9).round(),
            'label': 'زيادة بطيئة',
            'description': 'زيادة 0.25 كيلو أسبوعياً (نظيف)',
            'isRecommended': false,
          },
          {
            'key': 'gain_moderate',
            'calories': baseCalories,
            'label': 'زيادة متوسطة',
            'description': 'زيادة 0.5 كيلو أسبوعياً (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'gain_fast',
            'calories': (baseCalories * 1.2).round(),
            'label': 'زيادة سريعة',
            'description': 'زيادة 0.7-1 كيلو أسبوعياً (قوي)',
            'isRecommended': false,
          },
        ]);
      case HealthGoal.maintain:
      case HealthGoal.improveHealth:
        recommendations.addAll([
          {
            'key': 'maintain_conservative',
            'calories': (baseCalories * 0.9).round(),
            'label': 'محافظ',
            'description': 'للحفاظ على الوزن مع هامش أمان',
            'isRecommended': false,
          },
          {
            'key': 'maintain_balanced',
            'calories': baseCalories,
            'label': 'متوازن',
            'description': 'للحفاظ على الوزن الحالي (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'maintain_active',
            'calories': (baseCalories * 1.1).round(),
            'label': 'نشط',
            'description': 'للأشخاص الأكثر نشاطاً',
            'isRecommended': false,
          },
        ]);
    }

    return recommendations;
  }

  /// Calculate daily calorie needs for a specific health goal
  int? _calculateDailyCalorieNeedsForGoal(HealthGoal healthGoal) {
    if (state.data.height == null ||
        state.data.weight == null ||
        state.data.dateOfBirth == null ||
        state.data.gender == Gender.notSpecified) {
      return null;
    }

    final age = DateTime.now().year - state.data.dateOfBirth!.year;
    final height = state.data.height!;
    final weight = state.data.weight!;
    final isMale = state.data.gender == Gender.male;

    // Mifflin-St Jeor equation
    double bmr;
    if (isMale) {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) + 5;
    } else {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) - 161;
    }

    // Activity level multiplier
    double activityMultiplier;
    switch (state.data.activityLevel) {
      case ActivityLevel.sedentary:
        activityMultiplier = 1.2;
      case ActivityLevel.light:
        activityMultiplier = 1.375;
      case ActivityLevel.moderate:
        activityMultiplier = 1.55;
      case ActivityLevel.active:
        activityMultiplier = 1.725;
      case ActivityLevel.veryActive:
        activityMultiplier = 1.9;
    }

    final maintenanceCalories = (bmr * activityMultiplier).round();

    // Adjust based on health goal
    switch (healthGoal) {
      case HealthGoal.loseWeight:
        return (maintenanceCalories * 0.8).round(); // 20% deficit
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        return (maintenanceCalories * 1.15).round(); // 15% surplus
      case HealthGoal.maintain:
      case HealthGoal.improveHealth:
        return maintenanceCalories;
    }
  }

  /// Calculate daily calorie needs using Mifflin-St Jeor equation
  int? _calculateDailyCalorieNeeds() {
    if (state.data.height == null ||
        state.data.weight == null ||
        state.data.dateOfBirth == null ||
        state.data.gender == Gender.notSpecified) {
      return null;
    }

    final age = DateTime.now().year - state.data.dateOfBirth!.year;
    final height = state.data.height!;
    final weight = state.data.weight!;
    final isMale = state.data.gender == Gender.male;

    // Mifflin-St Jeor equation
    double bmr;
    if (isMale) {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) + 5;
    } else {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) - 161;
    }

    // Activity level multiplier
    double activityMultiplier;
    switch (state.data.activityLevel) {
      case ActivityLevel.sedentary:
        activityMultiplier = 1.2;
      case ActivityLevel.light:
        activityMultiplier = 1.375;
      case ActivityLevel.moderate:
        activityMultiplier = 1.55;
      case ActivityLevel.active:
        activityMultiplier = 1.725;
      case ActivityLevel.veryActive:
        activityMultiplier = 1.9;
    }

    final maintenanceCalories = (bmr * activityMultiplier).round();

    // Adjust based on health goal
    switch (state.data.healthGoal) {
      case HealthGoal.loseWeight:
        return (maintenanceCalories * 0.8).round(); // 20% deficit
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        return (maintenanceCalories * 1.15).round(); // 15% surplus
      case HealthGoal.maintain:
      case HealthGoal.improveHealth:
        return maintenanceCalories;
    }
  }

  /// Update preferences
  void updatePreferences({
    List<String>? favoriteIngredients,
    List<String>? dislikedIngredients,
    List<String>? favoriteCuisines,
    int? mealsPerDay,
    int? snacksPerDay,
  }) {
    state = state.copyWith(
      data: state.data.copyWith(
        favoriteIngredients: favoriteIngredients ?? state.data.favoriteIngredients,
        dislikedIngredients: dislikedIngredients ?? state.data.dislikedIngredients,
        favoriteCuisines: favoriteCuisines ?? state.data.favoriteCuisines,
        mealsPerDay: mealsPerDay ?? state.data.mealsPerDay,
        snacksPerDay: snacksPerDay ?? state.data.snacksPerDay,
      ),
    );
    _validatePreferences();
    _saveData(); // Incremental save - local storage only
  }



  /// Go to complete step (meal plan generation will happen there)
  void goToCompleteStep() {
    state = state.copyWith(currentStep: OnboardingStep.complete);
  }

  /// Complete onboarding (called from complete step after meal plan generation)
  Future<void> completeOnboarding() async {
    state = state.copyWith(isLoading: true);

    try {
      // Mark onboarding as completed in app state
      await ref.read(appStateNotifierProvider.notifier).completeOnboarding();

      // Mark onboarding as completed in local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.setOnboardingCompleted(true);

      // Sync onboarding data to user profile and Firestore
      await _syncOnboardingDataToProfile();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errors: {'general': 'Failed to complete onboarding'},
      );
    }
  }

  /// Sync onboarding data to user profile and Firestore
  Future<void> _syncOnboardingDataToProfile() async {
    try {
      final appState = ref.read(appStateNotifierProvider);
      final currentProfile = appState.userProfile;

      // Only sync if user is authenticated
      if (currentProfile == null) {
        return;
      }



      // Create updated core user profile (only core fields)
      final updatedProfile = currentProfile.copyWith(
        unitSystem: state.data.unitSystem,
        onboardingCompleted: true,
        updatedAt: DateTime.now(),
      );

      // Create plan preferences from onboarding data
      final planPreferences = PlanPreferences(
        // Personal Information
        dateOfBirth: state.data.dateOfBirth,
        gender: state.data.gender,

        // Physical Information
        height: state.data.height,
        weight: state.data.weight,
        activityLevel: state.data.activityLevel,
        dailyBurnedCalories: state.data.dailyBurnedCalories,
        useManualCalories: state.data.useManualCalories,

        // Health Information
        allergies: state.data.allergies,
        dietaryRestrictions: state.data.dietaryRestrictions,
        healthConditions: state.data.healthConditions,
        medications: state.data.medications,

        // Goals
        healthGoal: state.data.healthGoal,
        targetWeight: state.data.targetWeight,
        dailyCalorieGoal: state.data.dailyCalorieGoal,
        selectedCalorieRecommendation: state.data.selectedCalorieRecommendation,
        proteinGoal: state.data.proteinGoal,
        carbsGoal: state.data.carbsGoal,
        fatGoal: state.data.fatGoal,
        fiberGoal: state.data.fiberGoal,

        // Diet Type & Macros
        selectedDietType: state.data.selectedDietType,
        customMacroDistribution: state.data.customMacroDistribution,
        useCustomMacros: state.data.useCustomMacros,

        // Food Preferences
        favoriteIngredients: state.data.favoriteIngredients,
        dislikedIngredients: state.data.dislikedIngredients,
        favoriteCuisines: state.data.favoriteCuisines,
        mealsPerDay: state.data.mealsPerDay,
        snacksPerDay: state.data.snacksPerDay,
      );

      // Update models separately (notifications are handled separately in user_profile)
      await ref.read(appStateNotifierProvider.notifier).updateUserProfile(updatedProfile);
      await ref.read(appStateNotifierProvider.notifier).updatePlanPreferences(planPreferences);

    } catch (e) {
      // Log error but don't throw to avoid breaking onboarding completion
      // The data is still saved locally
    }
  }

  /// Pre-fill onboarding data from user profile and separate models (for settings pages)
  void preFillFromUserProfile(UserProfile userProfile, {PlanPreferences? planPreferences}) {
    // Use the separate models if available, otherwise use defaults

    state = state.copyWith(
      data: OnboardingData(
        // Personal Information (from planPreferences)
        dateOfBirth: planPreferences?.dateOfBirth,
        gender: planPreferences?.gender ?? Gender.notSpecified,

        // Physical Information (from planPreferences)
        height: planPreferences?.height,
        weight: planPreferences?.weight,
        activityLevel: planPreferences?.activityLevel ?? ActivityLevel.moderate,
        dailyBurnedCalories: planPreferences?.dailyBurnedCalories,
        useManualCalories: planPreferences?.useManualCalories ?? false,
        unitSystem: userProfile.unitSystem,

        // Health Information (from planPreferences)
        allergies: planPreferences?.allergies ?? [],
        dietaryRestrictions: planPreferences?.dietaryRestrictions ?? [],
        healthConditions: planPreferences?.healthConditions ?? [],
        medications: planPreferences?.medications ?? [],

        // Goals (from planPreferences)
        healthGoal: planPreferences?.healthGoal ?? HealthGoal.maintain,
        targetWeight: planPreferences?.targetWeight,
        dailyCalorieGoal: planPreferences?.dailyCalorieGoal,
        selectedCalorieRecommendation: planPreferences?.selectedCalorieRecommendation,
        proteinGoal: planPreferences?.proteinGoal,
        carbsGoal: planPreferences?.carbsGoal,
        fatGoal: planPreferences?.fatGoal,
        fiberGoal: planPreferences?.fiberGoal,

        // Diet Type & Macros (from planPreferences)
        selectedDietType: planPreferences?.selectedDietType,
        customMacroDistribution: planPreferences?.customMacroDistribution,
        useCustomMacros: planPreferences?.useCustomMacros ?? false,

        // Preferences (from planPreferences)
        favoriteIngredients: planPreferences?.favoriteIngredients ?? [],
        dislikedIngredients: planPreferences?.dislikedIngredients ?? [],
        favoriteCuisines: planPreferences?.favoriteCuisines ?? [],
        mealsPerDay: planPreferences?.mealsPerDay ?? 3,
        snacksPerDay: planPreferences?.snacksPerDay ?? 2,


      ),
    );

    // Validate all steps based on loaded data
    _validateAllSteps();

    // Save to local storage to keep it in sync
    _saveData();
  }

  /// Pre-fill onboarding data with random test values for testing purposes
  void preFillWithTestData() {
    final random = Random();

    // Sample data arrays
    final firstNames = ['Ahmed', 'Fatima', 'Omar', 'Aisha', 'Khalid', 'Layla', 'Hassan', 'Zeinab', 'Ali', 'Nour'];
    final lastNames = ['Al-Rashid', 'Al-Zahra', 'Al-Mansouri', 'Al-Hashimi', 'Al-Qasimi', 'Al-Maktoum', 'Al-Sabah', 'Al-Thani'];
    final allergies = ['Nuts', 'Dairy', 'Gluten', 'Shellfish', 'Eggs', 'Soy', 'Fish'];
    final dietaryRestrictions = ['Vegetarian', 'Vegan', 'Halal', 'Kosher', 'Low Carb', 'Keto', 'Paleo'];
    final healthConditions = ['Diabetes', 'Hypertension', 'High Cholesterol', 'Thyroid Issues', 'Heart Disease'];
    final medications = ['Metformin', 'Lisinopril', 'Atorvastatin', 'Levothyroxine', 'Aspirin'];
    final favoriteIngredients = ['Chicken', 'Rice', 'Tomatoes', 'Onions', 'Garlic', 'Olive Oil', 'Lemon', 'Parsley'];
    final dislikedIngredients = ['Liver', 'Mushrooms', 'Olives', 'Anchovies', 'Blue Cheese'];
    final favoriteCuisines = ['Arabic', 'Mediterranean', 'Italian', 'Asian', 'Mexican', 'Indian'];

    // Generate random birth date (18-65 years old)
    final now = DateTime.now();
    final minAge = 18;
    final maxAge = 65;
    final birthYear = now.year - (minAge + random.nextInt(maxAge - minAge));
    final birthMonth = 1 + random.nextInt(12);
    final birthDay = 1 + random.nextInt(28); // Safe day range for all months

    // Random physical data
    final height = 150.0 + random.nextDouble() * 50; // 150-200 cm
    final weight = 50.0 + random.nextDouble() * 50; // 50-100 kg
    final useManualCalories = random.nextBool();

    // Random target weight based on health goal
    final healthGoals = HealthGoal.values;
    final selectedHealthGoal = healthGoals[random.nextInt(healthGoals.length)];
    double? targetWeight;
    if (selectedHealthGoal == HealthGoal.loseWeight) {
      targetWeight = weight - (5 + random.nextDouble() * 15); // 5-20 kg less
    } else if (selectedHealthGoal == HealthGoal.gainWeight) {
      targetWeight = weight + (5 + random.nextDouble() * 15); // 5-20 kg more
    }

    // Random lists with 1-3 items each
    final randomAllergies = <String>[];
    final allergyCount = random.nextInt(3);
    for (int i = 0; i < allergyCount; i++) {
      final allergy = allergies[random.nextInt(allergies.length)];
      if (!randomAllergies.contains(allergy)) {
        randomAllergies.add(allergy);
      }
    }

    final randomDietaryRestrictions = <String>[];
    final restrictionCount = random.nextInt(2);
    for (int i = 0; i < restrictionCount; i++) {
      final restriction = dietaryRestrictions[random.nextInt(dietaryRestrictions.length)];
      if (!randomDietaryRestrictions.contains(restriction)) {
        randomDietaryRestrictions.add(restriction);
      }
    }

    final randomHealthConditions = <String>[];
    final conditionCount = random.nextInt(2);
    for (int i = 0; i < conditionCount; i++) {
      final condition = healthConditions[random.nextInt(healthConditions.length)];
      if (!randomHealthConditions.contains(condition)) {
        randomHealthConditions.add(condition);
      }
    }

    final randomMedications = <String>[];
    final medicationCount = random.nextInt(2);
    for (int i = 0; i < medicationCount; i++) {
      final medication = medications[random.nextInt(medications.length)];
      if (!randomMedications.contains(medication)) {
        randomMedications.add(medication);
      }
    }

    final randomFavoriteIngredients = <String>[];
    final favIngredientCount = 3 + random.nextInt(3); // 3-5 items
    for (int i = 0; i < favIngredientCount; i++) {
      final ingredient = favoriteIngredients[random.nextInt(favoriteIngredients.length)];
      if (!randomFavoriteIngredients.contains(ingredient)) {
        randomFavoriteIngredients.add(ingredient);
      }
    }

    final randomDislikedIngredients = <String>[];
    final dislikedIngredientCount = 1 + random.nextInt(3); // 1-3 items
    for (int i = 0; i < dislikedIngredientCount; i++) {
      final ingredient = dislikedIngredients[random.nextInt(dislikedIngredients.length)];
      if (!randomDislikedIngredients.contains(ingredient)) {
        randomDislikedIngredients.add(ingredient);
      }
    }

    final randomFavoriteCuisines = <String>[];
    final cuisineCount = 2 + random.nextInt(3); // 2-4 items
    for (int i = 0; i < cuisineCount; i++) {
      final cuisine = favoriteCuisines[random.nextInt(favoriteCuisines.length)];
      if (!randomFavoriteCuisines.contains(cuisine)) {
        randomFavoriteCuisines.add(cuisine);
      }
    }

    // Update state with all random data
    state = state.copyWith(
      data: OnboardingData(
        // Personal Information
        dateOfBirth: DateTime(birthYear, birthMonth, birthDay),
        gender: Gender.values[random.nextInt(Gender.values.length)],

        // Physical Information
        height: double.parse(height.toStringAsFixed(1)),
        weight: double.parse(weight.toStringAsFixed(1)),
        activityLevel: ActivityLevel.values[random.nextInt(ActivityLevel.values.length)],
        dailyBurnedCalories: useManualCalories ? 1500 + random.nextInt(1000) : null,
        useManualCalories: useManualCalories,
        unitSystem: 'metric',

        // Health Information
        allergies: randomAllergies,
        dietaryRestrictions: randomDietaryRestrictions,
        healthConditions: randomHealthConditions,
        medications: randomMedications,

        // Goals
        healthGoal: selectedHealthGoal,
        targetWeight: targetWeight != null ? double.parse(targetWeight.toStringAsFixed(1)) : null,
        dailyCalorieGoal: 1800 + random.nextInt(800), // 1800-2600 calories

        // Preferences
        favoriteIngredients: randomFavoriteIngredients,
        dislikedIngredients: randomDislikedIngredients,
        favoriteCuisines: randomFavoriteCuisines,
        mealsPerDay: 3 + random.nextInt(2), // 3-4 meals
        snacksPerDay: 1 + random.nextInt(3), // 1-3 snacks
      ),
    );

    // Validate all steps after pre-filling
    _validateAllSteps();

    // Save the test data
    _saveData();
  }

  /// Validate personal information step
  void _validatePersonalInfo() {
    final errors = <String, String>{};

    if (state.data.dateOfBirth == null) {
      errors['dateOfBirth'] = 'Date of birth is required';
    } else {
      final age = DateTime.now().year - state.data.dateOfBirth!.year;
      if (age < 13) {
        errors['dateOfBirth'] = 'You must be at least 13 years old';
      }
    }

    if (state.data.gender == Gender.notSpecified) {
      errors['gender'] = 'Please select your gender';
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.personalInfo: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('personal_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('personal_$key', value)),
        },
      );
    }
  }

  /// Validate physical information step
  void _validatePhysicalInfo() {
    final errors = <String, String>{};

    if (state.data.height == null || state.data.height! <= 0) {
      errors['height'] = 'Please enter a valid height';
    } else if (state.data.height! < 100 || state.data.height! > 250) {
      errors['height'] = 'Height must be between 100-250 cm';
    }

    if (state.data.weight == null || state.data.weight! <= 0) {
      errors['weight'] = 'Please enter a valid weight';
    } else if (state.data.weight! < 30 || state.data.weight! > 300) {
      errors['weight'] = 'Weight must be between 30-300 kg';
    }

    // Validate manual calories if that option is selected
    if (state.data.useManualCalories) {
      if (state.data.dailyBurnedCalories == null || state.data.dailyBurnedCalories! <= 0) {
        errors['dailyBurnedCalories'] = 'Please enter valid daily burned calories';
      } else if (state.data.dailyBurnedCalories! < 100 || state.data.dailyBurnedCalories! > 5000) {
        errors['dailyBurnedCalories'] = 'Daily burned calories must be between 100-5000';
      }
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.physicalInfo: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('physical_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('physical_$key', value)),
        },
      );
    }
  }

  /// Validate health information step
  void _validateHealthInfo() {
    // Health info is optional, so always valid
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.healthInfo: true},
    );
  }

  /// Validate goals step
  void _validateGoals() {
    final errors = <String, String>{};

    if (state.data.healthGoal == HealthGoal.loseWeight ||
        state.data.healthGoal == HealthGoal.gainWeight) {
      if (state.data.targetWeight == null || state.data.targetWeight! <= 0) {
        errors['targetWeight'] = 'Please enter your target weight';
      }
    }

    final isValid = errors.isEmpty;
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.goals: isValid},
      errors: {...state.errors}..removeWhere((key, value) => key.startsWith('goals_')),
    );

    if (errors.isNotEmpty) {
      state = state.copyWith(
        errors: {
          ...state.errors,
          ...errors.map((key, value) => MapEntry('goals_$key', value)),
        },
      );
    }
  }

  /// Validate preferences step
  void _validatePreferences() {
    // Preferences are optional, so always valid
    state = state.copyWith(
      stepValidation: {...state.stepValidation, OnboardingStep.preferences: true},
    );
  }



  /// Check if current step is valid
  bool get isCurrentStepValid {
    return state.stepValidation[state.currentStep] ?? false;
  }

  /// Check if can proceed to next step
  bool get canProceed {
    return isCurrentStepValid && state.currentStep != OnboardingStep.complete;
  }

  /// Get progress percentage
  double get progress {
    final currentIndex = OnboardingStep.values.indexOf(state.currentStep);
    final totalSteps = OnboardingStep.values.length - 1; // Exclude complete step
    return currentIndex / totalSteps;
  }

  /// Clear errors
  void clearErrors() {
    state = state.copyWith(errors: {});
  }

  /// Reset onboarding
  void reset() {
    state = const OnboardingState();
    _clearLocalData();
  }

  /// Clear all local onboarding data
  Future<void> _clearLocalData() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearPlanPreferences();
      await localStorageService.clearNotificationSettings();
      await localStorageService.clearCurrentOnboardingStep();
      await localStorageService.setOnboardingCompleted(false);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Check if onboarding is completed
  Future<bool> isOnboardingCompleted() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      return localStorageService.isOnboardingCompleted();
    } catch (e) {
      return false;
    }
  }
}
