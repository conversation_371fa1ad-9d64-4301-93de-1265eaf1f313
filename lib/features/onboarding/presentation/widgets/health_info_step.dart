import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/onboarding_provider.dart';

class HealthInfoStep extends ConsumerStatefulWidget {
  const HealthInfoStep({super.key});

  @override
  ConsumerState<HealthInfoStep> createState() => _HealthInfoStepState();
}

class _HealthInfoStepState extends ConsumerState<HealthInfoStep> {
  final _customAllergyController = TextEditingController();
  final _customRestrictionController = TextEditingController();
  final _customConditionController = TextEditingController();
  final _customMedicationController = TextEditingController();

  // Predefined options
  final List<String> _commonAllergies = [
    'المكسرات',
    'البيض',
    'الحليب ومنتجات الألبان',
    'الأسماك والمأكولات البحرية',
    'الصويا',
    'القمح والجلوتين',
    'الفول السوداني',
    'السمسم',
  ];

  final List<String> _commonRestrictions = [
    'حلال',
    'نباتي',
    'نباتي صرف',
    'خالي من الجلوتين',
    'خالي من اللاكتوز',
    'قليل الصوديوم',
    'قليل السكر',
    'خالي من المواد الحافظة',
  ];

  final List<String> _commonConditions = [
    'السكري',
    'ضغط الدم المرتفع',
    'أمراض القلب',
    'الكوليسترول المرتفع',
    'مشاكل الغدة الدرقية',
    'متلازمة القولون العصبي',
    'مرض الاضطرابات الهضمية',
    'النقرس',
  ];

  @override
  void initState() {
    super.initState();
    // Use post frame callback to safely read from provider after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final data = ref.read(onboardingNotifierProvider).data;
          // Data is already loaded in the provider, no local state to update
        } catch (e) {
          print('Error initializing HealthInfoStep: $e'); // Debug log
        }
      }
    });
  }

  @override
  void dispose() {
    _customAllergyController.dispose();
    _customRestrictionController.dispose();
    _customConditionController.dispose();
    _customMedicationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(onboardingNotifierProvider);
    final data = state.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الصحية',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أخبرنا عن أي حساسية أو قيود غذائية (اختياري)',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),

          // Allergies Card
          _buildCard(
            child: _buildSection(
              title: 'الحساسية الغذائية',
              description: 'اختر أي مواد غذائية تسبب لك حساسية',
              icon: Icons.warning_amber,
              color: Colors.orange,
              items: _commonAllergies,
              selectedItems: data.allergies,
              onItemToggle: (item) => _toggleAllergy(item),
              customController: _customAllergyController,
              onCustomAdd: _addCustomAllergy,
              hintText: 'أضف حساسية أخرى...',
            ),
          ),

          const SizedBox(height: 16),

          // Dietary Restrictions Card
          _buildCard(
            child: _buildSection(
              title: 'القيود الغذائية',
              description: 'حدد أي قيود غذائية تتبعها',
              icon: Icons.restaurant_menu,
              color: Colors.green,
              items: _commonRestrictions,
              selectedItems: data.dietaryRestrictions,
              onItemToggle: (item) => _toggleDietaryRestriction(item),
              customController: _customRestrictionController,
              onCustomAdd: _addCustomRestriction,
              hintText: 'أضف قيد غذائي آخر...',
            ),
          ),

          const SizedBox(height: 16),

          // Health Conditions Card
          _buildCard(
            child: _buildSection(
              title: 'الحالات الصحية',
              description: 'أخبرنا عن أي حالات صحية تؤثر على نظامك الغذائي',
              icon: Icons.health_and_safety,
              color: Colors.blue,
              items: _commonConditions,
              selectedItems: data.healthConditions,
              onItemToggle: (item) => _toggleHealthCondition(item),
              customController: _customConditionController,
              onCustomAdd: _addCustomCondition,
              hintText: 'أضف حالة صحية أخرى...',
            ),
          ),

          const SizedBox(height: 16),

          // Medications Card
          _buildCard(
            child: _buildMedicationsSection(data.medications),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> items,
    required List<String> selectedItems,
    required Function(String) onItemToggle,
    required TextEditingController customController,
    required VoidCallback onCustomAdd,
    required String hintText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        // Predefined items
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            final isSelected = selectedItems.contains(item);
            return FilterChip(
              label: Text(item),
              selected: isSelected,
              onSelected: (selected) => onItemToggle(item),
              selectedColor: color.withOpacity(0.2),
              checkmarkColor: color,
              side: BorderSide(color: isSelected ? color : Colors.grey.shade300),
            );
          }).toList(),
        ),

        const SizedBox(height: 16),

        // Custom input
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: customController,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onSubmitted: (_) => onCustomAdd(),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: onCustomAdd,
              icon: Icon(Icons.add, color: color),
              style: IconButton.styleFrom(
                backgroundColor: color.withOpacity(0.1),
              ),
            ),
          ],
        ),

        // Selected items display
        if (selectedItems.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: color),
                    const SizedBox(width: 6),
                    Text(
                      'المحدد (${selectedItems.length})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: selectedItems.map((item) {
                    return Chip(
                      label: Text(
                        item,
                        style: const TextStyle(fontSize: 12),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () => onItemToggle(item),
                      backgroundColor: color.withOpacity(0.1),
                      side: BorderSide.none,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMedicationsSection(List<String> medications) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.medication, color: Colors.red, size: 24),
            const SizedBox(width: 12),
            Text(
              'الأدوية الحالية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'أدخل أسماء الأدوية التي تتناولها حالياً (اختياري)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        // Custom medication input
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _customMedicationController,
                decoration: InputDecoration(
                  hintText: 'اسم الدواء...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onSubmitted: (_) => _addCustomMedication(),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _addCustomMedication,
              icon: const Icon(Icons.add, color: Colors.red),
              style: IconButton.styleFrom(
                backgroundColor: Colors.red.withOpacity(0.1),
              ),
            ),
          ],
        ),

        // Medications list
        if (medications.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.red),
                    const SizedBox(width: 6),
                    Text(
                      'الأدوية المضافة (${medications.length})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...medications.map((medication) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 6),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.medication, size: 16, color: Colors.red),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            medication,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => _removeMedication(medication),
                          icon: const Icon(Icons.close, size: 16),
                          color: Colors.red,
                          constraints: const BoxConstraints(),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // Helper methods
  void _toggleAllergy(String allergy) {
    final currentAllergies = List<String>.from(ref.read(onboardingNotifierProvider).data.allergies);
    if (currentAllergies.contains(allergy)) {
      currentAllergies.remove(allergy);
    } else {
      currentAllergies.add(allergy);
    }
    ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(allergies: currentAllergies);
  }

  void _addCustomAllergy() {
    final text = _customAllergyController.text.trim();
    if (text.isNotEmpty) {
      final currentAllergies = List<String>.from(ref.read(onboardingNotifierProvider).data.allergies);
      if (!currentAllergies.contains(text)) {
        currentAllergies.add(text);
        ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(allergies: currentAllergies);
      }
      _customAllergyController.clear();
    }
  }

  void _toggleDietaryRestriction(String restriction) {
    final currentRestrictions = List<String>.from(ref.read(onboardingNotifierProvider).data.dietaryRestrictions);
    if (currentRestrictions.contains(restriction)) {
      currentRestrictions.remove(restriction);
    } else {
      currentRestrictions.add(restriction);
    }
    ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(dietaryRestrictions: currentRestrictions);
  }

  void _addCustomRestriction() {
    final text = _customRestrictionController.text.trim();
    if (text.isNotEmpty) {
      final currentRestrictions = List<String>.from(ref.read(onboardingNotifierProvider).data.dietaryRestrictions);
      if (!currentRestrictions.contains(text)) {
        currentRestrictions.add(text);
        ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(dietaryRestrictions: currentRestrictions);
      }
      _customRestrictionController.clear();
    }
  }

  void _toggleHealthCondition(String condition) {
    final currentConditions = List<String>.from(ref.read(onboardingNotifierProvider).data.healthConditions);
    if (currentConditions.contains(condition)) {
      currentConditions.remove(condition);
    } else {
      currentConditions.add(condition);
    }
    ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(healthConditions: currentConditions);
  }

  void _addCustomCondition() {
    final text = _customConditionController.text.trim();
    if (text.isNotEmpty) {
      final currentConditions = List<String>.from(ref.read(onboardingNotifierProvider).data.healthConditions);
      if (!currentConditions.contains(text)) {
        currentConditions.add(text);
        ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(healthConditions: currentConditions);
      }
      _customConditionController.clear();
    }
  }

  void _addCustomMedication() {
    final text = _customMedicationController.text.trim();
    if (text.isNotEmpty) {
      final currentMedications = List<String>.from(ref.read(onboardingNotifierProvider).data.medications);
      if (!currentMedications.contains(text)) {
        currentMedications.add(text);
        ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(medications: currentMedications);
      }
      _customMedicationController.clear();
    }
  }

  void _removeMedication(String medication) {
    final currentMedications = List<String>.from(ref.read(onboardingNotifierProvider).data.medications);
    currentMedications.remove(medication);
    ref.read(onboardingNotifierProvider.notifier).updateHealthInfo(medications: currentMedications);
  }
}
