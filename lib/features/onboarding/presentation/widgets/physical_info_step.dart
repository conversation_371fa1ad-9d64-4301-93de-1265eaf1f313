import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../user_profile/data/models/user_profile.dart';
import '../../providers/onboarding_provider.dart';

class PhysicalInfoStep extends ConsumerStatefulWidget {
  const PhysicalInfoStep({super.key});

  @override
  ConsumerState<PhysicalInfoStep> createState() => _PhysicalInfoStepState();
}

class _PhysicalInfoStepState extends ConsumerState<PhysicalInfoStep> {
  double _selectedHeight = 170.0; // Default height in cm
  double _selectedWeight = 70.0; // Default weight in kg
  double _selectedCalories = 2000.0; // Default calories
  ActivityLevel _selectedActivityLevel = ActivityLevel.moderate;
  bool _useManualCalories = false;

  @override
  void initState() {
    super.initState();
    // Initialize with default values, will be updated by listener
    _selectedHeight = 170.0;
    _selectedWeight = 70.0;
    _selectedCalories = 2000.0;
    _selectedActivityLevel = ActivityLevel.moderate;
    _useManualCalories = false;

    // Use post frame callback to safely read from provider after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final data = ref.read(onboardingNotifierProvider).data;
          setState(() {
            _selectedHeight = data.height ?? 170.0;
            _selectedWeight = data.weight ?? 70.0;
            _selectedCalories = data.dailyBurnedCalories?.toDouble() ?? 2000.0;
            _selectedActivityLevel = data.activityLevel;
            _useManualCalories = data.useManualCalories;
          });
        } catch (e) {
          print('Error initializing PhysicalInfoStep: $e'); // Debug log
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Listen for state changes and update local state
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.data != next.data) {
        // Update local state variables
        if (next.data.height != null && _selectedHeight != next.data.height) {
          setState(() {
            _selectedHeight = next.data.height!;
          });
        }
        if (next.data.weight != null && _selectedWeight != next.data.weight) {
          setState(() {
            _selectedWeight = next.data.weight!;
          });
        }
        if (next.data.dailyBurnedCalories != null && _selectedCalories != next.data.dailyBurnedCalories!.toDouble()) {
          setState(() {
            _selectedCalories = next.data.dailyBurnedCalories!.toDouble();
          });
        }
        if (_selectedActivityLevel != next.data.activityLevel) {
          setState(() {
            _selectedActivityLevel = next.data.activityLevel;
          });
        }
        if (_useManualCalories != next.data.useManualCalories) {
          setState(() {
            _useManualCalories = next.data.useManualCalories;
          });
        }
      }
    });

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الجسدية',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل طولك ووزنك ومستوى نشاطك',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),

          // Height and Weight Pickers Row
          Row(
            children: [
              // Height Picker
              Expanded(
                child: _buildPickerCard(
                  title: 'الطول',
                  value: _selectedHeight.toInt(),
                  unit: 'سم',
                  icon: Icons.height,
                  onTap: () => _showHeightPicker(context),
                ),
              ),
              const SizedBox(width: 16),
              // Weight Picker
              Expanded(
                child: _buildPickerCard(
                  title: 'الوزن',
                  value: _selectedWeight.toInt(),
                  unit: 'كغ',
                  icon: Icons.monitor_weight,
                  onTap: () => _showWeightPicker(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Activity level or manual calories toggle
          Row(
            children: [
              Expanded(
                child: Text(
                  'طريقة تحديد النشاط',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Switch(
                value: _useManualCalories,
                onChanged: (value) {
                  setState(() {
                    _useManualCalories = value;
                  });
                  ref.read(onboardingNotifierProvider.notifier)
                      .updatePhysicalInfo(useManualCalories: value);
                },
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _useManualCalories
                ? 'إدخال السعرات المحروقة يومياً'
                : 'اختيار مستوى النشاط',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),

          if (_useManualCalories) ...[
            // Calories Slider
            _buildCaloriesSlider(),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'اسحب المؤشر لتحديد عدد السعرات الحرارية التي تحرقها يومياً',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Activity level selection
            ...ActivityLevel.values.map((level) => RadioListTile<ActivityLevel>(
              title: Text(_getActivityLevelLabel(level)),
              subtitle: Text(_getActivityLevelDescription(level)),
              value: level,
              groupValue: _selectedActivityLevel,
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedActivityLevel = value;
                  });
                  ref.read(onboardingNotifierProvider.notifier)
                      .updatePhysicalInfo(activityLevel: value);
                }
              },
            )),
          ],
        ],
      ),
    );
  }

  // Build picker card widget
  Widget _buildPickerCard({
    required String title,
    required int value,
    required String unit,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  value.toString(),
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  unit,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 3,
              width: 30,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getActivityLevelLabel(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'قليل الحركة';
      case ActivityLevel.light:
        return 'نشاط خفيف';
      case ActivityLevel.moderate:
        return 'نشاط متوسط';
      case ActivityLevel.active:
        return 'نشاط عالي';
      case ActivityLevel.veryActive:
        return 'نشاط عالي جداً';
    }
  }

  String _getActivityLevelDescription(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'عمل مكتبي، قليل من التمارين';
      case ActivityLevel.light:
        return 'تمارين خفيفة 1-3 أيام في الأسبوع';
      case ActivityLevel.moderate:
        return 'تمارين متوسطة 3-5 أيام في الأسبوع';
      case ActivityLevel.active:
        return 'تمارين شاقة 6-7 أيام في الأسبوع';
      case ActivityLevel.veryActive:
        return 'تمارين شاقة جداً أو عمل بدني';
    }
  }

  // Show height picker
  void _showHeightPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                    ),
                  ),
                  Text(
                    'اختر الطول',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.read(onboardingNotifierProvider.notifier)
                          .updatePhysicalInfo(height: _selectedHeight);
                      Navigator.pop(context);
                    },
                    child: Text(
                      'تم',
                      style: TextStyle(color: Theme.of(context).colorScheme.primary),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoPicker(
                itemExtent: 50,
                scrollController: FixedExtentScrollController(
                  initialItem: (_selectedHeight - 100).toInt(),
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedHeight = (100 + index).toDouble();
                  });
                },
                children: List.generate(
                  151, // 100cm to 250cm
                  (index) => Center(
                    child: Text(
                      '${100 + index} سم',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show weight picker
  void _showWeightPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                    ),
                  ),
                  Text(
                    'اختر الوزن',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.read(onboardingNotifierProvider.notifier)
                          .updatePhysicalInfo(weight: _selectedWeight);
                      Navigator.pop(context);
                    },
                    child: Text(
                      'تم',
                      style: TextStyle(color: Theme.of(context).colorScheme.primary),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoPicker(
                itemExtent: 50,
                scrollController: FixedExtentScrollController(
                  initialItem: (_selectedWeight - 30).toInt(),
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedWeight = (30 + index).toDouble();
                  });
                },
                children: List.generate(
                  171, // 30kg to 200kg
                  (index) => Center(
                    child: Text(
                      '${30 + index} كغ',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build calories slider
  Widget _buildCaloriesSlider() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السعرات المحروقة يومياً',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(
                Icons.local_fire_department,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Center(
            child: Column(
              children: [
                Text(
                  '${_selectedCalories.toInt()}',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  'سعرة حرارية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
            ),
            child: Slider(
              value: _selectedCalories,
              min: 200,
              max: 2000,
              divisions: 56, // 50 calorie increments
              onChanged: (value) {
                setState(() {
                  _selectedCalories = value;
                });
              },
              onChangeEnd: (value) {
                ref.read(onboardingNotifierProvider.notifier)
                    .updatePhysicalInfo(dailyBurnedCalories: value.toInt());
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '1200',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                '4000',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
