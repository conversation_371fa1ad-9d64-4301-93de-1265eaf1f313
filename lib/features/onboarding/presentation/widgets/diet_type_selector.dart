import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/diet_type.dart';
import '../../providers/onboarding_provider.dart';

class DietTypeSelector extends ConsumerStatefulWidget {
  const DietTypeSelector({super.key});

  @override
  ConsumerState<DietTypeSelector> createState() => _DietTypeSelectorState();
}

class _DietTypeSelectorState extends ConsumerState<DietTypeSelector> {
  bool _showCustomMacros = false;
  double _customCarbs = 50.0;
  double _customProtein = 20.0;
  double _customFat = 30.0;

  @override
  void initState() {
    super.initState();
    final data = ref.read(onboardingNotifierProvider).data;
    _showCustomMacros = data.useCustomMacros;

    if (data.customMacroDistribution != null) {
      _customCarbs = data.customMacroDistribution!.carbsPercentage;
      _customProtein = data.customMacroDistribution!.proteinPercentage;
      _customFat = data.customMacroDistribution!.fatPercentage;
    }

    // Set default diet type to balanced if none is selected
    if (data.selectedDietType == null && !data.useCustomMacros) {
      Future.microtask(() {
        ref.read(onboardingNotifierProvider.notifier).updateDietType(
          selectedDietType: DietType.balanced,
          useCustomMacros: false,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(onboardingNotifierProvider);
    final data = state.data;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع النظام الغذائي',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اختر النظام الغذائي المناسب لأهدافك',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 24),

        // Toggle between predefined and custom
        Row(
          children: [
            Expanded(
              child: _buildToggleButton(
                'أنظمة محددة مسبقاً',
                !_showCustomMacros,
                () => _toggleCustomMode(false),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildToggleButton(
                'تخصيص النسب',
                _showCustomMacros,
                () => _toggleCustomMode(true),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Content based on selection
        if (_showCustomMacros)
          _buildCustomMacrosSection()
        else
          _buildPredefinedDietsSection(data.selectedDietType),
      ],
    );
  }

  Widget _buildToggleButton(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isSelected 
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildPredefinedDietsSection(DietType? selectedType) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: DietTypes.predefined.length,
      itemBuilder: (context, index) {
        final dietData = DietTypes.predefined[index];
        final isSelected = selectedType == dietData.type;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildDietTypeCard(dietData, isSelected),
        );
      },
    );
  }

  Widget _buildDietTypeCard(DietTypeData dietData, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectDietType(dietData.type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    Theme.of(context).colorScheme.primary.withOpacity(0.05),
                  ],
                )
              : null,
          color: isSelected
              ? null
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: isSelected ? 2.5 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: IntrinsicHeight(
          child: Row(
            children: [
              // Left side: Icon and basic info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getIconData(dietData.iconName),
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

            // Middle: Title, description, and macro breakdown
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  Text(
                    dietData.nameAr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Description
                  Text(
                    dietData.descriptionAr,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Horizontal macro breakdown
                  _buildHorizontalMacroBreakdown(dietData.macroDistribution),
                ],
              ),
            ),

              // Right side: Selection indicator and benefit/warning
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Selection indicator
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.check,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 16,
                      ),
                    )
                  else
                    const SizedBox(height: 24),

                  // Single benefit or warning
                  if (dietData.benefits.isNotEmpty)
                    _buildCompactBenefit(dietData.benefits.first)
                  else if (dietData.warnings.isNotEmpty)
                    _buildCompactWarning(dietData.warnings.first)
                  else
                    const SizedBox.shrink(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactMacroBreakdown(MacroDistribution distribution) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildCompactMacroItem('ك', distribution.carbsPercentage, Colors.blue),
          _buildCompactMacroItem('ب', distribution.proteinPercentage, Colors.green),
          _buildCompactMacroItem('د', distribution.fatPercentage, Colors.orange),
        ],
      ),
    );
  }

  Widget _buildHorizontalMacroBreakdown(MacroDistribution distribution) {
    return Row(
      children: [
        _buildInlineMacroItem('ك', distribution.carbsPercentage, Colors.blue),
        const SizedBox(width: 12),
        _buildInlineMacroItem('ب', distribution.proteinPercentage, Colors.green),
        const SizedBox(width: 12),
        _buildInlineMacroItem('د', distribution.fatPercentage, Colors.orange),
      ],
    );
  }

  Widget _buildInlineMacroItem(String name, double percentage, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '$name ${percentage.toInt()}%',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 11,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactMacroItem(String name, double percentage, Color color) {
    return Column(
      children: [
        Text(
          '${percentage.toInt()}%',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 10,
          ),
        ),
        Text(
          name,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            fontSize: 9,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactBenefit(String benefit) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.check_circle_outline,
          size: 12,
          color: Colors.green.shade600,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            benefit,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.green.shade700,
              fontSize: 10,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildCompactWarning(String warning) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.warning_outlined,
          size: 12,
          color: Colors.orange.shade600,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            warning,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.orange.shade700,
              fontSize: 10,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildMacroBreakdown(MacroDistribution distribution) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildMacroItem('كربوهيدرات', distribution.carbsPercentage, Colors.blue),
          _buildMacroItem('بروتين', distribution.proteinPercentage, Colors.green),
          _buildMacroItem('دهون', distribution.fatPercentage, Colors.orange),
        ],
      ),
    );
  }

  Widget _buildMacroItem(String name, double percentage, Color color) {
    return Column(
      children: [
        Text(
          '${percentage.toInt()}%',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          name,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildBenefitsList(List<String> benefits) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: benefits.take(2).map((benefit) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 16,
              color: Colors.green.shade600,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                benefit,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green.shade700,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildWarningsList(List<String> warnings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: warnings.take(1).map((warning) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.warning_outlined,
              size: 16,
              color: Colors.orange.shade600,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                warning,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.orange.shade700,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildCustomMacrosSection() {
    final total = _customCarbs + _customProtein + _customFat;
    final isValid = total >= 99.0 && total <= 101.0;
    final warnings = DietTypes.getHealthWarnings(
      MacroDistribution(
        carbsPercentage: _customCarbs,
        proteinPercentage: _customProtein,
        fatPercentage: _customFat,
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تخصيص النسب المئوية للعناصر الغذائية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Carbs slider
        _buildMacroSlider(
          'الكربوهيدرات',
          _customCarbs,
          Colors.blue,
          (value) {
            setState(() => _customCarbs = value);
            _autoApplyCustomMacros(); // Auto-apply when slider changes
          },
        ),
        const SizedBox(height: 16),

        // Protein slider
        _buildMacroSlider(
          'البروتين',
          _customProtein,
          Colors.green,
          (value) {
            setState(() => _customProtein = value);
            _autoApplyCustomMacros(); // Auto-apply when slider changes
          },
        ),
        const SizedBox(height: 16),

        // Fat slider
        _buildMacroSlider(
          'الدهون',
          _customFat,
          Colors.orange,
          (value) {
            setState(() => _customFat = value);
            _autoApplyCustomMacros(); // Auto-apply when slider changes
          },
        ),
        const SizedBox(height: 16),

        // Total validation
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isValid 
                ? Colors.green.shade50
                : Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isValid 
                  ? Colors.green.shade300
                  : Colors.red.shade300,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isValid ? Icons.check_circle : Icons.error,
                color: isValid ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'المجموع: ${total.toInt()}%',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isValid ? Colors.green.shade700 : Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (!isValid) ...[
                const Spacer(),
                Text(
                  'يجب أن يكون المجموع 100%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.red.shade700,
                  ),
                ),
              ],
            ],
          ),
        ),

        // Health warnings
        if (warnings.isNotEmpty) ...[
          const SizedBox(height: 12),
          ...warnings.map((warning) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.warning_outlined,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    warning,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.orange.shade700,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],


      ],
    );
  }

  Widget _buildMacroSlider(
    String name,
    double value,
    Color color,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              name,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${value.toInt()}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: color,
            thumbColor: color,
            overlayColor: color.withOpacity(0.2),
          ),
          child: Slider(
            value: value,
            min: 0,
            max: 100,
            divisions: 100,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'balance':
        return Icons.balance;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'local_fire_department':
        return Icons.local_fire_department;
      case 'trending_down':
        return Icons.trending_down;
      case 'favorite':
        return Icons.favorite;
      case 'eco':
        return Icons.eco;
      default:
        return Icons.restaurant;
    }
  }

  void _toggleCustomMode(bool useCustom) {
    setState(() {
      _showCustomMacros = useCustom;
    });

    if (useCustom) {
      // When switching to custom mode, clear any selected predefined diet type
      // and automatically apply current custom macros if valid
      ref.read(onboardingNotifierProvider.notifier).updateDietType(
        useCustomMacros: true,
        selectedDietType: null, // Clear predefined selection
      );
      _autoApplyCustomMacros(); // Automatically apply custom macros
    } else {
      // When switching back to predefined mode, set balanced as default
      ref.read(onboardingNotifierProvider.notifier).updateDietType(
        useCustomMacros: false,
        selectedDietType: DietType.balanced,
        customMacroDistribution: null, // Clear custom distribution
      );
    }
  }

  void _selectDietType(DietType dietType) {
    ref.read(onboardingNotifierProvider.notifier).updateDietType(
      selectedDietType: dietType,
      useCustomMacros: false,
    );
  }

  void _applyCustomMacros() {
    final distribution = MacroDistribution(
      carbsPercentage: _customCarbs,
      proteinPercentage: _customProtein,
      fatPercentage: _customFat,
    );

    ref.read(onboardingNotifierProvider.notifier).updateDietType(
      selectedDietType: DietType.custom,
      customMacroDistribution: distribution,
      useCustomMacros: true,
    );
  }

  /// Automatically apply custom macros if they are valid
  void _autoApplyCustomMacros() {
    final total = _customCarbs + _customProtein + _customFat;
    final isValid = total >= 99.0 && total <= 101.0;

    if (isValid) {
      _applyCustomMacros();
    }
  }
}
