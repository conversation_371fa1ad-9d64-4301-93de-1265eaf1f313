import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/firestore_service.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../data/models/plan_preferences.dart';
import '../../providers/onboarding_provider.dart';

class SettingsSaveStep extends ConsumerStatefulWidget {
  const SettingsSaveStep({super.key});

  @override
  ConsumerState<SettingsSaveStep> createState() => _SettingsSaveStepState();
}

class _SettingsSaveStepState extends ConsumerState<SettingsSaveStep> {
  bool _isSaving = false;
  String? _saveError;
  bool _saveSuccess = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            if (_isSaving) ...[
              // Saving state
              const CircularProgressIndicator(),
              const SizedBox(height: 24),
              Text(
                'جاري حفظ الإعدادات...',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'يتم الآن حفظ إعداداتك الشخصية',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ] else if (_saveSuccess) ...[
              // Success state
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  size: 40,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'تم الحفظ بنجاح!',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'تم حفظ جميع إعداداتك الشخصية بنجاح',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    context.pop(); // Close modal
                    context.pop(); // Go back to profile
                  },
                  child: const Text('العودة للملف الشخصي'),
                ),
              ),
            ] else if (_saveError != null) ...[
              // Error state
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 40,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'حدث خطأ في الحفظ',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                _saveError!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => context.pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveSettings,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // Initial state
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.save,
                  size: 40,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'حفظ الإعدادات',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'هل تريد حفظ جميع التغييرات التي قمت بها؟',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => context.pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveSettings,
                      child: const Text('حفظ'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isSaving = true;
      _saveError = null;
      _saveSuccess = false;
    });

    try {
      final onboardingData = ref.read(onboardingNotifierProvider).data;
      final appState = ref.read(appStateNotifierProvider);


      // Check if user is authenticated
      if (appState.userProfile == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      AppLogger.info('SettingsSaveStep: Starting to save settings to Firestore');

      // Create separate models from onboarding data

      // Update core user profile (only core fields)
      final updatedProfile = appState.userProfile!.copyWith(
        unitSystem: onboardingData.unitSystem,
        updatedAt: DateTime.now(),
      );

      // Create plan preferences from onboarding data
      final planPreferences = PlanPreferences(
        // Personal Information
        dateOfBirth: onboardingData.dateOfBirth,
        gender: onboardingData.gender,

        // Physical Information
        height: onboardingData.height,
        weight: onboardingData.weight,
        activityLevel: onboardingData.activityLevel,
        dailyBurnedCalories: onboardingData.dailyBurnedCalories,
        useManualCalories: onboardingData.useManualCalories,

        // Health Information
        allergies: onboardingData.allergies,
        dietaryRestrictions: onboardingData.dietaryRestrictions,
        healthConditions: onboardingData.healthConditions,
        medications: onboardingData.medications,

        // Goals
        healthGoal: onboardingData.healthGoal,
        targetWeight: onboardingData.targetWeight,
        dailyCalorieGoal: onboardingData.dailyCalorieGoal,
        proteinGoal: onboardingData.proteinGoal,
        carbsGoal: onboardingData.carbsGoal,
        fatGoal: onboardingData.fatGoal,
        fiberGoal: onboardingData.fiberGoal,

        // Diet preferences
        selectedDietType: onboardingData.selectedDietType,
        customMacroDistribution: onboardingData.customMacroDistribution,
        useCustomMacros: onboardingData.useCustomMacros,

        // Preferences
        favoriteIngredients: onboardingData.favoriteIngredients,
        dislikedIngredients: onboardingData.dislikedIngredients,
        favoriteCuisines: onboardingData.favoriteCuisines,
        mealsPerDay: onboardingData.mealsPerDay,
        snacksPerDay: onboardingData.snacksPerDay,
      );

      // Update models separately (notifications are handled separately in user_profile)
      await ref.read(appStateNotifierProvider.notifier).updateUserProfile(updatedProfile);
      await ref.read(appStateNotifierProvider.notifier).updatePlanPreferences(planPreferences);

      AppLogger.info('SettingsSaveStep: Settings saved successfully');

      setState(() {
        _isSaving = false;
        _saveSuccess = true;
      });

      // Auto close after 2 seconds
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          context.pop(); // Close modal
          context.pop(); // Go back to profile
        }
      });

    } catch (e) {
      AppLogger.error('SettingsSaveStep: Error saving settings: $e');

      setState(() {
        _isSaving = false;
        _saveError = 'فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.';
      });
    }
  }
}
