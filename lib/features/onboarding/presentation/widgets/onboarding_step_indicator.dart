import 'package:flutter/material.dart';
import '../../providers/onboarding_provider.dart';

class OnboardingStepIndicator extends StatelessWidget {
  final OnboardingStep currentStep;
  final double progress;
  final int? totalSteps;
  final List<OnboardingStep>? steps;

  const OnboardingStepIndicator({
    super.key,
    required this.currentStep,
    required this.progress,
    this.totalSteps,
    this.steps,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress bar
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),

        // Step indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _getStepsToShow()
              .map((step) => _buildStepDot(context, step))
              .toList(),
        ),
      ],
    );
  }

  List<OnboardingStep> _getStepsToShow() {
    if (steps != null) {
      return steps!;
    }
    return OnboardingStep.values
        .where((step) => step != OnboardingStep.complete)
        .toList();
  }

  Widget _buildStepDot(BuildContext context, OnboardingStep step) {
    final stepsToShow = _getStepsToShow();
    final isActive = step == currentStep;
    final isCompleted = stepsToShow.indexOf(step) < stepsToShow.indexOf(currentStep);

    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isActive || isCompleted
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.outline.withOpacity(0.3),
      ),
      child: isCompleted
          ? Icon(
              Icons.check,
              size: 8,
              color: Theme.of(context).colorScheme.onPrimary,
            )
          : null,
    );
  }
}
