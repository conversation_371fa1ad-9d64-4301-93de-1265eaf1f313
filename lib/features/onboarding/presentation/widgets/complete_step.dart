import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:easydietai/core/router/app_routes.dart';
import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/theme/app_colors.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';
import 'package:easydietai/features/meal_planning/data/services/meal_plan_service.dart';
import 'package:easydietai/features/onboarding/providers/onboarding_provider.dart';
import 'package:easydietai/features/weight_tracking/providers/weight_tracking_provider.dart';
import 'package:easydietai/shared/providers/app_state_provider.dart';
import 'package:easydietai/shared/widgets/custom_button.dart';

class CompleteStep extends ConsumerStatefulWidget {
  const CompleteStep({super.key});

  @override
  ConsumerState<CompleteStep> createState() => _CompleteStepState();
}

class _CompleteStepState extends ConsumerState<CompleteStep>
    with TickerProviderStateMixin {
  bool _isGeneratingMealPlan = false;
  String? _generationError;
  StreamSubscription<QuerySnapshot>? _mealsSubscription;
  bool _isWaitingForMealData = false;
  String _currentStatusMessage = '';

  // Animation controllers
  late AnimationController _pulseAnimationController;
  late AnimationController _fadeAnimationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    // Check for existing meal generation loading state and handle accordingly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleMealGenerationState();
    });
  }

  void _initializeAnimations() {
    // Pulse animation for loading indicator
    _pulseAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    // Fade animation for state transitions
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimationController.forward();
  }

  /// Handle existing meal generation loading state
  Future<void> _handleMealGenerationState() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final mealGenerationLoading = localStorageService.loadMealGenerationLoading();

      if (mealGenerationLoading != null) {
        final isLoading = mealGenerationLoading['isLoading'] as bool? ?? false;
        final timestamp = mealGenerationLoading['timestamp'] as int? ?? 0;

        if (isLoading && timestamp > 0) {
          final now = DateTime.now().millisecondsSinceEpoch;
          final fiveMinutesInMs = 5 * 60 * 1000;
          final timeDifference = now - timestamp;

          AppLogger.info('CompleteStep: Found existing meal generation loading state');
          AppLogger.info('CompleteStep: Timestamp difference: ${timeDifference}ms (${(timeDifference / 1000 / 60).toStringAsFixed(1)} minutes)');

          if (timeDifference < fiveMinutesInMs) {
            // Less than 5 minutes old - assume generation is still in progress
            AppLogger.info('CompleteStep: Meal generation is recent (< 5 minutes), setting to generating mode without calling function');

            setState(() {
              _isGeneratingMealPlan = false;
              _isWaitingForMealData = true;
              _generationError = null;
              _currentStatusMessage = 'جاري إنشاء الوجبات المخصصة لك...';
            });

            // Start pulse animation
            _pulseAnimationController.repeat(reverse: true);

            // Set up Firebase listener to wait for completion
            _setupFirestoreListener();

            // Start timeout timer
            _startTimeoutTimer();

            return; // Don't call _completeOnboarding()
          } else {
            // 5 minutes or older - consider expired, restart generation
            AppLogger.info('CompleteStep: Meal generation is expired (>= 5 minutes), restarting generation');

            // Update timestamp and proceed with new generation
            await localStorageService.saveMealGenerationLoading(true);
          }
        }
      }

      // No existing loading state or expired - start normal completion process
      AppLogger.info('CompleteStep: No existing meal generation state, starting normal completion');
      await _completeOnboarding();

    } catch (e) {
      AppLogger.error('CompleteStep: Error handling meal generation state: $e');
      // Fall back to normal completion process
      await _completeOnboarding();
    }
  }

  @override
  void dispose() {
    _mealsSubscription?.cancel();
    _pulseAnimationController.dispose();
    _fadeAnimationController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    setState(() {
      _isGeneratingMealPlan = true;
      _generationError = null;
      _currentStatusMessage = 'جاري التحقق من المصادقة...';
    });

    // Start pulse animation
    _pulseAnimationController.repeat(reverse: true);

    // Add haptic feedback
    HapticFeedback.lightImpact();

    try {
      AppLogger.info('Starting onboarding completion process');

      // 1. Firebase User Authentication Check
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        AppLogger.warning('No authenticated user found during onboarding completion - showing error state directly');

        // Stop animations and show error state directly
        _pulseAnimationController.stop();

        setState(() {
          _isGeneratingMealPlan = false;
          _isWaitingForMealData = false;
          _generationError = 'يجب تسجيل الدخول أولاً';
        });
        return;
      }

      AppLogger.info('User authenticated: ${currentUser.uid}');

      setState(() {
        _currentStatusMessage = 'جاري تحميل البيانات المحفوظة...';
      });

      // 2. Data Extraction from Local Storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);

      final planPreferencesData = localStorageService.loadPlanPreferences();
      final notificationSettingsData = localStorageService.loadNotificationSettings();

      AppLogger.info('Plan preferences loaded: ${planPreferencesData != null}');
      AppLogger.info('Notification settings loaded: ${notificationSettingsData != null}');

      setState(() {
        _currentStatusMessage = 'جاري حفظ تفضيلاتك في السحابة...';
      });

      // 3. Firestore Data Synchronization with snake_case field names
      await _syncDataToFirestore(planPreferencesData, notificationSettingsData);

      // 4. Add initial weight tracking entry if weight is available
      await _addInitialWeightEntry(planPreferencesData);

      setState(() {
        _currentStatusMessage = 'جاري تحليل تفضيلاتك الغذائية...';
      });

      // 5. Set up Firebase listener before triggering generation
      _setupFirestoreListener();

      // 6. Set loading flag with timestamp in local storage
      await localStorageService.saveMealGenerationLoading(true);

      setState(() {
        _currentStatusMessage = 'جاري إنشاء خطة الوجبات المثالية لك...';
      });

      // 7. Call generate_meal Firebase Function using Dio HTTP client
      final apiResponse = await _callGenerateMealFunction();

      // Handle API response
      if (apiResponse['success'] == true) {
        AppLogger.info('Meal plan generation started successfully, waiting for Firestore data...');

        setState(() {
          _isGeneratingMealPlan = false;
          _isWaitingForMealData = true;
          _currentStatusMessage = 'جاري إنشاء الوجبات المخصصة لك...';
        });

        // Add haptic feedback for success
        HapticFeedback.mediumImpact();

        // Start timeout timer (5 minutes)
        _startTimeoutTimer();

        // The Firebase listener will handle completion when data arrives
      } else {
        throw Exception('Failed to start meal plan generation: ${apiResponse['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      AppLogger.error('Error during onboarding completion: $e');
      await _handleOnboardingError(e);
    }
  }



  /// Call generate_meal Firebase Function using Dio HTTP client
  Future<Map<String, dynamic>> _callGenerateMealFunction() async {
    try {
      final mealPlanService = await ref.read(mealPlanServiceProvider.future);
      final response = await mealPlanService.generateMealPlan();

      // Handle different response types
      if (response is AsyncMealPlanResponse) {
        return {
          'success': response.success,
          'message': response.message,
        };
      } else if (response is MealPlanResponse) {
        // Synchronous response - complete immediately
        AppLogger.info('Meal plan generated synchronously');
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.clearMealGenerationLoading();

        setState(() {
          _currentStatusMessage = 'تم إنشاء خطة الوجبات بنجاح!';
        });

        // Stop pulse animation
        _pulseAnimationController.stop();

        await _completeOnboardingProcess();

        return {
          'success': true,
          'message': 'Meal plan generated successfully',
        };
      } else {
        throw Exception('Unexpected response type from meal plan generation');
      }
    } catch (e) {
      AppLogger.error('Error calling generate_meal function: $e');
      throw e;
    }
  }

  /// Start timeout timer for meal generation
  void _startTimeoutTimer() {
    Timer(const Duration(minutes: 5), () {
      if (_isWaitingForMealData && mounted) {
        AppLogger.warning('Meal generation timeout reached');
        _handleMealGenerationTimeout();
      }
    });
  }

  /// Handle meal generation timeout
  Future<void> _handleMealGenerationTimeout() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearMealGenerationLoading();

      setState(() {
        _isWaitingForMealData = false;
        _isGeneratingMealPlan = false;
        _generationError = 'انتهت مهلة إنشاء خطة الوجبات';
        _currentStatusMessage = '';
      });

      // Stop animations
      _pulseAnimationController.stop();

      // Cancel Firestore listener
      await _mealsSubscription?.cancel();
      _mealsSubscription = null;
    } catch (e) {
      AppLogger.error('Error handling meal generation timeout: $e');
    }
  }

  /// Handle onboarding completion errors
  Future<void> _handleOnboardingError(dynamic error) async {
    // Stop animations
    _pulseAnimationController.stop();

    // Add error haptic feedback
    HapticFeedback.heavyImpact();

    // Clear loading flag on error
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearMealGenerationLoading();
    } catch (storageError) {
      AppLogger.warning('Failed to clear loading flag: $storageError');
    }

    setState(() {
      _isGeneratingMealPlan = false;
      _isWaitingForMealData = false;
      _generationError = 'حدث خطأ في إنشاء خطة الوجبات';
      _currentStatusMessage = '';
    });

    // Show error message to user
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إكمال الإعداد: ${error.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// Retry the onboarding completion process
  Future<void> _retryOnboardingCompletion() async {
    AppLogger.info('Retrying onboarding completion');

    // Reset error state
    setState(() {
      _generationError = null;
    });

    // Restart the completion process
    await _completeOnboarding();
  }

  /// Set up Firestore listener to monitor for new meal plan documents
  void _setupFirestoreListener() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      AppLogger.warning('No authenticated user found for Firestore listener');
      return;
    }

    AppLogger.info('Setting up Firestore listener for meal plan generation');

    // Listen for changes in the user's meals collection
    _mealsSubscription = FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('days')
        .orderBy('date', descending: false)
        .snapshots()
        .listen(
      _handleFirestoreUpdate,
      onError: (Object error) {
        AppLogger.warning('Error listening to Firestore meals during onboarding: $error');
        // On error, complete onboarding anyway after a delay
        Future<void>.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            _completeOnboardingProcess();
          }
        });
      },
    );
  }

  /// Handle Firestore updates and complete onboarding when new meal data arrives
  Future<void> _handleFirestoreUpdate(QuerySnapshot snapshot) async {
    try {
      // Only process if we're waiting for meal data
      if (!_isWaitingForMealData) return;

      if (snapshot.docs.isNotEmpty) {
        AppLogger.info('Detected ${snapshot.docs.length} meal documents from Firestore during onboarding');

        setState(() {
          _currentStatusMessage = 'تم إنشاء خطة الوجبات بنجاح!';
        });

        // Stop pulse animation
        _pulseAnimationController.stop();

        // Add success haptic feedback
        HapticFeedback.mediumImpact();

        // Clear loading flag since new meals have arrived
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.clearMealGenerationLoading();

        // Cancel the listener as we no longer need it
        await _mealsSubscription?.cancel();
        _mealsSubscription = null;

        // Execute complete data loading and complete onboarding process
        await _executeCompleteDataLoadingAndCompletion();
      }
    } catch (e) {
      AppLogger.warning('Error handling Firestore update during onboarding: $e');
      // On error, complete onboarding anyway
      await _completeOnboardingProcess();
    }
  }

  /// Execute complete data loading and complete onboarding process
  Future<void> _executeCompleteDataLoadingAndCompletion() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Load meal plans from users/{userId}/days collection
      final daysQuery = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('days')
          .orderBy('date', descending: false)
          .get();

      if (daysQuery.docs.isNotEmpty) {
        // Convert Firestore documents to meal plan data
        final mealPlanData = daysQuery.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();

        // Store meal data in meal_plans FlutterSecureStorage key
        final storageService = await ref.read(localStorageServiceProvider.future);
        await storageService.storeSecureObject('meal_plans', {
          'days': mealPlanData,
          'loaded_at': DateTime.now().toIso8601String(),
        });

        AppLogger.info('Meal plan data loaded and stored in local storage');
      }

      // Verify all required data is in local storage
      await _verifyLocalStorageData();

      // Set onboarding_completed to true in Firestore
      await _setOnboardingCompletedInFirestore(user.uid);

      // Complete onboarding process
      await _completeOnboardingProcess();
    } catch (e) {
      AppLogger.error('Error in complete data loading and completion: $e');
      // Still try to complete onboarding
      await _completeOnboardingProcess();
    }
  }

  /// Verify all required data is in local storage
  Future<void> _verifyLocalStorageData() async {
    try {
      final storageService = await ref.read(localStorageServiceProvider.future);

      final planPrefs = await storageService.getSecureObject('plan_preferences');
      final notificationSettings = await storageService.getSecureObject('notification_settings');
      final currentMealPlan = await storageService.getSecureObject('meal_plans');

      if (planPrefs == null) {
        throw Exception('plan_preferences not found in local storage');
      }
      if (notificationSettings == null) {
        throw Exception('notification_settings not found in local storage');
      }
      if (currentMealPlan == null) {
        throw Exception('meal_plans not found in local storage');
      }

      AppLogger.info('All required data verified in local storage');
    } catch (e) {
      AppLogger.warning('Local storage data verification failed: $e');
      throw e;
    }
  }

  /// Set onboarding_completed to true in Firestore
  Future<void> _setOnboardingCompletedInFirestore(String userId) async {
    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .set({
        'onboarding_completed': true,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      AppLogger.info('onboarding_completed set to true in Firestore');
    } catch (e) {
      AppLogger.error('Failed to set onboarding_completed in Firestore: $e');
      throw e;
    }
  }

  /// Complete the onboarding process and navigate to home
  Future<void> _completeOnboardingProcess() async {
    if (!mounted) return;

    try {
      setState(() {
        _isWaitingForMealData = false;
        _isGeneratingMealPlan = false;
        _generationError = null;
      });

      AppLogger.info('Completing onboarding process');

      // Complete onboarding through the provider
      await ref.read(onboardingNotifierProvider.notifier).completeOnboarding();

      AppLogger.info('Onboarding completed successfully');
    } catch (e) {
      AppLogger.warning('Error completing onboarding process: $e');

      setState(() {
        _generationError = 'حدث خطأ في إكمال الإعداد';
      });

      // Still try to complete onboarding even if there's an error
      try {
        await ref.read(onboardingNotifierProvider.notifier).completeOnboarding();
      } catch (completionError) {
        AppLogger.warning('Failed to complete onboarding after error: $completionError');
      }
    }
  }

  /// Handle login action from error state for unauthenticated users
  Future<void> _handleLoginFromErrorState() async {
    try {
      AppLogger.info('Login button clicked from error state - performing complete login flow');

      final localStorageService = await ref.read(localStorageServiceProvider.future);

      // Save current onboarding step number to local storage (complete step is step 6)
      await localStorageService.saveCurrentOnboardingStep(6);
      AppLogger.info('Saved current onboarding step (6) from error state');

      // Stop any ongoing animations and clear loading states
      _pulseAnimationController.stop();
      await _mealsSubscription?.cancel();
      _mealsSubscription = null;

      setState(() {
        _isGeneratingMealPlan = false;
        _isWaitingForMealData = false;
        _generationError = null; // Clear error state since we're navigating to auth
      });

      AppLogger.info('Cleared loading states from error state, preparing to navigate to auth page');

      // Navigate to welcome_auth_page using proper route constant
      if (mounted) {
        AppLogger.info('Navigating to welcome auth page from error state: ${AppRoutes.auth}');
        AppLogger.info('Current authentication status: ${FirebaseAuth.instance.currentUser != null}');
        AppLogger.info('Current guest user status: ${ref.read(appStateNotifierProvider).isGuestUser}');
        AppLogger.info('Current onboarding completed status: ${ref.read(appStateNotifierProvider).isOnboardingCompleted}');

        context.go(AppRoutes.auth);
        AppLogger.info('Navigation to auth page from error state initiated');

        // Add a small delay to check if navigation was successful
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            final currentRoute = GoRouterState.of(context).uri.path;
            AppLogger.info('Current route after navigation attempt from error state: $currentRoute');
            if (currentRoute != AppRoutes.auth) {
              AppLogger.warning('Navigation to auth page may have been redirected. Expected: ${AppRoutes.auth}, Actual: $currentRoute');
            }
          }
        });
      } else {
        AppLogger.warning('Widget not mounted, cannot navigate to auth page from error state');
      }
    } catch (e) {
      AppLogger.error('Error in complete login flow from error state: $e');

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الانتقال لصفحة تسجيل الدخول: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }



  /// Sync plan preferences and notification settings to Firestore with snake_case field names
  Future<void> _syncDataToFirestore(
    Map<String, dynamic>? planPreferencesData,
    Map<String, dynamic>? notificationSettingsData,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Sync plan preferences to users/{userId}/preferences path
      if (planPreferencesData != null && planPreferencesData.isNotEmpty) {
        AppLogger.info('Syncing plan preferences to Firestore with snake_case fields');

        // Convert to snake_case field names
        final snakeCasePreferences = _convertToSnakeCase(planPreferencesData);

        await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .set({
          'plan_preferences': {
            ...snakeCasePreferences,
            'updated_at': FieldValue.serverTimestamp(),
          },
          'updated_at': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        AppLogger.info('Plan preferences synced successfully');
      } else {
        AppLogger.warning('No plan preferences data found to sync');
      }

      // Notification settings are now handled separately in user_profile feature

      AppLogger.info('Data synchronization to Firestore completed');
    } catch (e) {
      AppLogger.error('Error syncing data to Firestore: $e');

      // Show warning but don't stop the process
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تحذير: فشل في حفظ بعض الإعدادات: ${e.toString()}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      // Don't rethrow - allow meal generation to continue
    }
  }

  /// Convert camelCase keys to snake_case
  Map<String, dynamic> _convertToSnakeCase(Map<String, dynamic> data) {
    final Map<String, dynamic> result = {};

    for (final entry in data.entries) {
      final snakeKey = _camelToSnakeCase(entry.key);
      if (entry.value is Map<String, dynamic>) {
        result[snakeKey] = _convertToSnakeCase(entry.value as Map<String, dynamic>);
      } else if (entry.value is List) {
        result[snakeKey] = entry.value;
      } else {
        result[snakeKey] = entry.value;
      }
    }

    return result;
  }

  /// Convert camelCase string to snake_case
  String _camelToSnakeCase(String camelCase) {
    return camelCase.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isLoading = _isGeneratingMealPlan || _isWaitingForMealData;

    return PopScope(
      canPop: !isLoading,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && isLoading) {
          _showExitConfirmationDialog();
        }
      },
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Main content based on state
              if (isLoading) ...[
                _buildLoadingState(),
              ] else if (_generationError != null) ...[
                _buildErrorState(),
              ] else ...[
                _buildSuccessState(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build loading state UI with enhanced animations
  Widget _buildLoadingState() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated meal generation icon
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 32),

          // Loading title
          Text(
            _isGeneratingMealPlan ? 'جاري إعداد خطتك الغذائية' : 'جاري إنشاء الوجبات',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Status message
          if (_currentStatusMessage.isNotEmpty) ...[
            Text(
              _currentStatusMessage,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
          ],

          // Progress indicator
          SizedBox(
            width: 40,
            height: 40,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),

          const SizedBox(height: 16),

          // Encouraging message
          Text(
            'يرجى عدم إغلاق التطبيق أثناء هذه العملية',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build error state UI with authentication-aware handling
  Widget _buildErrorState() {
    // Check if user is authenticated
    final isAuthenticated = FirebaseAuth.instance.currentUser != null;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon with animation
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.errorContainer,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.error,
                width: 2,
              ),
            ),
            child: Icon(
              isAuthenticated ? Icons.error_outline : Icons.login,
              size: 60,
              color: Theme.of(context).colorScheme.error,
            ),
          ),

          const SizedBox(height: 32),

          // Error title - different for authenticated vs unauthenticated users
          Text(
            isAuthenticated
                ? 'حدث خطأ في إنشاء خطة الوجبات'
                : 'تسجيل الدخول مطلوب',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Error message - different for authenticated vs unauthenticated users
          Text(
            isAuthenticated
                ? 'حدث خطأ أثناء إنشاء خطة الوجبات. يرجى المحاولة مرة أخرى.'
                : 'يجب تسجيل الدخول إلى حسابك أولاً لإنشاء خطة الوجبات المخصصة لك.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Action button - different for authenticated vs unauthenticated users
          if (isAuthenticated) ...[
            // Retry button for authenticated users
            CustomButton(
              text: 'إعادة المحاولة',
              onPressed: _retryOnboardingCompletion,
              icon: const Icon(Icons.refresh, color: Colors.white),
              backgroundColor: AppColors.primary,
            ),
          ] else ...[
            // Login button for unauthenticated users
            CustomButton(
              text: 'تسجيل الدخول',
              onPressed: _handleLoginFromErrorState,
              icon: const Icon(Icons.login, color: Colors.white),
              backgroundColor: AppColors.primary,
            ),
          ],
        ],
      ),
    );
  }

  /// Build success state UI
  Widget _buildSuccessState() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success animation or icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Icon(
              Icons.check_circle,
              size: 60,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 32),

          // Success title
          Text(
            'تم الإعداد بنجاح!',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Success message
          Text(
            'أصبح كل شيء جاهزاً! يمكنك الآن البدء في استخدام التطبيق',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Settings info message
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.primaryWithOpacity,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.settings,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'يمكنك تعديل جميع هذه الإعدادات لاحقاً من صفحة الملف الشخصي في الإعدادات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Meal plan status
          if (_currentStatusMessage.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.restaurant_menu,
                    color: Colors.green,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _currentStatusMessage,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Show exit confirmation dialog
  void _showExitConfirmationDialog() {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الخروج'),
          content: const Text(
            'جاري إنشاء خطة الوجبات الخاصة بك. إذا خرجت الآن، ستحتاج إلى إعادة بدء العملية.\n\nهل تريد الخروج فعلاً؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('البقاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Cancel ongoing operations
                _pulseAnimationController.stop();
                _mealsSubscription?.cancel();
                // Force exit
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error,
              ),
              child: const Text('الخروج'),
            ),
          ],
        );
      },
    );
  }

  /// Add initial weight tracking entry from onboarding data
  Future<void> _addInitialWeightEntry(Map<String, dynamic>? planPreferencesData) async {
    try {
      if (planPreferencesData == null || planPreferencesData.isEmpty) {
        AppLogger.info('No plan preferences data available for weight entry');
        return;
      }

      final weight = planPreferencesData['weight'] as double?;
      if (weight == null) {
        AppLogger.info('No weight data found in plan preferences');
        return;
      }

      AppLogger.info('Adding initial weight tracking entry: ${weight}kg');

      // Add weight entry using the weight tracking provider
      await ref.read(weightTrackingNotifierProvider.notifier).addWeightEntry(
        weight: weight,
        date: DateTime.now(),
        notes: 'الوزن الإبتدائي من الخطة',
      );

      AppLogger.info('Initial weight tracking entry added successfully');
    } catch (e) {
      AppLogger.warning('Failed to add initial weight tracking entry: $e');
      // Don't throw error as this shouldn't stop the onboarding process

      // Show warning but don't stop the process
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تحذير: فشل في حفظ الوزن الأولي: ${e.toString()}'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
