import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../user_profile/data/models/user_profile.dart';
import '../../providers/onboarding_provider.dart';

class PersonalInfoStep extends ConsumerStatefulWidget {
  const PersonalInfoStep({super.key});

  @override
  ConsumerState<PersonalInfoStep> createState() => _PersonalInfoStepState();
}

class _PersonalInfoStepState extends ConsumerState<PersonalInfoStep> {
  final _formKey = GlobalKey<FormState>();
  int? _selectedDay;
  int? _selectedMonth;
  int? _selectedYear;
  Gender _selectedGender = Gender.notSpecified;

  // Arabic month names
  final List<String> _arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  @override
  void initState() {
    super.initState();
    // Initialize with default values, will be updated by listener
    _selectedGender = Gender.notSpecified;

    // Use post frame callback to safely read from provider after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final data = ref.read(onboardingNotifierProvider).data;
          setState(() {
            if (data.dateOfBirth != null) {
              _selectedDay = data.dateOfBirth!.day;
              _selectedMonth = data.dateOfBirth!.month;
              _selectedYear = data.dateOfBirth!.year;
            }
            _selectedGender = data.gender;
          });
        } catch (e) {
          print('Error initializing PersonalInfoStep: $e'); // Debug log
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);

    // Listen for state changes and update form controllers
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.data != next.data) {
        // Update local state variables for date
        if (next.data.dateOfBirth != null) {
          final newDay = next.data.dateOfBirth!.day;
          final newMonth = next.data.dateOfBirth!.month;
          final newYear = next.data.dateOfBirth!.year;

          if (_selectedDay != newDay || _selectedMonth != newMonth || _selectedYear != newYear) {
            setState(() {
              _selectedDay = newDay;
              _selectedMonth = newMonth;
              _selectedYear = newYear;
            });
          }
        } else if (_selectedDay != null || _selectedMonth != null || _selectedYear != null) {
          setState(() {
            _selectedDay = null;
            _selectedMonth = null;
            _selectedYear = null;
          });
        }

        // Update gender
        if (_selectedGender != next.data.gender) {
          setState(() {
            _selectedGender = next.data.gender;
          });
        }
      }
    });

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'المعلومات الشخصية',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'أخبرنا عن نفسك لنتمكن من تخصيص تجربتك',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 32),

            // Date of Birth
            Text(
              'تاريخ الميلاد',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                // Day Dropdown
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedDay,
                    decoration: InputDecoration(
                      labelText: 'اليوم',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: List.generate(31, (index) => index + 1)
                        .map((day) => DropdownMenuItem(
                              value: day,
                              child: Text(day.toString()),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedDay = value;
                      });
                      _updateDateOfBirth();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                // Month Dropdown
                Expanded(
                  flex: 2,
                  child: DropdownButtonFormField<int>(
                    value: _selectedMonth,
                    decoration: InputDecoration(
                      labelText: 'الشهر',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: List.generate(12, (index) => index + 1)
                        .map((month) => DropdownMenuItem(
                              value: month,
                              child: Text(_arabicMonths[month - 1]),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedMonth = value;
                      });
                      _updateDateOfBirth();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                // Year Dropdown
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedYear,
                    decoration: InputDecoration(
                      labelText: 'السنة',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: List.generate(61, (index) => 2010 - index)
                        .map((year) => DropdownMenuItem(
                              value: year,
                              child: Text(year.toString()),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedYear = value;
                      });
                      _updateDateOfBirth();
                    },
                  ),
                ),
              ],
            ),
            if (onboardingState.errors.containsKey('personal_dateOfBirth'))
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  onboardingState.errors['personal_dateOfBirth']!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ),
            const SizedBox(height: 24),

            // Gender Selection
            Text(
              'الجنس',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildGenderOption(
                    context,
                    Gender.male,
                    'ذكر',
                    Icons.male,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildGenderOption(
                    context,
                    Gender.female,
                    'أنثى',
                    Icons.female,
                  ),
                ),
              ],
            ),
            if (onboardingState.errors.containsKey('personal_gender'))
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  onboardingState.errors['personal_gender']!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderOption(
    BuildContext context,
    Gender gender,
    String label,
    IconData icon,
  ) {
    final isSelected = _selectedGender == gender;
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);

    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = gender;
        });
        onboardingNotifier.updatePersonalInfo(gender: gender);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateDateOfBirth() {
    if (_selectedDay != null && _selectedMonth != null && _selectedYear != null) {
      try {
        final dateOfBirth = DateTime(_selectedYear!, _selectedMonth!, _selectedDay!);
        ref.read(onboardingNotifierProvider.notifier)
            .updatePersonalInfo(dateOfBirth: dateOfBirth);
      } catch (e) {
        // Invalid date combination, ignore
      }
    }
  }
}
