import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../user_profile/data/models/user_profile.dart';
import '../../providers/onboarding_provider.dart';
import 'diet_type_selector.dart';


class GoalsStep extends ConsumerStatefulWidget {
  const GoalsStep({super.key});

  @override
  ConsumerState<GoalsStep> createState() => _GoalsStepState();
}

class _GoalsStepState extends ConsumerState<GoalsStep> {
  double _selectedTargetWeight = 70.0; // Default target weight in kg
  double _selectedCalorieGoal = 2000.0; // Default calorie goal
  int? _recommendedCalories; // Calculated recommendation
  bool _isUserInteractingWithSlider = false; // Flag to prevent auto-sync during user interaction
  Timer? _sliderDebounceTimer; // Debounce timer for slider changes

  @override
  void initState() {
    super.initState();
    // Initialize with default values, will be updated by listener
    _selectedTargetWeight = 70.0;
    _selectedCalorieGoal = 2000.0;

    // Use post frame callback to safely read from provider after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final data = ref.read(onboardingNotifierProvider).data;
          setState(() {
            _selectedTargetWeight = data.targetWeight ?? 70.0;
            _selectedCalorieGoal = data.dailyCalorieGoal?.toDouble() ?? 2000.0;
          });

          // Calculate recommended calories
          _calculateRecommendedCalories();
        } catch (e) {
          print('Error initializing GoalsStep: $e'); // Debug log
        }
      }
    });
  }

  @override
  void dispose() {
    _sliderDebounceTimer?.cancel();
    super.dispose();
  }

  void _calculateRecommendedCalories() {
    final data = ref.read(onboardingNotifierProvider).data;
    _recommendedCalories = _calculateDailyCalorieNeeds(data);

    // Set recommended calories as default if no goal is set
    if (data.dailyCalorieGoal == null && _recommendedCalories != null) {
      _selectedCalorieGoal = _recommendedCalories!.toDouble();

      // Auto-select the recommended option if no selection exists
      if (data.selectedCalorieRecommendation == null) {
        final recommendedKey = _getRecommendedOptionKey(data.healthGoal);
        // Use Future.microtask to avoid modifying provider during build
        Future.microtask(() {
          ref.read(onboardingNotifierProvider.notifier).updateGoals(
            dailyCalorieGoal: _recommendedCalories,
            selectedCalorieRecommendation: recommendedKey,
          );
        });
      }
    }

    // Ensure the selected calorie goal is within the slider range
    _selectedCalorieGoal = _selectedCalorieGoal.clamp(_getSliderMin().toDouble(), _getSliderMax().toDouble());
  }

  String _getRecommendedOptionKey(HealthGoal healthGoal) {
    switch (healthGoal) {
      case HealthGoal.loseWeight:
        return 'lose_moderate';
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        return 'gain_moderate';
      default:
        return 'maintain_balanced';
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(onboardingNotifierProvider);
    final data = state.data;

    // Listen for state changes and update local state
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.data != next.data) {
        // Update local state variables
        if (next.data.targetWeight != null && _selectedTargetWeight != next.data.targetWeight) {
          setState(() {
            _selectedTargetWeight = next.data.targetWeight!;
          });
        }
        if (next.data.dailyCalorieGoal != null && _selectedCalorieGoal != next.data.dailyCalorieGoal!.toDouble()) {
          setState(() {
            // Clamp the value to ensure it's within slider range
            _selectedCalorieGoal = next.data.dailyCalorieGoal!.toDouble()
                .clamp(_getSliderMin().toDouble(), _getSliderMax().toDouble());
          });

          // Auto-sync recommendation selection when calorie goal changes externally
          // Only if user is not actively interacting with the slider
          if (!_isUserInteractingWithSlider) {
            _autoSyncRecommendationSelection(next.data.dailyCalorieGoal!);
          }
        }

        // Recalculate recommendations if relevant data changed
        if (previous?.data.height != next.data.height ||
            previous?.data.weight != next.data.weight ||
            previous?.data.dateOfBirth != next.data.dateOfBirth ||
            previous?.data.gender != next.data.gender ||
            previous?.data.activityLevel != next.data.activityLevel ||
            previous?.data.healthGoal != next.data.healthGoal) {
          _calculateRecommendedCalories();
        }
      }
    });

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أهدافك الصحية',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ما هو هدفك الرئيسي؟',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 32),

          // Health Goal Selection
          _buildHealthGoalSection(data.healthGoal),

          const SizedBox(height: 32),

          // Target Weight (conditional)
          if (data.healthGoal == HealthGoal.loseWeight || data.healthGoal == HealthGoal.gainWeight)
            _buildTargetWeightSection(data),

          const SizedBox(height: 32),

          // Daily Calorie Goal
          _buildCalorieGoalSection(data),

          const SizedBox(height: 32),

          // Diet Type Selection
          const DietTypeSelector(),

          const SizedBox(height: 32),

          // Goal Timeline
          _buildTimelineSection(),
        ],
      ),
    );
  }

  Widget _buildHealthGoalSection(HealthGoal selectedGoal) {
    final goals = [
      {
        'goal': HealthGoal.loseWeight,
        'title': 'فقدان الوزن',
        'subtitle': 'أريد أن أفقد وزناً',
        'icon': Icons.trending_down,
        'color': Colors.red,
      },
      {
        'goal': HealthGoal.gainWeight,
        'title': 'زيادة الوزن',
        'subtitle': 'أريد أن أزيد وزناً',
        'icon': Icons.trending_up,
        'color': Colors.blue,
      },
      {
        'goal': HealthGoal.maintain,
        'title': 'الحفاظ على الوزن',
        'subtitle': 'أريد أن أحافظ على وزني الحالي',
        'icon': Icons.balance,
        'color': Colors.green,
      },
      {
        'goal': HealthGoal.buildMuscle,
        'title': 'بناء العضلات',
        'subtitle': 'أريد أن أبني كتلة عضلية',
        'icon': Icons.fitness_center,
        'color': Colors.orange,
      },
      {
        'goal': HealthGoal.improveHealth,
        'title': 'تحسين الصحة',
        'subtitle': 'أريد أن أحسن صحتي العامة',
        'icon': Icons.favorite,
        'color': Colors.pink,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'هدفك الصحي',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...goals.map((goalData) {
          final goal = goalData['goal'] as HealthGoal;
          final isSelected = selectedGoal == goal;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: GestureDetector(
              onTap: () => _updateHealthGoal(goal),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (goalData['color'] as Color).withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? (goalData['color'] as Color)
                        : Colors.grey.shade300,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: (goalData['color'] as Color).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        goalData['icon'] as IconData,
                        color: goalData['color'] as Color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            goalData['title'] as String,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isSelected
                                  ? (goalData['color'] as Color)
                                  : null,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            goalData['subtitle'] as String,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: goalData['color'] as Color,
                      ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildTargetWeightSection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوزن المستهدف',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'كم تريد أن يكون وزنك؟',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        // Target Weight Picker Card
        _buildPickerCard(
          title: 'الوزن المستهدف',
          value: _selectedTargetWeight.toInt(),
          unit: 'كغ',
          icon: Icons.monitor_weight,
          onTap: () => _showTargetWeightPicker(context),
        ),

        if (data.weight != null && data.targetWeight != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getWeightDifferenceText(data.weight!, data.targetWeight!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCalorieGoalSection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'هدف السعرات الحرارية اليومية',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'كم سعرة حرارية تريد أن تستهلك يومياً؟',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        // Calorie Goal Slider
        _buildCalorieSlider(),

        const SizedBox(height: 16),

        // Smart calorie recommendations
        if (_recommendedCalories != null) _buildCalorieRecommendations(data),
      ],
    );
  }

  Widget _buildTimelineSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الجدول الزمني',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'نصائح لتحقيق أهدافك',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.lightbulb, color: Colors.green.shade600),
                  const SizedBox(width: 8),
                  Text(
                    'نصائح مهمة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildTip('فقدان الوزن الصحي: 0.5-1 كيلو في الأسبوع'),
              _buildTip('زيادة الوزن الصحية: 0.25-0.5 كيلو في الأسبوع'),
              _buildTip('اشرب 8-10 أكواب من الماء يومياً'),
              _buildTip('مارس الرياضة 150 دقيقة في الأسبوع'),
              _buildTip('احصل على 7-9 ساعات نوم يومياً'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6, left: 8),
            decoration: BoxDecoration(
              color: Colors.green.shade600,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.green.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  void _updateHealthGoal(HealthGoal goal) {
    ref.read(onboardingNotifierProvider.notifier).updateGoals(healthGoal: goal);
    // Recalculate recommendations when goal changes
    _calculateRecommendedCalories();
  }

  void _selectCalorieRecommendation(String recommendationKey, int calories) {
    setState(() {
      _selectedCalorieGoal = calories.toDouble();
    });
    ref.read(onboardingNotifierProvider.notifier).updateGoals(
      dailyCalorieGoal: calories,
      selectedCalorieRecommendation: recommendationKey,
    );
  }



  /// Update calorie goal and sync with recommendations
  void _updateCalorieGoalWithSync(int calories) {
    setState(() {
      _selectedCalorieGoal = calories.toDouble();
    });

    // Update provider with calorie goal and auto-sync recommendation
    ref.read(onboardingNotifierProvider.notifier).updateGoals(
      dailyCalorieGoal: calories,
      autoSyncRecommendation: true,
    );
  }

  /// Debounced sync for slider changes to prevent excessive updates
  void _debouncedSyncRecommendationSelection(int calories) {
    _sliderDebounceTimer?.cancel();
    _sliderDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      if (_isUserInteractingWithSlider) {
        // Only sync during active interaction, not after onChangeEnd
        _syncRecommendationSelection(calories);
      }
    });
  }

  /// Sync recommendation selection during real-time slider changes (for visual feedback)
  void _syncRecommendationSelection(int calories) {
    // Use auto-sync to update recommendation selection based on calorie value
    ref.read(onboardingNotifierProvider.notifier).updateGoals(
      dailyCalorieGoal: calories,
      autoSyncRecommendation: true,
    );
  }

  /// Auto-sync recommendation selection when calorie goal changes externally
  void _autoSyncRecommendationSelection(int calories) {
    // Additional safety check to prevent cascading updates
    if (_isUserInteractingWithSlider) {
      return; // Don't auto-sync during user interaction
    }

    // Use Future.microtask to avoid modifying provider during listener callback
    Future.microtask(() {
      // Double-check the flag hasn't changed
      if (!_isUserInteractingWithSlider) {
        ref.read(onboardingNotifierProvider.notifier).updateGoals(
          dailyCalorieGoal: calories,
          autoSyncRecommendation: true,
        );
      }
    });
  }

  /// Get dynamic slider minimum value
  int _getSliderMin() {
    return 1000; // Minimum safe calorie intake
  }

  /// Get dynamic slider maximum value based on user profile
  int _getSliderMax() {
    final data = ref.read(onboardingNotifierProvider).data;

    // If we have enough data to calculate BMR, use a dynamic range
    if (data.height != null && data.weight != null && data.dateOfBirth != null) {
      final baseCalories = _recommendedCalories ?? 2000;
      // Set max to 150% of calculated needs, with a minimum of 4000
      return ((baseCalories * 1.5).round()).clamp(4000, 6000);
    }

    // Default range for incomplete profiles
    return 4000;
  }

  /// Get number of divisions for the slider
  int _getSliderDivisions() {
    final range = _getSliderMax() - _getSliderMin();
    // Aim for 50-calorie increments
    return (range / 50).round();
  }

  String _getWeightDifferenceText(double currentWeight, double targetWeight) {
    final difference = (targetWeight - currentWeight).abs();
    final isLosing = targetWeight < currentWeight;

    if (isLosing) {
      return 'تحتاج إلى فقدان ${difference.toStringAsFixed(1)} كيلو';
    } else {
      return 'تحتاج إلى زيادة ${difference.toStringAsFixed(1)} كيلو';
    }
  }

  // Build picker card widget (similar to physical_info_step.dart)
  Widget _buildPickerCard({
    required String title,
    required int value,
    required String unit,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Icon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  value.toString(),
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  unit,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 3,
              width: 30,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show target weight picker
  void _showTargetWeightPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                    ),
                  ),
                  Text(
                    'اختر الوزن المستهدف',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      ref.read(onboardingNotifierProvider.notifier)
                          .updateGoals(targetWeight: _selectedTargetWeight);
                      Navigator.pop(context);
                    },
                    child: Text(
                      'تم',
                      style: TextStyle(color: Theme.of(context).colorScheme.primary),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoPicker(
                itemExtent: 50,
                scrollController: FixedExtentScrollController(
                  initialItem: (_selectedTargetWeight - 30).toInt(),
                ),
                onSelectedItemChanged: (index) {
                  setState(() {
                    _selectedTargetWeight = (30 + index).toDouble();
                  });
                },
                children: List.generate(
                  171, // 30kg to 200kg
                  (index) => Center(
                    child: Text(
                      '${30 + index} كغ',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build calorie slider
  Widget _buildCalorieSlider() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'السعرات الحرارية اليومية',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(
                Icons.local_fire_department,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Center(
            child: Column(
              children: [
                Text(
                  '${_selectedCalorieGoal.toInt()}',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  'سعرة حرارية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
            ),
            child: Slider(
              value: _selectedCalorieGoal.clamp(_getSliderMin().toDouble(), _getSliderMax().toDouble()),
              min: _getSliderMin().toDouble(),
              max: _getSliderMax().toDouble(),
              divisions: _getSliderDivisions(),
              onChanged: (value) {
                setState(() {
                  _selectedCalorieGoal = value;
                  _isUserInteractingWithSlider = true; // Mark as user interaction
                });
                // Debounce the sync to prevent excessive updates
                _debouncedSyncRecommendationSelection(value.toInt());
              },
              onChangeEnd: (value) {
                setState(() {
                  _isUserInteractingWithSlider = false; // Clear interaction flag
                });
                // Use the new sync method to update both calorie goal and recommendation selection
                _updateCalorieGoalWithSync(value.toInt());
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '1000',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              Text(
                '3500',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build calorie recommendations widget
  Widget _buildCalorieRecommendations(OnboardingData data) {
    final recommendations = _getCalorieRecommendations(data);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التوصيات الذكية:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...recommendations.map((rec) {
          final isRecommended = rec['isRecommended'] as bool;
          final calories = rec['calories'] as int;
          final label = rec['label'] as String;
          final description = rec['description'] as String;
          final recommendationKey = rec['key'] as String;

          // Check if this recommendation is currently selected
          final isSelected = data.selectedCalorieRecommendation == recommendationKey;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: GestureDetector(
              onTap: () => _selectCalorieRecommendation(recommendationKey, calories),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '$calories سعرة',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isSelected
                              ? Colors.white
                              : Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                label,
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : null,
                                ),
                              ),
                              if (isRecommended) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    'موصى به',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            description,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  // Calculate daily calorie needs using Mifflin-St Jeor equation
  int? _calculateDailyCalorieNeeds(OnboardingData data) {
    if (data.height == null ||
        data.weight == null ||
        data.dateOfBirth == null ||
        data.gender == Gender.notSpecified) {
      return null;
    }

    final age = DateTime.now().year - data.dateOfBirth!.year;
    final height = data.height!;
    final weight = data.weight!;
    final isMale = data.gender == Gender.male;

    // Mifflin-St Jeor equation
    double bmr;
    if (isMale) {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) + 5;
    } else {
      bmr = (10 * weight) + (6.25 * height) - (5 * age) - 161;
    }

    // Activity level multiplier
    double activityMultiplier;
    switch (data.activityLevel) {
      case ActivityLevel.sedentary:
        activityMultiplier = 1.2;
        break;
      case ActivityLevel.light:
        activityMultiplier = 1.375;
        break;
      case ActivityLevel.moderate:
        activityMultiplier = 1.55;
        break;
      case ActivityLevel.active:
        activityMultiplier = 1.725;
        break;
      case ActivityLevel.veryActive:
        activityMultiplier = 1.9;
        break;
    }

    final maintenanceCalories = (bmr * activityMultiplier).round();

    // Adjust based on health goal
    switch (data.healthGoal) {
      case HealthGoal.loseWeight:
        return (maintenanceCalories * 0.8).round(); // 20% deficit
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        return (maintenanceCalories * 1.15).round(); // 15% surplus
      case HealthGoal.maintain:
      case HealthGoal.improveHealth:
      default:
        return maintenanceCalories;
    }
  }

  // Get calorie recommendations with different approaches
  List<Map<String, dynamic>> _getCalorieRecommendations(OnboardingData data) {
    if (_recommendedCalories == null) return [];

    final recommendations = <Map<String, dynamic>>[];
    final baseCalories = _recommendedCalories!;

    switch (data.healthGoal) {
      case HealthGoal.loseWeight:
        recommendations.addAll([
          {
            'key': 'lose_fast',
            'calories': (baseCalories * 0.7).round(),
            'label': 'فقدان سريع',
            'description': 'فقدان 0.7-1 كيلو أسبوعياً (قاسي)',
            'isRecommended': false,
          },
          {
            'key': 'lose_moderate',
            'calories': baseCalories,
            'label': 'فقدان متوسط',
            'description': 'فقدان 0.5 كيلو أسبوعياً (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'lose_slow',
            'calories': (baseCalories * 1.1).round(),
            'label': 'فقدان بطيء',
            'description': 'فقدان 0.25 كيلو أسبوعياً (مستدام)',
            'isRecommended': false,
          },
        ]);
        break;
      case HealthGoal.gainWeight:
      case HealthGoal.buildMuscle:
        recommendations.addAll([
          {
            'key': 'gain_slow',
            'calories': (baseCalories * 0.9).round(),
            'label': 'زيادة بطيئة',
            'description': 'زيادة 0.25 كيلو أسبوعياً (نظيف)',
            'isRecommended': false,
          },
          {
            'key': 'gain_moderate',
            'calories': baseCalories,
            'label': 'زيادة متوسطة',
            'description': 'زيادة 0.5 كيلو أسبوعياً (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'gain_fast',
            'calories': (baseCalories * 1.2).round(),
            'label': 'زيادة سريعة',
            'description': 'زيادة 0.7-1 كيلو أسبوعياً (قوي)',
            'isRecommended': false,
          },
        ]);
        break;
      default:
        recommendations.addAll([
          {
            'key': 'maintain_conservative',
            'calories': (baseCalories * 0.9).round(),
            'label': 'محافظ',
            'description': 'للحفاظ على الوزن مع هامش أمان',
            'isRecommended': false,
          },
          {
            'key': 'maintain_balanced',
            'calories': baseCalories,
            'label': 'متوازن',
            'description': 'للحفاظ على الوزن الحالي (موصى به)',
            'isRecommended': true,
          },
          {
            'key': 'maintain_active',
            'calories': (baseCalories * 1.1).round(),
            'label': 'نشط',
            'description': 'للأشخاص الأكثر نشاطاً',
            'isRecommended': false,
          },
        ]);
    }

    return recommendations;
  }
}
