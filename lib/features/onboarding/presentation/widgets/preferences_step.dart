import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/onboarding_provider.dart';

class PreferencesStep extends ConsumerStatefulWidget {
  const PreferencesStep({super.key});

  @override
  ConsumerState<PreferencesStep> createState() => _PreferencesStepState();
}

class _PreferencesStepState extends ConsumerState<PreferencesStep> {
  final _customIngredientController = TextEditingController();
  final _customDislikedController = TextEditingController();

  // Predefined options
  final List<String> _popularIngredients = [
    'الدجاج',
    'اللحم البقري',
    'السمك',
    'البيض',
    'الأرز',
    'المعكرونة',
    'البطاطس',
    'الطماطم',
    'البصل',
    'الثوم',
    'الجزر',
    'الخيار',
    'الخس',
    'السبانخ',
    'الجبن',
    'اللبن',
    'زيت الزيتون',
    'الليمون',
  ];

  final List<String> _cuisineTypes = [
    'عربي',
    'إيطالي',
    'آسيوي',
    'مكسيكي',
    'هندي',
    'تركي',
    'فرنسي',
    'يوناني',
    'تايلاندي',
    'يابانيّ',
  ];

  @override
  void dispose() {
    _customIngredientController.dispose();
    _customDislikedController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(onboardingNotifierProvider);
    final data = state.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التفضيلات الغذائية',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أخبرنا عن تفضيلاتك في الطعام',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),

          // Meal Frequency Card
          _buildCard(
            child: _buildMealFrequencySection(data),
          ),

          const SizedBox(height: 16),

          // Favorite Ingredients Card
          _buildCard(
            child: _buildFavoriteIngredientsSection(data),
          ),

          const SizedBox(height: 16),

          // Disliked Ingredients Card
          _buildCard(
            child: _buildDislikedIngredientsSection(data),
          ),

          const SizedBox(height: 16),

          // Favorite Cuisines Card
          _buildCard(
            child: _buildFavoriteCuisinesSection(data),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildMealFrequencySection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.restaurant, color: Colors.blue, size: 24),
            const SizedBox(width: 12),
            Text(
              'تكرار الوجبات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'حدد عدد الوجبات والوجبات الخفيفة في اليوم',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 20),

        // Meals per day
        _buildMealsPerDaySection(data),

        const SizedBox(height: 24),

        // Snacks per day
        _buildSnacksPerDaySection(data),
      ],
    );
  }

  Widget _buildMealsPerDaySection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.restaurant, color: Colors.blue, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'عدد الوجبات: ${data.mealsPerDay}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Colors.blue,
            thumbColor: Colors.blue,
            overlayColor: Colors.blue.withOpacity(0.2),
          ),
          child: Slider(
            value: data.mealsPerDay.toDouble(),
            min: 1,
            max: 6,
            divisions: 5,
            onChanged: (value) {
              ref.read(onboardingNotifierProvider.notifier)
                  .updatePreferences(mealsPerDay: value.toInt());
            },
          ),
        ),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('وجبة واحدة', style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            )),
            Text('6 وجبات', style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildSnacksPerDaySection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.cookie, color: Colors.orange, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'عدد الوجبات الخفيفة: ${data.snacksPerDay}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Colors.orange,
            thumbColor: Colors.orange,
            overlayColor: Colors.orange.withOpacity(0.2),
          ),
          child: Slider(
            value: data.snacksPerDay.toDouble(),
            min: 0,
            max: 4,
            divisions: 4,
            onChanged: (value) {
              ref.read(onboardingNotifierProvider.notifier)
                  .updatePreferences(snacksPerDay: value.toInt());
            },
          ),
        ),

        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('بدون وجبات خفيفة', style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            )),
            Text('4 وجبات خفيفة', style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            )),
          ],
        ),
      ],
    );
  }

  Widget _buildFavoriteIngredientsSection(OnboardingData data) {
    return _buildIngredientSection(
      title: 'المكونات المفضلة',
      description: 'اختر المكونات التي تحب تناولها',
      icon: Icons.favorite,
      color: Colors.green,
      items: _popularIngredients,
      selectedItems: data.favoriteIngredients,
      onItemToggle: _toggleFavoriteIngredient,
      controller: _customIngredientController,
      onCustomAdd: _addCustomFavoriteIngredient,
      hintText: 'أضف مكون مفضل...',
    );
  }

  Widget _buildDislikedIngredientsSection(OnboardingData data) {
    return _buildIngredientSection(
      title: 'المكونات غير المرغوبة',
      description: 'اختر المكونات التي تريد تجنبها',
      icon: Icons.block,
      color: Colors.red,
      items: _popularIngredients,
      selectedItems: data.dislikedIngredients,
      onItemToggle: _toggleDislikedIngredient,
      controller: _customDislikedController,
      onCustomAdd: _addCustomDislikedIngredient,
      hintText: 'أضف مكون غير مرغوب...',
    );
  }

  Widget _buildFavoriteCuisinesSection(OnboardingData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.public, color: Colors.purple, size: 24),
            const SizedBox(width: 12),
            Text(
              'المطابخ المفضلة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'اختر أنواع المطابخ التي تفضلها',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _cuisineTypes.map((cuisine) {
            final isSelected = data.favoriteCuisines.contains(cuisine);
            return FilterChip(
              label: Text(cuisine),
              selected: isSelected,
              onSelected: (selected) => _toggleFavoriteCuisine(cuisine),
              selectedColor: Colors.purple.withOpacity(0.2),
              checkmarkColor: Colors.purple,
              side: BorderSide(color: isSelected ? Colors.purple : Colors.grey.shade300),
            );
          }).toList(),
        ),

        if (data.favoriteCuisines.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.purple.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.purple),
                    const SizedBox(width: 6),
                    Text(
                      'المطابخ المحددة (${data.favoriteCuisines.length})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.purple,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: data.favoriteCuisines.map((cuisine) {
                    return Chip(
                      label: Text(
                        cuisine,
                        style: const TextStyle(fontSize: 12),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () => _toggleFavoriteCuisine(cuisine),
                      backgroundColor: Colors.purple.withOpacity(0.1),
                      side: BorderSide.none,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildIngredientSection({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> items,
    required List<String> selectedItems,
    required Function(String) onItemToggle,
    required TextEditingController controller,
    required VoidCallback onCustomAdd,
    required String hintText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),

        // Predefined items
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            final isSelected = selectedItems.contains(item);
            return FilterChip(
              label: Text(item),
              selected: isSelected,
              onSelected: (selected) => onItemToggle(item),
              selectedColor: color.withOpacity(0.2),
              checkmarkColor: color,
              side: BorderSide(color: isSelected ? color : Colors.grey.shade300),
            );
          }).toList(),
        ),

        const SizedBox(height: 16),

        // Custom input
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hintText,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onSubmitted: (_) => onCustomAdd(),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: onCustomAdd,
              icon: Icon(Icons.add, color: color),
              style: IconButton.styleFrom(
                backgroundColor: color.withOpacity(0.1),
              ),
            ),
          ],
        ),

        // Selected items display
        if (selectedItems.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, size: 16, color: color),
                    const SizedBox(width: 6),
                    Text(
                      'المحدد (${selectedItems.length})',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: selectedItems.map((item) {
                    return Chip(
                      label: Text(
                        item,
                        style: const TextStyle(fontSize: 12),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () => onItemToggle(item),
                      backgroundColor: color.withOpacity(0.1),
                      side: BorderSide.none,
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // Helper methods
  void _toggleFavoriteIngredient(String ingredient) {
    final currentIngredients = List<String>.from(ref.read(onboardingNotifierProvider).data.favoriteIngredients);
    if (currentIngredients.contains(ingredient)) {
      currentIngredients.remove(ingredient);
    } else {
      currentIngredients.add(ingredient);
    }
    ref.read(onboardingNotifierProvider.notifier).updatePreferences(favoriteIngredients: currentIngredients);
  }

  void _addCustomFavoriteIngredient() {
    final text = _customIngredientController.text.trim();
    if (text.isNotEmpty) {
      final currentIngredients = List<String>.from(ref.read(onboardingNotifierProvider).data.favoriteIngredients);
      if (!currentIngredients.contains(text)) {
        currentIngredients.add(text);
        ref.read(onboardingNotifierProvider.notifier).updatePreferences(favoriteIngredients: currentIngredients);
      }
      _customIngredientController.clear();
    }
  }

  void _toggleDislikedIngredient(String ingredient) {
    final currentIngredients = List<String>.from(ref.read(onboardingNotifierProvider).data.dislikedIngredients);
    if (currentIngredients.contains(ingredient)) {
      currentIngredients.remove(ingredient);
    } else {
      currentIngredients.add(ingredient);
    }
    ref.read(onboardingNotifierProvider.notifier).updatePreferences(dislikedIngredients: currentIngredients);
  }

  void _addCustomDislikedIngredient() {
    final text = _customDislikedController.text.trim();
    if (text.isNotEmpty) {
      final currentIngredients = List<String>.from(ref.read(onboardingNotifierProvider).data.dislikedIngredients);
      if (!currentIngredients.contains(text)) {
        currentIngredients.add(text);
        ref.read(onboardingNotifierProvider.notifier).updatePreferences(dislikedIngredients: currentIngredients);
      }
      _customDislikedController.clear();
    }
  }

  void _toggleFavoriteCuisine(String cuisine) {
    final currentCuisines = List<String>.from(ref.read(onboardingNotifierProvider).data.favoriteCuisines);
    if (currentCuisines.contains(cuisine)) {
      currentCuisines.remove(cuisine);
    } else {
      currentCuisines.add(cuisine);
    }
    ref.read(onboardingNotifierProvider.notifier).updatePreferences(favoriteCuisines: currentCuisines);
  }
}