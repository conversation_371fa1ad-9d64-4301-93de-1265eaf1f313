import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/router/app_routes.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../core/utils/logger.dart';
import '../../../../generated/l10n/app_localizations.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../providers/onboarding_provider.dart';
import '../widgets/onboarding_step_indicator.dart';

import '../widgets/personal_info_step.dart';
import '../widgets/physical_info_step.dart';
import '../widgets/health_info_step.dart';
import '../widgets/goals_step.dart';
import '../widgets/preferences_step.dart';
import '../widgets/complete_step.dart';

class OnboardingPage extends ConsumerStatefulWidget {
  final int? initialStepIndex;

  const OnboardingPage({super.key, this.initialStepIndex});

  @override
  ConsumerState<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends ConsumerState<OnboardingPage> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();

    // Initialize PageController synchronously to prevent LateInitializationError
    _pageController = PageController(initialPage: 0);

    // Check for saved onboarding step and navigate to correct page asynchronously
    _initializeOnboardingStep();
  }

  /// Initialize onboarding step by checking for saved progress
  Future<void> _initializeOnboardingStep() async {
    int targetPage = 0;

    try {
      final localStorageService =
          await ref.read(localStorageServiceProvider.future);

      // First check for meal generation loading state
      final mealGenerationLoading =
          localStorageService.loadMealGenerationLoading();
      if (mealGenerationLoading != null) {
        final isLoading = mealGenerationLoading['isLoading'] as bool? ?? false;
        final timestamp = mealGenerationLoading['timestamp'] as int? ?? 0;

        if (isLoading && timestamp > 0) {
          AppLogger.info(
              'Onboarding: Found meal generation in progress, navigating to complete step');

          // Navigate directly to complete step (index 6)
          targetPage = OnboardingStep.values.indexOf(OnboardingStep.complete);

          // Set the onboarding provider to complete step
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref
                .read(onboardingNotifierProvider.notifier)
                .goToStep(OnboardingStep.complete);
            AppLogger.info(
                'Onboarding: Navigated to complete step due to meal generation in progress');
          });

          // Skip other checks and proceed to navigation
          _navigateToTargetPage(targetPage);
          return;
        }
      }

      // Check if there's a URL parameter for initial step
      if (widget.initialStepIndex != null &&
          widget.initialStepIndex! >= 0 &&
          widget.initialStepIndex! < OnboardingStep.values.length) {
        targetPage = widget.initialStepIndex!;
        AppLogger.info('Onboarding: Using URL parameter step: $targetPage');

        // Set the onboarding provider to the correct step
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ref
              .read(onboardingNotifierProvider.notifier)
              .goToStep(OnboardingStep.values[widget.initialStepIndex!]);
        });
      } else {
        // Check for saved onboarding step in local storage
        final savedStep = localStorageService.loadCurrentOnboardingStep();

        if (savedStep != null &&
            savedStep >= 0 &&
            savedStep < OnboardingStep.values.length) {
          AppLogger.info(
              'Onboarding: Found saved step in local storage: $savedStep');

          // Use the saved step as target page
          targetPage = savedStep;

          // Clear the saved step immediately to prevent reuse
          await localStorageService.clearCurrentOnboardingStep();
          AppLogger.info('Onboarding: Cleared saved step from local storage');

          // Set the onboarding provider to the saved step
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref
                .read(onboardingNotifierProvider.notifier)
                .goToStep(OnboardingStep.values[savedStep]);
            AppLogger.info(
                'Onboarding: Navigated to saved step: ${OnboardingStep.values[savedStep]}');
          });
        } else {
          // No saved step found, start from current provider state or beginning
          final currentStep = ref.read(onboardingNotifierProvider).currentStep;
          targetPage = OnboardingStep.values.indexOf(currentStep);
          AppLogger.info(
              'Onboarding: No saved step found, starting from: ${currentStep}');
        }
      }
    } catch (e) {
      // Handle errors gracefully - fall back to normal flow
      AppLogger.error('Onboarding: Error checking saved step: $e');
      final currentStep = ref.read(onboardingNotifierProvider).currentStep;
      targetPage = OnboardingStep.values.indexOf(currentStep);
      AppLogger.info(
          'Onboarding: Falling back to current step: ${currentStep}');
    }

    // Navigate to the determined target page
    _navigateToTargetPage(targetPage);
  }

  /// Navigate to target page if different from initial page (0)
  void _navigateToTargetPage(int targetPage) {
    /// Navigate to target page if different from initial page (0)
    if (targetPage != 0 && _pageController.hasClients && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _pageController.hasClients) {
          _pageController.jumpToPage(targetPage);
          AppLogger.info('Onboarding: Jumped to target page: $targetPage');
        }
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
    final l10n = AppLocalizations.of(context);

    // Listen for step changes and completion
    ref.listen(onboardingNotifierProvider, (previous, next) {
      if (previous?.currentStep != next.currentStep) {
        // Sync PageView with state changes
        final newIndex = OnboardingStep.values.indexOf(next.currentStep);
        if (mounted && _pageController.hasClients) {
          _pageController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    // Listen for onboarding completion (when app state changes)
    ref.listen(appStateNotifierProvider, (previous, next) {
      // Check if onboarding was just completed
      if (previous?.isOnboardingCompleted == false &&
          next.isOnboardingCompleted == true) {
        // Navigate to home
        if (context.mounted) {
          context.go(AppRoutes.home);
        }
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: kDebugMode
          ? AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              actions: [
                IconButton(
                  icon: const Icon(Icons.bug_report),
                  tooltip: 'Pre-fill with test data',
                  onPressed: () {
                    onboardingNotifier.preFillWithTestData();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content:
                            Text('Onboarding data pre-filled with test values'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                ),
              ],
            )
          : null,
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            if (onboardingState.currentStep != OnboardingStep.complete)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: OnboardingStepIndicator(
                  currentStep: onboardingState.currentStep,
                  progress: onboardingNotifier.progress,
                ),
              ),

            // Step content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  onboardingNotifier.goToStep(OnboardingStep.values[index]);
                },
                children: [
                  PersonalInfoStep(),
                  PhysicalInfoStep(),
                  HealthInfoStep(),
                  GoalsStep(),
                  PreferencesStep(),
                  CompleteStep(),
                ],
              ),
            ),

            // Navigation buttons
            // if (onboardingState.currentStep != OnboardingStep.complete)
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Back button
                  if (onboardingState.currentStep !=
                      OnboardingStep.personalInfo)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: onboardingNotifier.previousStep,
                        child: Text(l10n.back),
                      ),
                    ),

                  if (onboardingState.currentStep !=
                      OnboardingStep.personalInfo)
                    const SizedBox(width: 16),

                  // Next/Complete button
                  Expanded(
                    flex: onboardingState.currentStep ==
                            OnboardingStep.personalInfo
                        ? 1
                        : 1,
                    child: ElevatedButton(
                      onPressed: onboardingNotifier.canProceed
                          ? () {
                              onboardingNotifier.nextStep();
                            }
                          : null,
                      child: onboardingState.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(
                              onboardingState.currentStep ==
                                      OnboardingStep.preferences
                                  ? l10n.complete
                                  : l10n.next,
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
