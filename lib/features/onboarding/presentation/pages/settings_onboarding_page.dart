import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../generated/l10n/app_localizations.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../providers/onboarding_provider.dart';
import '../widgets/onboarding_step_indicator.dart';
import '../widgets/personal_info_step.dart';
import '../widgets/physical_info_step.dart';
import '../widgets/health_info_step.dart';
import '../widgets/goals_step.dart';
import '../widgets/preferences_step.dart';

import '../widgets/settings_save_step.dart';

class SettingsOnboardingPage extends ConsumerStatefulWidget {
  final OnboardingStep? initialStep;
  final bool isEditMode;

  const SettingsOnboardingPage({
    super.key,
    this.initialStep,
    this.isEditMode = false,
  });

  @override
  ConsumerState<SettingsOnboardingPage> createState() => _SettingsOnboardingPageState();
}

class _SettingsOnboardingPageState extends ConsumerState<SettingsOnboardingPage> {
  PageController? _pageController;

  // Auto-save state management
  bool _isAutoSaving = false;
  Map<String, dynamic>? _lastKnownFirestoreState;
  Map<String, dynamic>? _initialLocalState;

  // Define steps for settings (excluding complete step and notifications)
  static const List<OnboardingStep> settingsSteps = [
    OnboardingStep.personalInfo,
    OnboardingStep.physicalInfo,
    OnboardingStep.healthInfo,
    OnboardingStep.goals,
    OnboardingStep.preferences,
  ];

  @override
  void initState() {
    super.initState();

    // Only create page controller for wizard mode
    if (!widget.isEditMode) {
      // Use the provided initial step or fall back to personalInfo
      final targetStep = widget.initialStep ?? OnboardingStep.personalInfo;
      int initialIndex = settingsSteps.indexOf(targetStep);
      if (initialIndex == -1) {
        initialIndex = 0;
      }
      _pageController = PageController(initialPage: initialIndex);
    }

    // Set the step in the provider to match our target and pre-fill data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // Determine the step to set
        final targetStep = widget.initialStep ?? OnboardingStep.personalInfo;
        int initialIndex = settingsSteps.indexOf(targetStep);
        if (initialIndex == -1) {
          initialIndex = 0;
        }
        final stepToSet = settingsSteps[initialIndex];

        // Set the step first
        ref.read(onboardingNotifierProvider.notifier).goToStep(stepToSet);

        if (widget.isEditMode) {
          // In edit mode, pre-fill from local storage
          await _loadExistingDataForEdit();
          // Initialize change tracking after data is loaded
          await _initializeChangeTracking();
        } else {
          // In wizard mode, pre-fill from user profile if available
          final userProfile = ref.read(currentUserProfileProvider);
          if (userProfile != null) {
            ref.read(onboardingNotifierProvider.notifier).preFillFromUserProfile(userProfile);
          }
          // Initialize change tracking for wizard mode too
          await _initializeChangeTracking();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحميل الصفحة: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    });
  }

  Future<void> _loadExistingDataForEdit() async {
    try {
      // Show loading state
      if (mounted) {
        setState(() {
          // Could add a loading state here if needed
        });
      }

      final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
      await onboardingNotifier.loadExistingOnboardingData();

      // Validate the loaded data
      final currentState = ref.read(onboardingNotifierProvider);
      if (currentState.data == const OnboardingData()) {
        // No data was loaded, show a warning
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على بيانات محفوظة. سيتم استخدام القيم الافتراضية.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('Error loading existing data for edit: $e'); // Debug log
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تعذر تحميل البيانات الحالية: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    if (!widget.isEditMode && _pageController != null) {
      _pageController!.dispose();
    }
    super.dispose();
  }

  /// Initialize change tracking by capturing initial states
  Future<void> _initializeChangeTracking() async {
    try {
      // Capture initial local storage state
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final planPreferences = localStorageService.loadPlanPreferences();

      _initialLocalState = <String, dynamic>{};
      if (planPreferences != null) {
        _initialLocalState!.addAll(planPreferences);
      }

      // Capture last known Firestore state for authenticated users
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (isAuthenticated) {
        await _loadLastKnownFirestoreState();
      }

      print('Auto-save: Change tracking initialized'); // Debug log
    } catch (e) {
      print('Auto-save: Error initializing change tracking: $e'); // Debug log
    }
  }

  /// Load the last known Firestore state for comparison
  Future<void> _loadLastKnownFirestoreState() async {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);

      // Load plan preferences from Firestore
      final planPreferences = await firestoreService.getPlanPreferences();

      _lastKnownFirestoreState = <String, dynamic>{};
      if (planPreferences != null) {
        _lastKnownFirestoreState!.addAll(planPreferences);
      }

      print('Auto-save: Loaded last known Firestore state'); // Debug log
    } catch (e) {
      print('Auto-save: Error loading Firestore state: $e'); // Debug log
      // If we can't load Firestore state, assume changes exist to be safe
      _lastKnownFirestoreState = null;
    }
  }

  /// Check if there are unsaved changes that need to be synced to Firestore
  Future<bool> _hasUnsavedChangesToFirestore() async {
    try {
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (!isAuthenticated) {
        // For guest users, no Firestore sync needed
        return false;
      }

      // Get current local storage state
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final currentPlanPreferences = localStorageService.loadPlanPreferences();
      final currentNotificationSettings = localStorageService.loadNotificationSettings();

      final currentLocalState = <String, dynamic>{};
      if (currentPlanPreferences != null) {
        currentLocalState.addAll(currentPlanPreferences);
      }
      if (currentNotificationSettings != null) {
        currentLocalState.addAll(currentNotificationSettings);
      }

      // If we don't have the last known Firestore state, assume changes exist
      if (_lastKnownFirestoreState == null) {
        print('Auto-save: No Firestore state available, assuming changes exist'); // Debug log
        return true;
      }

      // Compare current local state with last known Firestore state
      final hasChanges = !_mapsAreEqual(currentLocalState, _lastKnownFirestoreState!);
      print('Auto-save: Unsaved changes to Firestore: $hasChanges'); // Debug log
      return hasChanges;
    } catch (e) {
      print('Auto-save: Error checking for unsaved changes: $e'); // Debug log
      // If we can't determine, assume changes exist to be safe
      return true;
    }
  }

  /// Compare two maps for equality (ignoring timestamps and other metadata)
  bool _mapsAreEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    // Create copies without timestamp fields for comparison
    final cleanMap1 = Map<String, dynamic>.from(map1);
    final cleanMap2 = Map<String, dynamic>.from(map2);

    // Remove timestamp fields that shouldn't affect equality
    cleanMap1.remove('updatedAt');
    cleanMap1.remove('updated_at');
    cleanMap2.remove('updatedAt');
    cleanMap2.remove('updated_at');

    if (cleanMap1.length != cleanMap2.length) return false;

    for (final key in cleanMap1.keys) {
      if (!cleanMap2.containsKey(key) || cleanMap1[key] != cleanMap2[key]) {
        return false;
      }
    }

    return true;
  }

  /// Perform auto-save operation before navigation
  Future<bool> _performAutoSave() async {
    if (_isAutoSaving) {
      // Already in progress, wait for completion
      return true;
    }

    try {
      setState(() {
        _isAutoSaving = true;
      });

      // Show saving indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 12),
                Text('جاري حفظ التغييرات...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Perform complete save
      final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
      await onboardingNotifier.saveOnboardingData(isCompleteSave: true);

      // Update the last known Firestore state
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (isAuthenticated) {
        await _loadLastKnownFirestoreState();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات تلقائياً'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }

      print('Auto-save: Successfully completed auto-save'); // Debug log
      return true;
    } catch (e) {
      print('Auto-save: Error during auto-save: $e'); // Debug log

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        return await _showAutoSaveErrorDialog();
      }
      return false;
    } finally {
      if (mounted) {
        setState(() {
          _isAutoSaving = false;
        });
      }
    }
  }

  /// Show error dialog when auto-save fails
  Future<bool> _showAutoSaveErrorDialog() async {
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('فشل في حفظ التغييرات'),
          content: const Text(
            'حدث خطأ أثناء حفظ التغييرات. ماذا تريد أن تفعل؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop('discard'),
              child: const Text('تجاهل التغييرات'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('local'),
              child: const Text('حفظ محلياً فقط'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop('retry'),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        );
      },
    );

    switch (result) {
      case 'retry':
        return await _performAutoSave();
      case 'local':
        try {
          // Save to local storage only
          final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
          await onboardingNotifier.saveOnboardingData(isCompleteSave: false);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حفظ التغييرات محلياً'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 2),
              ),
            );
          }
          return true;
        } catch (e) {
          print('Auto-save: Local save also failed: $e'); // Debug log
          return false;
        }
      case 'discard':
      default:
        return true; // Allow navigation even if discarding changes
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);

    // Listen for step changes (only in wizard mode)
    if (!widget.isEditMode) {
      ref.listen(onboardingNotifierProvider, (previous, next) {
        if (previous?.currentStep != next.currentStep) {
          final newIndex = settingsSteps.indexOf(next.currentStep);
          if (newIndex != -1 && _pageController != null && _pageController!.hasClients) {
            try {
              _pageController!.animateToPage(
                newIndex,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            } catch (e) {
              print('Error animating to page: $e'); // Debug log
            }
          }
        }
      });
    }

    return PopScope(
      canPop: false, // Always intercept navigation attempts
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (didPop) return; // Already popped, nothing to do

        // Check for unsaved changes and perform auto-save if needed
        final shouldProceed = await _handleNavigationAttempt();
        if (shouldProceed && mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.isEditMode ? _getEditModeTitle(onboardingState.currentStep) : 'إدارة الخطة الشخصية',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () async {
            final shouldProceed = await _handleNavigationAttempt();
            if (shouldProceed && mounted) {
              context.pop();
            }
          },
        ),
        actions: [
          if (kDebugMode && !widget.isEditMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              tooltip: 'Pre-fill with test data',
              onPressed: () {
                onboardingNotifier.preFillWithTestData();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Settings data pre-filled with test values'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
            ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator (only in wizard mode)
            if (!widget.isEditMode)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: OnboardingStepIndicator(
                  currentStep: onboardingState.currentStep,
                  progress: _calculateSettingsProgress(onboardingState.currentStep),
                  totalSteps: settingsSteps.length,
                  steps: settingsSteps,
                ),
              ),

            // Step content
            Expanded(
              child: widget.isEditMode
                ? _buildEditModeContent(onboardingState.currentStep)
                : _pageController != null
                    ? PageView(
                        controller: _pageController!,
                        onPageChanged: (index) {
                          if (index < settingsSteps.length) {
                            try {
                              onboardingNotifier.goToStep(settingsSteps[index]);
                            } catch (e) {
                              print('Error changing step: $e'); // Debug log
                            }
                          }
                        },
                        children: const [
                          PersonalInfoStep(),
                          PhysicalInfoStep(),
                          HealthInfoStep(),
                          GoalsStep(),
                          PreferencesStep(),
                        ],
                      )
                    : const Center(
                        child: CircularProgressIndicator(),
                      ),
            ),

            // Navigation buttons
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: widget.isEditMode
                ? _buildEditModeButtons()
                : _buildWizardModeButtons(),
            ),
          ],
        ),
      ),
    ),
    );
  }

  /// Handle navigation attempts with auto-save logic
  Future<bool> _handleNavigationAttempt() async {
    print('Auto-save: Navigation attempt detected'); // Debug log

    // Check if there are unsaved changes
    final hasUnsavedChanges = await _hasUnsavedChangesToFirestore();

    if (!hasUnsavedChanges) {
      print('Auto-save: No unsaved changes, allowing navigation'); // Debug log
      return true;
    }

    print('Auto-save: Unsaved changes detected, performing auto-save'); // Debug log

    // Perform auto-save
    return await _performAutoSave();
  }

  int _getCurrentStepIndex() {
    final currentStep = ref.read(onboardingNotifierProvider).currentStep;
    return settingsSteps.indexOf(currentStep);
  }

  bool _isLastStep() {
    return _getCurrentStepIndex() == settingsSteps.length - 1;
  }

  bool _isCurrentStepValid() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final currentStep = onboardingState.currentStep;
    return onboardingState.stepValidation[currentStep] ?? false;
  }

  void _goToPreviousStep() {
    final currentIndex = _getCurrentStepIndex();
    if (currentIndex > 0) {
      final previousStep = settingsSteps[currentIndex - 1];
      ref.read(onboardingNotifierProvider.notifier).goToStep(previousStep);
    }
  }

  void _goToNextStepOrSave() {
    final currentIndex = _getCurrentStepIndex();
    if (_isLastStep()) {
      // Show save step
      _showSaveStep();
    } else if (currentIndex < settingsSteps.length - 1) {
      final nextStep = settingsSteps[currentIndex + 1];
      ref.read(onboardingNotifierProvider.notifier).goToStep(nextStep);
    }
  }

  void _showSaveStep() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SettingsSaveStep(),
    );
  }

  double _calculateSettingsProgress(OnboardingStep currentStep) {
    final currentIndex = settingsSteps.indexOf(currentStep);
    if (currentIndex == -1) return 0.0;
    return (currentIndex + 1) / settingsSteps.length;
  }

  String _getEditModeTitle(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.personalInfo:
        return 'تعديل المعلومات الشخصية';
      case OnboardingStep.physicalInfo:
        return 'تعديل القياسات الجسدية';
      case OnboardingStep.healthInfo:
        return 'تعديل المعلومات الصحية';
      case OnboardingStep.goals:
        return 'تعديل التفضيلات الغذائية';
      case OnboardingStep.preferences:
        return 'تعديل تفضيلات الطعام';
      case OnboardingStep.complete:
        return 'تعديل الإعدادات';
    }
  }

  Widget _buildEditModeContent(OnboardingStep currentStep) {
    try {
      switch (currentStep) {
        case OnboardingStep.personalInfo:
          return const SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: PersonalInfoStep(),
          );
        case OnboardingStep.physicalInfo:
          return const SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: PhysicalInfoStep(),
          );
        case OnboardingStep.healthInfo:
          return const SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: HealthInfoStep(),
          );
        case OnboardingStep.goals:
          return const SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: GoalsStep(),
          );
        case OnboardingStep.preferences:
          return const SingleChildScrollView(
            padding: EdgeInsets.all(16.0),
            child: PreferencesStep(),
          );
        case OnboardingStep.complete:
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'هذه الخطوة غير متاحة للتعديل',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
          );
      }
    } catch (e) {
      print('Error building edit mode content: $e'); // Debug log
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في تحميل المحتوى',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'يرجى المحاولة مرة أخرى',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: const Text('العودة'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildEditModeButtons() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isCurrentStepValid() ? () => _saveAndReturn() : null,
        child: const Text('حفظ التغييرات'),
      ),
    );
  }

  Widget _buildWizardModeButtons() {
    return Row(
      children: [
        // Previous button
        if (_getCurrentStepIndex() > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: () => _goToPreviousStep(),
              child: const Text('السابق'),
            ),
          ),

        if (_getCurrentStepIndex() > 0)
          const SizedBox(width: 16),

        // Next/Save button
        Expanded(
          flex: _getCurrentStepIndex() == 0 ? 1 : 2,
          child: ElevatedButton(
            onPressed: _isCurrentStepValid() ? () => _goToNextStepOrSave() : null,
            child: Text(_isLastStep() ? 'حفظ' : 'التالي'),
          ),
        ),
      ],
    );
  }

  Future<void> _saveAndReturn() async {
    final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);

    try {
      // Save the current onboarding data with complete save flag
      await onboardingNotifier.saveOnboardingData(isCompleteSave: true);

      // Update the last known Firestore state after successful save
      final isAuthenticated = ref.read(isAuthenticatedProvider);
      if (isAuthenticated) {
        await _loadLastKnownFirestoreState();
      }

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Navigate back to previous page (preserves navigation stack)
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حفظ التغييرات: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
