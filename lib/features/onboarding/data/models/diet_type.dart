import 'package:freezed_annotation/freezed_annotation.dart';

part '../../../onboarding/data/models/diet_type.freezed.dart';
part '../../../onboarding/data/models/diet_type.g.dart';

/// Diet type enum
enum DietType {
  @JsonValue('balanced')
  balanced,
  @JsonValue('high_protein')
  highProtein,
  @JsonValue('ketogenic')
  ketogenic,
  @JsonValue('low_carb')
  lowCarb,
  @JsonValue('mediterranean')
  mediterranean,
  @JsonValue('plant_based')
  plantBased,
  @JsonValue('custom')
  custom,
}

/// Macro distribution model
@freezed
class MacroDistribution with _$MacroDistribution {
  const factory MacroDistribution({
    @JsonKey(name: 'carbs_percentage') required double carbsPercentage,
    @JsonKey(name: 'protein_percentage') required double proteinPercentage,
    @<PERSON>son<PERSON>ey(name: 'fat_percentage') required double fatPercentage,
  }) = _MacroDistribution;

  factory MacroDistribution.fromJson(Map<String, dynamic> json) =>
      _$MacroDistributionFromJson(json);
}

/// Diet type data model with predefined macro distributions
@freezed
class DietTypeData with _$DietTypeData {
  const factory DietTypeData({
    required DietType type,
    required String nameAr,
    required String nameEn,
    required String descriptionAr,
    required String descriptionEn,
    required String iconName,
    required MacroDistribution macroDistribution,
    required List<String> benefits,
    required List<String> warnings,
  }) = _DietTypeData;

  factory DietTypeData.fromJson(Map<String, dynamic> json) =>
      _$DietTypeDataFromJson(json);
}

/// Predefined diet types with their data
class DietTypes {
  static const List<DietTypeData> predefined = [
    DietTypeData(
      type: DietType.balanced,
      nameAr: 'النظام المتوازن',
      nameEn: 'Balanced Diet',
      descriptionAr: 'مثالي للصحة العامة والحفاظ على الوزن',
      descriptionEn: 'Ideal for general health and weight maintenance',
      iconName: 'balance',
      macroDistribution: MacroDistribution(
        carbsPercentage: 50.0,
        proteinPercentage: 20.0,
        fatPercentage: 30.0,
      ),
      benefits: [
        'توازن غذائي مثالي',
        'سهل الاتباع',
        'مناسب للحياة اليومية',
      ],
      warnings: [],
    ),
    DietTypeData(
      type: DietType.highProtein,
      nameAr: 'عالي البروتين',
      nameEn: 'High-Protein',
      descriptionAr: 'ممتاز لبناء العضلات والشعور بالشبع',
      descriptionEn: 'Great for muscle building and satiety',
      iconName: 'fitness_center',
      macroDistribution: MacroDistribution(
        carbsPercentage: 40.0,
        proteinPercentage: 30.0,
        fatPercentage: 30.0,
      ),
      benefits: [
        'يساعد في بناء العضلات',
        'يزيد الشعور بالشبع',
        'يحافظ على الكتلة العضلية',
      ],
      warnings: [
        'قد يحتاج مراقبة وظائف الكلى',
      ],
    ),
    DietTypeData(
      type: DietType.ketogenic,
      nameAr: 'الكيتو',
      nameEn: 'Ketogenic (Keto)',
      descriptionAr: 'فعال لفقدان الوزن السريع',
      descriptionEn: 'Effective for rapid weight loss',
      iconName: 'local_fire_department',
      macroDistribution: MacroDistribution(
        carbsPercentage: 5.0,
        proteinPercentage: 20.0,
        fatPercentage: 75.0,
      ),
      benefits: [
        'فقدان وزن سريع',
        'تحسين التركيز',
        'استقرار السكر',
      ],
      warnings: [
        'قد يسبب أعراض البداية (كيتو فلو)',
        'يحتاج متابعة طبية',
        'صعب الاتباع طويل المدى',
      ],
    ),
    DietTypeData(
      type: DietType.lowCarb,
      nameAr: 'قليل الكربوهيدرات',
      nameEn: 'Low-Carb',
      descriptionAr: 'نهج متوازن لفقدان الوزن',
      descriptionEn: 'Balanced approach to weight loss',
      iconName: 'trending_down',
      macroDistribution: MacroDistribution(
        carbsPercentage: 20.0,
        proteinPercentage: 30.0,
        fatPercentage: 50.0,
      ),
      benefits: [
        'فقدان وزن تدريجي',
        'أسهل من الكيتو',
        'يحسن حساسية الأنسولين',
      ],
      warnings: [
        'قد يحتاج فترة تكيف',
      ],
    ),
    DietTypeData(
      type: DietType.mediterranean,
      nameAr: 'البحر المتوسط',
      nameEn: 'Mediterranean',
      descriptionAr: 'صحي للقلب مع التركيز على الدهون الصحية',
      descriptionEn: 'Heart-healthy with emphasis on healthy fats',
      iconName: 'favorite',
      macroDistribution: MacroDistribution(
        carbsPercentage: 45.0,
        proteinPercentage: 15.0,
        fatPercentage: 40.0,
      ),
      benefits: [
        'يحسن صحة القلب',
        'غني بمضادات الأكسدة',
        'مستدام طويل المدى',
      ],
      warnings: [],
    ),
    DietTypeData(
      type: DietType.plantBased,
      nameAr: 'نباتي',
      nameEn: 'Plant-Based',
      descriptionAr: 'مستدام وصديق للبيئة',
      descriptionEn: 'Sustainable and environmentally friendly',
      iconName: 'eco',
      macroDistribution: MacroDistribution(
        carbsPercentage: 60.0,
        proteinPercentage: 15.0,
        fatPercentage: 25.0,
      ),
      benefits: [
        'صديق للبيئة',
        'غني بالألياف',
        'يقلل خطر الأمراض المزمنة',
      ],
      warnings: [
        'يحتاج تخطيط للحصول على البروتين الكامل',
        'قد يحتاج مكملات فيتامين B12',
      ],
    ),
  ];

  /// Get diet type data by type
  static DietTypeData? getByType(DietType type) {
    try {
      return predefined.firstWhere((data) => data.type == type);
    } catch (e) {
      return null;
    }
  }

  /// Get macro distribution for a diet type
  static MacroDistribution? getMacroDistribution(DietType type) {
    return getByType(type)?.macroDistribution;
  }

  /// Validate custom macro distribution
  static bool isValidMacroDistribution(MacroDistribution distribution) {
    final total = distribution.carbsPercentage + 
                  distribution.proteinPercentage + 
                  distribution.fatPercentage;
    return (total >= 99.0 && total <= 101.0); // Allow 1% tolerance for rounding
  }

  /// Check if macro distribution is potentially unhealthy
  static List<String> getHealthWarnings(MacroDistribution distribution) {
    final warnings = <String>[];
    
    if (distribution.carbsPercentage < 10) {
      warnings.add('نسبة الكربوهيدرات منخفضة جداً - قد تؤثر على الطاقة');
    }
    
    if (distribution.proteinPercentage > 35) {
      warnings.add('نسبة البروتين عالية جداً - قد تؤثر على الكلى');
    }
    
    if (distribution.fatPercentage > 70) {
      warnings.add('نسبة الدهون عالية جداً - يحتاج متابعة طبية');
    }
    
    if (distribution.fatPercentage < 15) {
      warnings.add('نسبة الدهون منخفضة جداً - قد تؤثر على امتصاص الفيتامينات');
    }
    
    return warnings;
  }
}
