import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:easydietai/features/onboarding/data/models/diet_type.dart';
import 'package:easydietai/features/user_profile/data/models/user_profile.dart';

part 'plan_preferences.freezed.dart';
part 'plan_preferences.g.dart';

@freezed
class PlanPreferences with _$PlanPreferences {
  const factory PlanPreferences({
    // Personal Information
    @JsonKey(name: 'date_of_birth') DateTime? dateOfBirth,
    @Default(Gender.notSpecified) Gender gender,

    // Physical Information
    double? height, // in cm
    double? weight, // in kg
    @Json<PERSON>ey(name: 'activity_level') @Default(ActivityLevel.moderate) ActivityLevel activityLevel,
    @JsonKey(name: 'daily_burned_calories') int? dailyBurnedCalories, // manually entered daily burned calories
    @JsonKey(name: 'use_manual_calories') @Default(false) bool useManualCalories, // whether to use manual calories instead of activity level

    // Health Information
    @Default([]) List<String> allergies,
    @JsonKey(name: 'dietary_restrictions') @Default([]) List<String> dietaryRestrictions,
    @JsonKey(name: 'health_conditions') @Default([]) List<String> healthConditions,
    @Default([]) List<String> medications,

    // Goals
    @JsonKey(name: 'health_goal') @Default(HealthGoal.maintain) HealthGoal healthGoal,
    @JsonKey(name: 'target_weight') double? targetWeight,
    @JsonKey(name: 'daily_calorie_goal') int? dailyCalorieGoal,
    @JsonKey(name: 'selected_calorie_recommendation') String? selectedCalorieRecommendation, // Track which recommendation option is selected
    @JsonKey(name: 'protein_goal') double? proteinGoal,
    @JsonKey(name: 'carbs_goal') double? carbsGoal,
    @JsonKey(name: 'fat_goal') double? fatGoal,
    @JsonKey(name: 'fiber_goal') double? fiberGoal,

    // Diet Type & Macros
    @JsonKey(name: 'selected_diet_type') DietType? selectedDietType,
    @JsonKey(
      name: 'custom_macro_distribution',
      includeIfNull: false,
      fromJson: _macroDistributionFromJson,
      toJson: _macroDistributionToJson,
    ) MacroDistribution? customMacroDistribution, // Only used when selectedDietType is custom
    @JsonKey(name: 'use_custom_macros') bool? useCustomMacros, // Whether user chose custom macros over predefined diet

    // Preferences
    @JsonKey(name: 'favorite_ingredients') @Default([]) List<String> favoriteIngredients,
    @JsonKey(name: 'disliked_ingredients') @Default([]) List<String> dislikedIngredients,
    @JsonKey(name: 'favorite_cuisines') @Default([]) List<String> favoriteCuisines,
    @JsonKey(name: 'meals_per_day') @Default(3) int mealsPerDay,
    @JsonKey(name: 'snacks_per_day') @Default(2) int snacksPerDay,
  }) = _PlanPreferences;

  factory PlanPreferences.fromJson(Map<String, dynamic> json) =>
      _$PlanPreferencesFromJson(json);
}

/// Helper functions for MacroDistribution JSON conversion
MacroDistribution? _macroDistributionFromJson(Map<String, dynamic>? json) {
  if (json == null) return null;
  return MacroDistribution.fromJson(json);
}

Map<String, dynamic>? _macroDistributionToJson(MacroDistribution? macroDistribution) {
  return macroDistribution?.toJson();
}
