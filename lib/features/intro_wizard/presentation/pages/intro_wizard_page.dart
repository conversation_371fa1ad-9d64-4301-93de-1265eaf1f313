import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_routes.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../providers/intro_wizard_provider.dart';
import '../widgets/welcome_intro_step.dart';
import '../widgets/features_intro_step.dart';
import '../widgets/ai_powered_intro_step.dart';
import '../widgets/personalized_intro_step.dart';
import '../widgets/get_started_intro_step.dart';

class IntroWizardPage extends ConsumerStatefulWidget {
  const IntroWizardPage({super.key});

  @override
  ConsumerState<IntroWizardPage> createState() => _IntroWizardPageState();
}

class _IntroWizardPageState extends ConsumerState<IntroWizardPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    final currentStep = ref.read(introWizardNotifierProvider).currentStep;
    _pageController = PageController(
      initialPage: IntroWizardStep.values.indexOf(currentStep),
    );

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleSkip() async {
    await ref.read(appStateNotifierProvider.notifier).completeIntroWizard();

    // Ensure state is fully propagated before navigation
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          context.go(AppRoutes.auth);
        }
      });
    }
  }

  Future<void> _handleGetStarted() async {
    await ref.read(appStateNotifierProvider.notifier).completeIntroWizard();

    // Ensure state is fully propagated before navigation
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          context.go(AppRoutes.auth);
        }
      });
    }
  }

  void _handleNext() {
    final wizardNotifier = ref.read(introWizardNotifierProvider.notifier);
    if (wizardNotifier.isLastStep) {
      _handleGetStarted();
    } else {
      wizardNotifier.nextStep();
    }
  }

  void _handleBack() {
    ref.read(introWizardNotifierProvider.notifier).previousStep();
  }

  @override
  Widget build(BuildContext context) {
    final wizardState = ref.watch(introWizardNotifierProvider);
    final wizardNotifier = ref.read(introWizardNotifierProvider.notifier);

    // Listen for step changes
    ref.listen(introWizardNotifierProvider, (previous, next) {
      if (previous?.currentStep != next.currentStep) {
        final newIndex = IntroWizardStep.values.indexOf(next.currentStep);
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // Top bar with skip button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Progress indicator
                    Expanded(
                      child: LinearProgressIndicator(
                        value: wizardNotifier.progress,
                        backgroundColor: AppColors.border,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primary,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Skip button
                    TextButton(
                      onPressed: _handleSkip,
                      child: Text(
                        'تخطي',
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    wizardNotifier.goToStep(IntroWizardStep.values[index]);
                  },
                  children: const [
                    WelcomeIntroStep(),
                    FeaturesIntroStep(),
                    AiPoweredIntroStep(),
                    PersonalizedIntroStep(),
                    GetStartedIntroStep(),
                  ],
                ),
              ),

              // Bottom navigation
              Container(
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Back button
                    if (wizardNotifier.canGoBack)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _handleBack,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('السابق'),
                        ),
                      ),

                    if (wizardNotifier.canGoBack) const SizedBox(width: 16),

                    // Next/Get Started button
                    Expanded(
                      flex: wizardNotifier.canGoBack ? 1 : 1,
                      child: ElevatedButton(
                        onPressed: _handleNext,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          wizardNotifier.isLastStep ? 'ابدأ الآن' : 'التالي',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
