import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class PersonalizedIntroStep extends StatelessWidget {
  const PersonalizedIntroStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Personalization illustration
          Stack(
            alignment: Alignment.center,
            children: [
              // Background circles
              ...List.generate(3, (index) {
                return Container(
                  width: 120 + (index * 20.0),
                  height: 120 + (index * 20.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.2 - (index * 0.05)),
                      width: 2,
                    ),
                  ),
                );
              }),
              // Main icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.secondary,
                      AppColors.info,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.secondary.withOpacity(0.4),
                      blurRadius: 25,
                      offset: const Offset(0, 12),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.person_pin,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 40),

          // Title
          Text(
            'مخصص خصيصاً لك',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Subtitle
          Text(
            'كل شيء يتكيف مع احتياجاتك الفريدة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Personalization features
          Column(
            children: [
              _buildPersonalizationItem(
                context,
                Icons.favorite,
                'تفضيلاتك الغذائية',
                'نراعي ما تحب وما لا تحب من الأطعمة',
                AppColors.primary,
              ),
              const SizedBox(height: 20),
              _buildPersonalizationItem(
                context,
                Icons.fitness_center,
                'أهدافك الصحية',
                'خطط مصممة لتحقيق أهدافك الشخصية',
                AppColors.secondary,
              ),
              const SizedBox(height: 20),
              _buildPersonalizationItem(
                context,
                Icons.schedule,
                'نمط حياتك',
                'جداول تتناسب مع روتينك اليومي',
                AppColors.info,
              ),
              const SizedBox(height: 20),
              _buildPersonalizationItem(
                context,
                Icons.local_hospital,
                'حالتك الصحية',
                'نأخذ في الاعتبار أي قيود صحية أو حساسية',
                Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalizationItem(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 52,
          height: 52,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(14),
          ),
          child: Icon(
            icon,
            size: 26,
            color: color,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
