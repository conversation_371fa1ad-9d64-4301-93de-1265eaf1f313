import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';

class WelcomeIntroStep extends StatelessWidget {
  const WelcomeIntroStep({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // App logo
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                ),
              ],
            ),
            child: const Icon(
              Icons.restaurant_menu,
              size: 80,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 48),

          // Welcome title
          Text(
            'مرحباً بك في EasyDietAI',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Subtitle
          Text(
            'رفيقك الذكي لحياة صحية أفضل',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Description
          Text(
            'اكتشف قوة الذكاء الاصطناعي في تخطيط وجباتك اليومية. سنساعدك في الوصول لأهدافك الصحية بطريقة ممتعة وسهلة.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 48),

          // Features preview
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFeatureIcon(
                context,
                Icons.psychology,
                'ذكي',
                AppColors.primary,
              ),
              _buildFeatureIcon(
                context,
                Icons.person,
                'مخصص',
                AppColors.secondary,
              ),
              _buildFeatureIcon(
                context,
                Icons.track_changes,
                'فعال',
                AppColors.info,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureIcon(
    BuildContext context,
    IconData icon,
    String label,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            icon,
            size: 30,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }
}
