import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'intro_wizard_provider.freezed.dart';
part 'intro_wizard_provider.g.dart';

/// Introduction wizard step enum
enum IntroWizardStep {
  welcome,
  features,
  aiPowered,
  personalized,
  getStarted,
}

/// Introduction wizard state
@freezed
class IntroWizardState with _$IntroWizardState {
  const factory IntroWizardState({
    @Default(IntroWizardStep.welcome) IntroWizardStep currentStep,
    @Default(false) bool isLoading,
  }) = _IntroWizardState;
}

/// Introduction wizard notifier
@riverpod
class IntroWizardNotifier extends _$IntroWizardNotifier {
  @override
  IntroWizardState build() {
    return const IntroWizardState();
  }

  /// Go to next step
  void nextStep() {
    final currentIndex = IntroWizardStep.values.indexOf(state.currentStep);
    if (currentIndex < IntroWizardStep.values.length - 1) {
      final nextStep = IntroWizardStep.values[currentIndex + 1];
      state = state.copyWith(currentStep: nextStep);
    }
  }

  /// Go to previous step
  void previousStep() {
    final currentIndex = IntroWizardStep.values.indexOf(state.currentStep);
    if (currentIndex > 0) {
      final previousStep = IntroWizardStep.values[currentIndex - 1];
      state = state.copyWith(currentStep: previousStep);
    }
  }

  /// Go to specific step
  void goToStep(IntroWizardStep step) {
    state = state.copyWith(currentStep: step);
  }

  /// Get progress percentage
  double get progress {
    final currentIndex = IntroWizardStep.values.indexOf(state.currentStep);
    final totalSteps = IntroWizardStep.values.length - 1;
    return currentIndex / totalSteps;
  }

  /// Check if can go to next step
  bool get canGoNext {
    final currentIndex = IntroWizardStep.values.indexOf(state.currentStep);
    return currentIndex < IntroWizardStep.values.length - 1;
  }

  /// Check if can go to previous step
  bool get canGoBack {
    final currentIndex = IntroWizardStep.values.indexOf(state.currentStep);
    return currentIndex > 0;
  }

  /// Check if is last step
  bool get isLastStep {
    return state.currentStep == IntroWizardStep.getStarted;
  }

  /// Reset wizard
  void reset() {
    state = const IntroWizardState();
  }
}
