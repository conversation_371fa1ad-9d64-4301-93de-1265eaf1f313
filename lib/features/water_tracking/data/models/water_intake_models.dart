import 'package:freezed_annotation/freezed_annotation.dart';

part 'water_intake_models.freezed.dart';
part 'water_intake_models.g.dart';

/// Model for a single water intake entry
@freezed
class WaterIntakeEntry with _$WaterIntakeEntry {
  const factory WaterIntakeEntry({
    required String id,
    required double amount, // Amount in milliliters
    required DateTime timestamp,
    String? note,
  }) = _WaterIntakeEntry;

  factory WaterIntakeEntry.fromJson(Map<String, dynamic> json) =>
      _$WaterIntakeEntryFromJson(json);
}

/// Model for daily water intake data
@freezed
class DailyWaterIntake with _$DailyWaterIntake {
  const factory DailyWaterIntake({
    required String date, // Format: yyyy-MM-dd
    required double dailyGoal, // Daily goal in milliliters
    @Default([]) List<WaterIntakeEntry> entries,
    @Default(0.0) double totalIntake, // Total intake for the day in milliliters
  }) = _DailyWaterIntake;

  factory DailyWaterIntake.fromJson(Map<String, dynamic> json) =>
      _$DailyWaterIntakeFromJson(json);
}

/// Extension to add computed properties
extension DailyWaterIntakeExtension on DailyWaterIntake {
  /// Calculate total intake from entries
  double get calculatedTotalIntake {
    return entries.fold(0.0, (sum, entry) => sum + entry.amount);
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (dailyGoal <= 0) return 0.0;
    return (calculatedTotalIntake / dailyGoal).clamp(0.0, 1.0);
  }

  /// Check if daily goal is achieved
  bool get isGoalAchieved {
    return calculatedTotalIntake >= dailyGoal;
  }

  /// Get remaining amount to reach goal
  double get remainingAmount {
    final remaining = dailyGoal - calculatedTotalIntake;
    return remaining > 0 ? remaining : 0.0;
  }

  /// Format total intake for display (e.g., "1.2L" or "500ml")
  String get formattedTotalIntake {
    if (calculatedTotalIntake >= 1000) {
      return '${(calculatedTotalIntake / 1000).toStringAsFixed(1)}L';
    } else {
      return '${calculatedTotalIntake.toInt()}ml';
    }
  }

  /// Format daily goal for display
  String get formattedDailyGoal {
    if (dailyGoal >= 1000) {
      return '${(dailyGoal / 1000).toStringAsFixed(1)}L';
    } else {
      return '${dailyGoal.toInt()}ml';
    }
  }

  /// Format progress for display (e.g., "1.2L / 2.5L")
  String get formattedProgress {
    return '$formattedTotalIntake / $formattedDailyGoal';
  }
}

/// Model for water intake settings
@freezed
class WaterIntakeSettings with _$WaterIntakeSettings {
  const factory WaterIntakeSettings({
    @Default(2500.0) double dailyGoal, // Default 2.5L
    @Default([250.0, 500.0, 750.0, 1000.0]) List<double> quickAddAmounts,
    @Default(true) bool enableNotifications,
    @Default(60) int reminderIntervalMinutes,
  }) = _WaterIntakeSettings;

  factory WaterIntakeSettings.fromJson(Map<String, dynamic> json) =>
      _$WaterIntakeSettingsFromJson(json);
}
