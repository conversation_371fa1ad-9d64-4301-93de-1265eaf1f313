import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/water_tracking/data/models/water_intake_models.dart';

part 'water_intake_sync_service.g.dart';

/// Service for synchronizing water intake data between Firestore and local storage
class WaterIntakeSyncService {
  final FirestoreService _firestoreService;
  final LocalStorageService _localStorageService;
  final FirebaseAuth _auth;

  WaterIntakeSyncService({
    required FirestoreService firestoreService,
    required LocalStorageService localStorageService,
    FirebaseAuth? auth,
  })  : _firestoreService = firestoreService,
        _localStorageService = localStorageService,
        _auth = auth ?? FirebaseAuth.instance;

  /// Check if user is authenticated
  bool get _isAuthenticated => _auth.currentUser != null;

  /// Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  /// Generate date hash ID for local storage
  String _generateDateHashKey(String date) => 'water_intake_hash_$date';

  /// Get today's date as string (yyyy-MM-dd format)
  String _getTodayDateString() {
    return DateFormat('yyyy-MM-dd').format(DateTime.now());
  }

  /// Sync water intake data to Firestore
  Future<bool> syncWaterIntakeToFirestore(String date, DailyWaterIntake dailyIntake) async {
    if (!_isAuthenticated) {
      AppLogger.warning('WaterIntakeSyncService: User not authenticated, skipping sync to Firestore');
      return false;
    }

    try {
      AppLogger.info('WaterIntakeSyncService: Syncing water intake data to Firestore for $date');

      // Save to Firestore and get document ID
      final documentId = await _firestoreService.saveWaterIntakeData(date, dailyIntake.toJson());

      // Store the document hash ID in local storage for quick access
      await _localStorageService.saveString(_generateDateHashKey(date), documentId);

      AppLogger.info('WaterIntakeSyncService: Successfully synced water intake data to Firestore with document ID: $documentId');
      return true;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error syncing water intake data to Firestore: $e');
      return false;
    }
  }

  /// Sync water intake data from Firestore to local storage
  Future<bool> syncWaterIntakeFromFirestore(String date) async {
    if (!_isAuthenticated) {
      AppLogger.warning('WaterIntakeSyncService: User not authenticated, skipping sync from Firestore');
      return false;
    }

    try {
      AppLogger.info('WaterIntakeSyncService: Syncing water intake data from Firestore for $date');

      // Get data from Firestore
      final firestoreData = await _firestoreService.getWaterIntakeDataForDate(date);
      if (firestoreData == null) {
        AppLogger.info('WaterIntakeSyncService: No water intake data found in Firestore for $date');
        return false;
      }

      // Extract the document hash ID and store it locally
      final documentId = firestoreData['date_hash_id'] as String?;
      if (documentId != null) {
        await _localStorageService.saveString(_generateDateHashKey(date), documentId);
      }

      // Remove Firestore metadata before saving to local storage
      final cleanData = Map<String, dynamic>.from(firestoreData);
      cleanData.remove('date_hash_id');
      cleanData.remove('synced_at');
      cleanData.remove('last_modified');

      // Save to local storage
      final success = await _localStorageService.saveDailyWaterIntake(date, cleanData);

      if (success) {
        AppLogger.info('WaterIntakeSyncService: Successfully synced water intake data from Firestore to local storage');
        return true;
      } else {
        AppLogger.error('WaterIntakeSyncService: Failed to save water intake data to local storage');
        return false;
      }
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error syncing water intake data from Firestore: $e');
      return false;
    }
  }

  /// Add water intake entry with automatic Firestore sync
  Future<bool> addWaterIntakeWithSync({
    required double amount,
    DateTime? entryTimestamp,
    String? note,
  }) async {
    try {
      final today = _getTodayDateString();
      final timestamp = entryTimestamp ?? DateTime.now();

      AppLogger.info('WaterIntakeSyncService: Adding water intake entry with sync for $today');

      // Create new entry
      final entry = WaterIntakeEntry(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: amount,
        timestamp: timestamp,
        note: note,
      );

      // Get current daily intake or create new one
      DailyWaterIntake currentDailyIntake;
      final existingData = _localStorageService.loadDailyWaterIntake(today);

      if (existingData != null) {
        currentDailyIntake = DailyWaterIntake.fromJson(existingData);
      } else {
        // Load default settings for new daily intake
        final settingsData = _localStorageService.loadWaterSettings();
        final settings = settingsData != null
            ? WaterIntakeSettings.fromJson(settingsData)
            : const WaterIntakeSettings();

        currentDailyIntake = DailyWaterIntake(
          date: today,
          dailyGoal: settings.dailyGoal,
        );
      }

      // Add entry to the list
      final updatedEntries = [...currentDailyIntake.entries, entry];
      final updatedDailyIntake = currentDailyIntake.copyWith(
        entries: updatedEntries,
        totalIntake: updatedEntries.fold(0.0, (sum, e) => sum + e.amount),
      );

      // Save to local storage first
      final localSuccess = await _localStorageService.saveDailyWaterIntake(today, updatedDailyIntake.toJson());
      if (!localSuccess) {
        AppLogger.error('WaterIntakeSyncService: Failed to save water intake to local storage');
        return false;
      }

      // Sync to Firestore if authenticated
      if (_isAuthenticated) {
        final syncSuccess = await syncWaterIntakeToFirestore(today, updatedDailyIntake);
        if (!syncSuccess) {
          AppLogger.warning('WaterIntakeSyncService: Failed to sync water intake to Firestore, but local save succeeded');
          // Don't return false here as local save succeeded
        }
      }

      AppLogger.info('WaterIntakeSyncService: Successfully added water intake entry with sync');
      return true;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error adding water intake entry with sync: $e');
      return false;
    }
  }

  /// Remove water intake entry with automatic Firestore sync
  Future<bool> removeWaterIntakeWithSync({
    required String date,
    required String entryId,
  }) async {
    try {
      AppLogger.info('WaterIntakeSyncService: Removing water intake entry with sync for $date, entryId: $entryId');

      // Get current daily intake
      final existingData = _localStorageService.loadDailyWaterIntake(date);
      if (existingData == null) {
        AppLogger.warning('WaterIntakeSyncService: No daily intake data found for $date');
        return false;
      }

      final currentDailyIntake = DailyWaterIntake.fromJson(existingData);

      // Remove entry from the list
      final updatedEntries = currentDailyIntake.entries.where((entry) => entry.id != entryId).toList();

      // Check if entry was actually removed
      if (updatedEntries.length == currentDailyIntake.entries.length) {
        AppLogger.warning('WaterIntakeSyncService: Entry with ID $entryId not found');
        return false;
      }

      final updatedDailyIntake = currentDailyIntake.copyWith(
        entries: updatedEntries,
        totalIntake: updatedEntries.fold(0.0, (sum, e) => sum + e.amount),
      );

      // Save to local storage first
      final localSuccess = await _localStorageService.saveDailyWaterIntake(date, updatedDailyIntake.toJson());
      if (!localSuccess) {
        AppLogger.error('WaterIntakeSyncService: Failed to save updated water intake to local storage');
        return false;
      }

      // Sync to Firestore if authenticated
      if (_isAuthenticated) {
        final syncSuccess = await syncWaterIntakeToFirestore(date, updatedDailyIntake);
        if (!syncSuccess) {
          AppLogger.warning('WaterIntakeSyncService: Failed to sync water intake removal to Firestore, but local save succeeded');
          // Don't return false here as local save succeeded
        }
      }

      AppLogger.info('WaterIntakeSyncService: Successfully removed water intake entry with sync');
      return true;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error removing water intake entry with sync: $e');
      return false;
    }
  }

  /// Load current date's water intake data with Firestore sync check
  Future<DailyWaterIntake?> loadTodaysWaterIntakeWithSync() async {
    final today = _getTodayDateString();
    return await loadWaterIntakeWithSync(today);
  }

  /// Load water intake data for a specific date with Firestore sync check
  Future<DailyWaterIntake?> loadWaterIntakeWithSync(String date) async {
    try {
      AppLogger.info('WaterIntakeSyncService: Loading water intake data with sync for $date');

      // First, try to load from local storage
      final localData = _localStorageService.loadDailyWaterIntake(date);
      
      // If authenticated, check if we need to sync from Firestore
      if (_isAuthenticated) {
        // Check if we have a stored document hash ID for this date
        final storedHashId = _localStorageService.loadString(_generateDateHashKey(date));
        
        // If no local data or no hash ID, try to sync from Firestore
        if (localData == null || storedHashId == null) {
          AppLogger.info('WaterIntakeSyncService: No local data or hash ID found, attempting sync from Firestore');
          final syncSuccess = await syncWaterIntakeFromFirestore(date);
          
          if (syncSuccess) {
            // Reload from local storage after sync
            final syncedData = _localStorageService.loadDailyWaterIntake(date);
            if (syncedData != null) {
              return DailyWaterIntake.fromJson(syncedData);
            }
          }
        }
      }

      // Return local data if available
      if (localData != null) {
        return DailyWaterIntake.fromJson(localData);
      }

      AppLogger.info('WaterIntakeSyncService: No water intake data found for $date');
      return null;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error loading water intake data with sync: $e');
      return null;
    }
  }

  /// Check if current date matches stored hash date and create new document if needed
  Future<bool> validateAndUpdateDateHash() async {
    if (!_isAuthenticated) {
      return true; // Skip validation if not authenticated
    }

    try {
      final today = _getTodayDateString();
      final storedHashId = _localStorageService.loadString(_generateDateHashKey(today));

      // If no hash ID stored for today, we might need to create a new document
      if (storedHashId == null) {
        AppLogger.info('WaterIntakeSyncService: No hash ID found for today, checking if data exists');
        
        // Check if there's local data for today that needs to be synced
        final localData = _localStorageService.loadDailyWaterIntake(today);
        if (localData != null) {
          final dailyIntake = DailyWaterIntake.fromJson(localData);
          await syncWaterIntakeToFirestore(today, dailyIntake);
        }
      }

      return true;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error validating date hash: $e');
      return false;
    }
  }

  /// Clear all water intake sync data
  Future<bool> clearAllWaterIntakeSyncData() async {
    try {
      AppLogger.info('WaterIntakeSyncService: Clearing all water intake sync data');

      // Clear local storage data
      await _localStorageService.clearAllWaterIntakeData();
      await _localStorageService.clearWaterSettings();

      // Clear hash IDs
      final allKeys = _localStorageService.getAllKeys();
      for (final key in allKeys) {
        if (key.startsWith('water_intake_hash_')) {
          await _localStorageService.remove(key);
        }
      }

      AppLogger.info('WaterIntakeSyncService: Successfully cleared all water intake sync data');
      return true;
    } catch (e) {
      AppLogger.error('WaterIntakeSyncService: Error clearing water intake sync data: $e');
      return false;
    }
  }
}

@riverpod
Future<WaterIntakeSyncService> waterIntakeSyncService(Ref ref) async {
  final firestoreService = ref.watch(firestoreServiceProvider);
  final localStorageService = await ref.watch(localStorageServiceProvider.future);

  return WaterIntakeSyncService(
    firestoreService: firestoreService,
    localStorageService: localStorageService,
  );
}
