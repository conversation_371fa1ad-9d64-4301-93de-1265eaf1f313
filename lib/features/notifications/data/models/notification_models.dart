import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_models.freezed.dart';
part 'notification_models.g.dart';

/// Notification action types
enum NotificationActionType {
  none,
  internalNavigation,
  externalLink,
}

/// Notification action data
@freezed
class NotificationAction with _$NotificationAction {
  const factory NotificationAction({
    @Default(NotificationActionType.none) NotificationActionType type,
    String? route,
    String? url,
    Map<String, dynamic>? parameters,
  }) = _NotificationAction;

  factory NotificationAction.fromJson(Map<String, dynamic> json) =>
      _$NotificationActionFromJson(json);
}

/// Individual notification model
@freezed
class AppNotification with _$AppNotification {
  const factory AppNotification({
    required String id,
    required String title,
    required String message,
    required DateTime timestamp,
    @Default(false) bool seen,
    NotificationAction? action,
  }) = _AppNotification;

  factory AppNotification.fromJson(Map<String, dynamic> json) =>
      _$AppNotificationFromJson(json);
}

/// Paginated notifications response
@freezed
class NotificationPage with _$NotificationPage {
  const factory NotificationPage({
    required List<AppNotification> notifications,
    required int totalCount,
    required int currentPage,
    required int pageSize,
    required bool hasMore,
  }) = _NotificationPage;

  factory NotificationPage.fromJson(Map<String, dynamic> json) =>
      _$NotificationPageFromJson(json);
}

/// Notification statistics
@freezed
class NotificationStats with _$NotificationStats {
  const factory NotificationStats({
    @Default(0) int totalCount,
    @Default(0) int unseenCount,
    @Default(0) int todayCount,
  }) = _NotificationStats;

  factory NotificationStats.fromJson(Map<String, dynamic> json) =>
      _$NotificationStatsFromJson(json);
}
