import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:intl/intl.dart';

import '../../../../core/services/local_storage_service.dart';
import '../models/notification_models.dart';

part 'notification_sample_service.g.dart';

class NotificationSampleService {
  final LocalStorageService _localStorageService;

  NotificationSampleService(this._localStorageService);

  /// Add sample notifications for testing
  Future<void> addSampleNotifications() async {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);
    final yesterday = DateFormat('yyyy-MM-dd').format(now.subtract(const Duration(days: 1)));

    // Sample notifications for today
    final todayNotifications = [
      AppNotification(
        id: '1',
        title: 'تذكير بشرب الماء',
        message: 'حان وقت شرب كوب من الماء للحفاظ على ترطيب جسمك',
        timestamp: now.subtract(const Duration(hours: 1)),
        seen: false,
        action: const NotificationAction(
          type: NotificationActionType.internalNavigation,
          route: '/home/<USER>',
        ),
      ),
      AppNotification(
        id: '2',
        title: 'وقت وجبة الغداء',
        message: 'لا تنس تناول وجبة الغداء المخططة لك اليوم',
        timestamp: now.subtract(const Duration(hours: 2)),
        seen: false,
        action: const NotificationAction(
          type: NotificationActionType.internalNavigation,
          route: '/home',
        ),
      ),
      AppNotification(
        id: '3',
        title: 'تحديث خطة الوجبات',
        message: 'تم إنشاء خطة وجبات جديدة لك بناءً على تفضيلاتك المحدثة',
        timestamp: now.subtract(const Duration(hours: 3)),
        seen: true,
      ),
    ];

    // Sample notifications for yesterday
    final yesterdayNotifications = [
      AppNotification(
        id: '4',
        title: 'تسجيل الوزن الأسبوعي',
        message: 'حان وقت تسجيل وزنك الأسبوعي لتتبع تقدمك',
        timestamp: now.subtract(const Duration(days: 1, hours: 2)),
        seen: true,
        action: const NotificationAction(
          type: NotificationActionType.internalNavigation,
          route: '/home/<USER>',
        ),
      ),
      AppNotification(
        id: '5',
        title: 'نصائح غذائية',
        message: 'اكتشف نصائح جديدة لتحسين نظامك الغذائي',
        timestamp: now.subtract(const Duration(days: 1, hours: 4)),
        seen: true,
        action: const NotificationAction(
          type: NotificationActionType.externalLink,
          url: 'https://example.com/nutrition-tips',
        ),
      ),
      AppNotification(
        id: '6',
        title: 'إنجاز رائع!',
        message: 'لقد حققت هدفك في شرب الماء لمدة 7 أيام متتالية',
        timestamp: now.subtract(const Duration(days: 1, hours: 6)),
        seen: true,
      ),
    ];

    // Save notifications
    await _saveNotificationsForDate(today, todayNotifications);
    await _saveNotificationsForDate(yesterday, yesterdayNotifications);

    // Calculate and save stats
    await _calculateAndSaveStats(todayNotifications + yesterdayNotifications);
  }

  /// Save notifications for a specific date
  Future<void> _saveNotificationsForDate(String date, List<AppNotification> notifications) async {
    final notificationJsonList = notifications.map((n) => n.toJson()).toList();
    await _localStorageService.saveNotificationsForDate(date, notificationJsonList);
  }

  /// Calculate and save notification statistics
  Future<void> _calculateAndSaveStats(List<AppNotification> allNotifications) async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    
    int totalCount = allNotifications.length;
    int unseenCount = allNotifications.where((n) => !n.seen).length;
    int todayCount = allNotifications
        .where((n) => DateFormat('yyyy-MM-dd').format(n.timestamp) == today)
        .length;

    final stats = NotificationStats(
      totalCount: totalCount,
      unseenCount: unseenCount,
      todayCount: todayCount,
    );

    await _localStorageService.saveNotificationStats(stats.toJson());
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _localStorageService.clearAllNotifications();
  }

  /// Add a single test notification
  Future<void> addTestNotification({
    required String title,
    required String message,
    NotificationAction? action,
    bool seen = false,
  }) async {
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);
    
    final notification = AppNotification(
      id: now.millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      timestamp: now,
      seen: seen,
      action: action,
    );

    // Load existing notifications for today
    final existingNotifications = _localStorageService.loadNotificationsForDate(today) ?? [];
    
    // Add new notification at the beginning
    existingNotifications.insert(0, notification.toJson());
    
    // Save updated notifications
    await _localStorageService.saveNotificationsForDate(today, existingNotifications);

    // Update stats
    final currentStats = _localStorageService.loadNotificationStats();
    final stats = currentStats != null 
        ? NotificationStats.fromJson(currentStats)
        : const NotificationStats();

    final updatedStats = stats.copyWith(
      totalCount: stats.totalCount + 1,
      unseenCount: stats.unseenCount + (seen ? 0 : 1),
      todayCount: stats.todayCount + 1,
    );

    await _localStorageService.saveNotificationStats(updatedStats.toJson());
  }
}

@riverpod
Future<NotificationSampleService> notificationSampleService(NotificationSampleServiceRef ref) async {
  final localStorageService = await ref.read(localStorageServiceProvider.future);
  return NotificationSampleService(localStorageService);
}
