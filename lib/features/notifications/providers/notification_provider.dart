import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/services/local_storage_service.dart';
import '../data/models/notification_models.dart';
import '../data/services/firestore_notification_service.dart';
import '../providers/notification_metadata_provider.dart';

part 'notification_provider.freezed.dart';
part 'notification_provider.g.dart';

@freezed
class NotificationState with _$NotificationState {
  const factory NotificationState({
    @Default([]) List<AppNotification> notifications,
    @Default(NotificationStats()) NotificationStats stats,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(true) bool hasMore,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    String? error,
  }) = _NotificationState;
}

@riverpod
class NotificationNotifier extends _$NotificationNotifier {
  @override
  NotificationState build() {
    // Return initial state first, then load data asynchronously
    Future.microtask(() {
      _loadNotifications();
      _loadStats();
    });
    return const NotificationState();
  }

  /// Load notifications from Firestore (first page)
  Future<void> _loadNotifications() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final firestoreService = ref.read(firestoreNotificationServiceProvider);
      final notifications = await firestoreService.loadAllNotifications();

      // Apply pagination
      final pageNotifications = notifications.take(state.pageSize).toList();

      state = state.copyWith(
        notifications: pageNotifications,
        isLoading: false,
        hasMore: notifications.length > state.pageSize,
        currentPage: 1,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load notifications: $e',
      );
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications() async {
    if (state.isLoadingMore || !state.hasMore) return;
    
    state = state.copyWith(isLoadingMore: true, error: null);
    
    try {
      final firestoreService = ref.read(firestoreNotificationServiceProvider);
      final allNotifications = await firestoreService.loadAllNotifications();
      
      // Calculate pagination
      final nextPage = state.currentPage + 1;
      final startIndex = (nextPage - 1) * state.pageSize;
      final endIndex = startIndex + state.pageSize;
      
      if (startIndex >= allNotifications.length) {
        state = state.copyWith(isLoadingMore: false, hasMore: false);
        return;
      }
      
      final newNotifications = allNotifications
          .skip(startIndex)
          .take(state.pageSize)
          .toList();
      
      final hasMore = endIndex < allNotifications.length;
      
      state = state.copyWith(
        notifications: [...state.notifications, ...newNotifications],
        isLoadingMore: false,
        hasMore: hasMore,
        currentPage: nextPage,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingMore: false,
        error: 'Failed to load more notifications: $e',
      );
    }
  }

  /// Load notification statistics from Firestore
  Future<void> _loadStats() async {
    try {
      final firestoreService = ref.read(firestoreNotificationServiceProvider);
      final stats = await firestoreService.getNotificationStats();
      state = state.copyWith(stats: stats);
    } catch (e) {
      // Use default stats if loading fails
    }
  }

  /// Calculate and save notification statistics
  Future<void> _calculateAndSaveStats() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final allDates = localStorageService.getAllNotificationDates();
      
      int totalCount = 0;
      int unseenCount = 0;
      int todayCount = 0;
      
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      
      for (final date in allDates) {
        final dateNotifications = localStorageService.loadNotificationsForDate(date);
        if (dateNotifications != null) {
          totalCount += dateNotifications.length;
          
          for (final notificationJson in dateNotifications) {
            final notification = AppNotification.fromJson(notificationJson);
            
            if (!notification.seen) {
              unseenCount++;
            }
            
            if (date == today) {
              todayCount++;
            }
          }
        }
      }
      
      final stats = NotificationStats(
        totalCount: totalCount,
        unseenCount: unseenCount,
        todayCount: todayCount,
      );
      
      // Save stats
      await localStorageService.saveNotificationStats(stats.toJson());
      
      state = state.copyWith(stats: stats);
    } catch (e) {
      // Ignore errors in stats calculation
    }
  }

  /// Mark notification as seen
  Future<void> markNotificationAsSeen(String notificationId) async {
    try {
      final firestoreService = ref.read(firestoreNotificationServiceProvider);

      // Update in Firestore
      await firestoreService.markNotificationAsSeen(notificationId);

      // Find and update the notification in local state
      final updatedNotifications = state.notifications.map((notification) {
        if (notification.id == notificationId && !notification.seen) {
          return notification.copyWith(seen: true);
        }
        return notification;
      }).toList();

      // Update state
      state = state.copyWith(notifications: updatedNotifications);

      // Update stats
      final updatedStats = state.stats.copyWith(
        unseenCount: (state.stats.unseenCount - 1).clamp(0, state.stats.totalCount),
      );
      state = state.copyWith(stats: updatedStats);

      // Update metadata provider to reflect the change in real-time
      ref.read(notificationMetadataNotifierProvider.notifier).decrementUnseenCount();
    } catch (e) {
      state = state.copyWith(error: 'Failed to mark notification as seen: $e');
    }
  }

  /// Update notification in storage
  Future<void> _updateNotificationInStorage(String notificationId, bool seen) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final allDates = localStorageService.getAllNotificationDates();
      
      for (final date in allDates) {
        final dateNotifications = localStorageService.loadNotificationsForDate(date);
        if (dateNotifications != null) {
          bool updated = false;
          final updatedNotifications = dateNotifications.map((notificationJson) {
            final notification = AppNotification.fromJson(notificationJson);
            if (notification.id == notificationId) {
              updated = true;
              return notification.copyWith(seen: seen).toJson();
            }
            return notificationJson;
          }).toList();
          
          if (updated) {
            await localStorageService.saveNotificationsForDate(date, updatedNotifications);
            break;
          }
        }
      }
    } catch (e) {
      // Ignore storage update errors
    }
  }

  /// Add a new notification (for testing purposes)
  Future<void> addNotification(AppNotification notification) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      
      // Load existing notifications for today
      final existingNotifications = localStorageService.loadNotificationsForDate(today) ?? [];
      
      // Add new notification
      existingNotifications.insert(0, notification.toJson());
      
      // Save updated notifications
      await localStorageService.saveNotificationsForDate(today, existingNotifications);
      
      // Update state
      final updatedNotifications = [notification, ...state.notifications];
      state = state.copyWith(notifications: updatedNotifications);
      
      // Update stats
      final updatedStats = state.stats.copyWith(
        totalCount: state.stats.totalCount + 1,
        unseenCount: state.stats.unseenCount + (notification.seen ? 0 : 1),
        todayCount: state.stats.todayCount + 1,
      );
      state = state.copyWith(stats: updatedStats);
      
      // Save updated stats
      await localStorageService.saveNotificationStats(updatedStats.toJson());
    } catch (e) {
      state = state.copyWith(error: 'Failed to add notification: $e');
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications() async {
    state = state.copyWith(
      notifications: [],
      currentPage: 1,
      hasMore: true,
    );
    await _loadNotifications();
    await _loadStats();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for unseen notification count
@riverpod
int unseenNotificationCount(Ref ref) {
  final notificationState = ref.watch(notificationNotifierProvider);
  return notificationState.stats.unseenCount;
}

/// Provider for checking if badge should be shown
@riverpod
bool shouldShowNotificationBadge(Ref ref) {
  final unseenCount = ref.watch(unseenNotificationCountProvider);
  return unseenCount > 0;
}

/// Provider for getting a specific notification by ID
@riverpod
AppNotification? getNotificationById(Ref ref, String notificationId) {
  final notificationState = ref.watch(notificationNotifierProvider);
  try {
    return notificationState.notifications.firstWhere(
      (notification) => notification.id == notificationId,
    );
  } catch (e) {
    return null;
  }
}
