import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../data/models/notification_models.dart';
import '../../providers/notification_provider.dart';

class NotificationDetailPage extends ConsumerStatefulWidget {
  const NotificationDetailPage({
    super.key,
    required this.notificationId,
  });

  final String notificationId;

  @override
  ConsumerState<NotificationDetailPage> createState() => _NotificationDetailPageState();
}

class _NotificationDetailPageState extends ConsumerState<NotificationDetailPage> {
  @override
  void initState() {
    super.initState();
    // Mark notification as seen when opening detail page
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationNotifierProvider.notifier).markNotificationAsSeen(widget.notificationId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final notification = ref.watch(getNotificationByIdProvider(widget.notificationId));

    if (notification == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('الإشعار'),
          backgroundColor: Colors.white,
          foregroundColor: AppColors.textPrimary,
          elevation: 0,
        ),
        body: const Center(
          child: Text('لم يتم العثور على الإشعار'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الإشعار'),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      backgroundColor: AppColors.background,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification header card
            _buildHeaderCard(notification),
            
            const SizedBox(height: 20),
            
            // Notification content card
            _buildContentCard(notification),
            
            const SizedBox(height: 20),
            
            // Action button (if notification has action)
            if (notification.action != null && 
                notification.action!.type != NotificationActionType.none)
              _buildActionCard(notification.action!),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(AppNotification notification) {
    final dateFormat = DateFormat('EEEE، d MMMM yyyy - HH:mm', 'ar');
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Notification icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: _getNotificationIconColor(notification).withOpacity(0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                _getNotificationIcon(notification),
                color: _getNotificationIconColor(notification),
                size: 30,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Notification info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    dateFormat.format(notification.timestamp),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Row(
                    children: [
                      Icon(
                        notification.seen ? Icons.mark_email_read : Icons.mark_email_unread,
                        size: 16,
                        color: notification.seen ? Colors.green : AppColors.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        notification.seen ? 'تم القراءة' : 'غير مقروء',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: notification.seen ? Colors.green : AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard(AppNotification notification) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'محتوى الإشعار',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              notification.message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textPrimary,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(NotificationAction action) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراء مقترح',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              _getActionDescription(action),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: 16),
            
            CustomButton(
              text: _getActionButtonText(action),
              onPressed: () => _executeAction(action),
              icon: Icon(_getActionIcon(action)),
              backgroundColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getNotificationIcon(AppNotification notification) {
    if (notification.title.contains('وجبة') || notification.title.contains('طعام')) {
      return Icons.restaurant;
    } else if (notification.title.contains('ماء') || notification.title.contains('شرب')) {
      return Icons.water_drop;
    } else if (notification.title.contains('وزن')) {
      return Icons.monitor_weight;
    } else if (notification.title.contains('تذكير')) {
      return Icons.alarm;
    }
    return Icons.notifications;
  }

  Color _getNotificationIconColor(AppNotification notification) {
    if (notification.title.contains('وجبة') || notification.title.contains('طعام')) {
      return AppColors.primary;
    } else if (notification.title.contains('ماء') || notification.title.contains('شرب')) {
      return Colors.blue;
    } else if (notification.title.contains('وزن')) {
      return Colors.purple;
    } else if (notification.title.contains('تذكير')) {
      return Colors.orange;
    }
    return AppColors.primary;
  }

  String _getActionDescription(NotificationAction action) {
    switch (action.type) {
      case NotificationActionType.internalNavigation:
        return 'انتقل إلى الصفحة المرتبطة بهذا الإشعار';
      case NotificationActionType.externalLink:
        return 'افتح الرابط الخارجي في المتصفح';
      case NotificationActionType.none:
        return '';
    }
  }

  String _getActionButtonText(NotificationAction action) {
    switch (action.type) {
      case NotificationActionType.internalNavigation:
        return 'انتقل إلى الصفحة';
      case NotificationActionType.externalLink:
        return 'افتح الرابط';
      case NotificationActionType.none:
        return '';
    }
  }

  IconData _getActionIcon(NotificationAction action) {
    switch (action.type) {
      case NotificationActionType.internalNavigation:
        return Icons.arrow_forward;
      case NotificationActionType.externalLink:
        return Icons.open_in_new;
      case NotificationActionType.none:
        return Icons.info;
    }
  }

  void _executeAction(NotificationAction action) {
    switch (action.type) {
      case NotificationActionType.internalNavigation:
        if (action.route != null) {
          context.push(action.route!);
        }
        break;
      case NotificationActionType.externalLink:
        if (action.url != null) {
          _launchUrl(action.url!);
        }
        break;
      case NotificationActionType.none:
        break;
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح الرابط'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
