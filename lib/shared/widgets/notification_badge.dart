import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/app_colors.dart';
import '../../features/notifications/providers/notification_provider.dart';
import '../../features/notifications/providers/notification_metadata_provider.dart';

class NotificationBadge extends ConsumerWidget {
  const NotificationBadge({
    super.key,
    required this.child,
    this.showZero = false,
    this.badgeColor,
    this.textColor,
    this.fontSize = 12,
    this.offset = const Offset(-4, 4), // Changed to top-left positioning
  });

  /// Constructor specifically for app bar usage with better positioning
  const NotificationBadge.appBar({
    super.key,
    required this.child,
    this.showZero = false,
    this.badgeColor,
    this.textColor,
    this.fontSize = 10,
    this.offset = const Offset(2, 2), // Better positioning for app bar
  });

  final Widget child;
  final bool showZero;
  final Color? badgeColor;
  final Color? textColor;
  final double fontSize;
  final Offset offset;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use metadata provider for real-time updates from Firestore
    final unseenCount = ref.watch(unseenNotificationCountFromMetadataProvider);
    final shouldShow = ref.watch(shouldShowNotificationBadgeFromMetadataProvider);

    if (!shouldShow && !showZero) {
      return child;
    }

    // Determine if this is an app bar badge based on offset values
    final isAppBarBadge = offset.dx >= 0 && offset.dy >= 0;

    return Padding(
      padding: isAppBarBadge
          ? const EdgeInsets.only(left: 8, top: 4) // Less padding for app bar
          : const EdgeInsets.only(left: 12, top: 8), // More padding for general use
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          child,
        if (shouldShow || (showZero && unseenCount == 0))
          Positioned(
            left: offset.dx.abs(), // Use absolute value for left positioning
            top: offset.dy.abs(), // Use absolute value for top positioning
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: badgeColor ?? Colors.red,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
              ),
              constraints: const BoxConstraints(
                minWidth: 18,
                minHeight: 18,
              ),
              child: Text(
                unseenCount > 99 ? '99+' : unseenCount.toString(),
                style: TextStyle(
                  color: textColor ?? Colors.white,
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class NotificationIconButton extends ConsumerWidget {
  const NotificationIconButton({
    super.key,
    required this.onPressed,
    this.icon = Icons.notifications,
    this.iconSize = 24,
    this.iconColor,
    this.badgeColor,
    this.showZero = false,
  });

  final VoidCallback onPressed;
  final IconData icon;
  final double iconSize;
  final Color? iconColor;
  final Color? badgeColor;
  final bool showZero;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return NotificationBadge.appBar(
      badgeColor: badgeColor,
      showZero: showZero,
      child: IconButton(
        icon: Icon(
          icon,
          size: iconSize,
          color: iconColor,
        ),
        onPressed: onPressed,
      ),
    );
  }
}
