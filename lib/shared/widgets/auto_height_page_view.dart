import 'package:flutter/material.dart';

/// A PageView widget that automatically adjusts its height based on the current page content.
/// 
/// This widget measures the height of each page and animates smoothly between different heights
/// when the user swipes between pages.
class AutoHeightPageView extends StatefulWidget {
  /// The controller for the PageView
  final PageController? controller;
  
  /// Called when the page changes
  final ValueChanged<int>? onPageChanged;
  
  /// The number of pages
  final int itemCount;
  
  /// Builder function for each page
  final IndexedWidgetBuilder itemBuilder;
  
  /// Animation duration for height changes
  final Duration animationDuration;
  
  /// Animation curve for height changes
  final Curve animationCurve;
  
  /// Initial page index
  final int initialPage;
  
  /// Whether to enable page snapping
  final bool pageSnapping;
  
  /// Scroll direction
  final Axis scrollDirection;
  
  /// Physics for the PageView
  final ScrollPhysics? physics;

  const AutoHeightPageView({
    super.key,
    this.controller,
    this.onPageChanged,
    required this.itemCount,
    required this.itemBuilder,
    this.animationDuration = const Duration(milliseconds: 400),
    this.animationCurve = Curves.easeInOut,
    this.initialPage = 0,
    this.pageSnapping = true,
    this.scrollDirection = Axis.horizontal,
    this.physics,
  });

  @override
  State<AutoHeightPageView> createState() => _AutoHeightPageViewState();
}

class _AutoHeightPageViewState extends State<AutoHeightPageView> {
  late PageController _pageController;
  late int _currentPage;
  
  /// Map to store the height of each page
  final Map<int, double> _pageHeights = {};
  
  /// Map to store GlobalKeys for each page
  final Map<int, GlobalKey> _pageKeys = {};
  
  /// Current height of the PageView
  double _currentHeight = 730.0; // Default height - increased to accommodate content
  
  /// Whether we're currently measuring heights
  bool _isMeasuring = false;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _pageController = widget.controller ?? PageController(initialPage: widget.initialPage);
    
    // Initialize keys for all pages
    for (int i = 0; i < widget.itemCount; i++) {
      _pageKeys[i] = GlobalKey();
    }
    
    // Measure heights after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureAllPageHeights();
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _pageController.dispose();
    }
    super.dispose();
  }

  /// Measure the height of all pages
  void _measureAllPageHeights() {
    if (_isMeasuring) return;
    
    setState(() {
      _isMeasuring = true;
    });
    
    // Measure current page first
    _measurePageHeight(_currentPage);
    
    // Then measure other pages
    for (int i = 0; i < widget.itemCount; i++) {
      if (i != _currentPage) {
        _measurePageHeight(i);
      }
    }
    
    setState(() {
      _isMeasuring = false;
    });
  }

  /// Measure the height of a specific page
  void _measurePageHeight(int pageIndex) {
    final key = _pageKeys[pageIndex];
    if (key?.currentContext != null) {
      final RenderBox? renderBox = key!.currentContext!.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final height = renderBox.size.height;
        if (height > 0) {
          _pageHeights[pageIndex] = height;
          
          // Update current height if this is the current page
          if (pageIndex == _currentPage) {
            setState(() {
              _currentHeight = height;
            });
          }
        }
      }
    }
  }

  /// Handle page change
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
      // Update height to the new page's height
      _currentHeight = _pageHeights[page] ?? _currentHeight;
    });
    
    // Measure the new page height if not already measured
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_pageHeights.containsKey(page)) {
        _measurePageHeight(page);
      }
    });
    
    widget.onPageChanged?.call(page);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: widget.animationDuration,
      curve: widget.animationCurve,
      height: _currentHeight,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        itemCount: widget.itemCount,
        pageSnapping: widget.pageSnapping,
        scrollDirection: widget.scrollDirection,
        physics: widget.physics,
        itemBuilder: (context, index) {
          return _MeasurablePageWrapper(
            key: _pageKeys[index],
            onHeightChanged: (height) {
              _pageHeights[index] = height;
              if (index == _currentPage) {
                setState(() {
                  _currentHeight = height;
                });
              }
            },
            child: widget.itemBuilder(context, index),
          );
        },
      ),
    );
  }
}

/// A wrapper widget that measures its child's height and reports changes
class _MeasurablePageWrapper extends StatefulWidget {
  final Widget child;
  final ValueChanged<double> onHeightChanged;

  const _MeasurablePageWrapper({
    super.key,
    required this.child,
    required this.onHeightChanged,
  });

  @override
  State<_MeasurablePageWrapper> createState() => _MeasurablePageWrapperState();
}

class _MeasurablePageWrapperState extends State<_MeasurablePageWrapper> {
  double? _lastHeight;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureHeight();
    });
  }

  void _measureHeight() {
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null && renderBox.hasSize) {
      final height = renderBox.size.height;
      if (height != _lastHeight && height > 0) {
        _lastHeight = height;
        widget.onHeightChanged(height);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureHeight();
    });

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          child: widget.child,
        );
      },
    );
  }
}
