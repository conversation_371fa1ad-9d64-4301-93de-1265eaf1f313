import 'package:firebase_auth/firebase_auth.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/user_profile/data/models/user_profile.dart';
import 'package:easydietai/features/onboarding/data/models/plan_preferences.dart';
import 'package:easydietai/features/user_profile/data/models/notification_settings_models.dart';
import 'package:easydietai/shared/services/app_service.dart';

part 'app_state_provider.freezed.dart';
part 'app_state_provider.g.dart';

/// Authentication status enum
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Global app state with authentication
@freezed
class AppState with _$AppState {
  const factory AppState({
    // App state
    @Default(false) bool isInitialized,
    @Default(false) bool isOnboardingCompleted,
    @Default('ar') String currentLanguage,
    @Default('system') String currentTheme,
    @Default(true) bool isFirstLaunch,
    @Default(false) bool isGuestUser,
    @Default(false) bool hasSeenIntroWizard,
    // Authentication state
    User? user,
    UserProfile? userProfile,
    NotificationSettingsData? notificationSettingsData, // Comprehensive notification settings
    PlanPreferences? planPreferences,
    @Default(AuthStatus.initial) AuthStatus authStatus,
    String? authErrorMessage,
  }) = _AppState;
}

/// App state notifier with authentication
@riverpod
class AppStateNotifier extends _$AppStateNotifier {
  late final FirebaseAuth _firebaseAuth;
  late final GoogleSignIn _googleSignIn;
  late final LocalStorageService _storageService;



  @override
  AppState build() {
    _firebaseAuth = FirebaseAuth.instance;
    _googleSignIn = GoogleSignIn(
      // Client ID for web - automatically uses meta tag from index.html
      // For Android, it uses the google-services.json configuration
      scopes: ['email'],
    );
    // Initialize storage service and app service
    _initializeServices();

    AppLogger.info('AppStateNotifier: Initializing app state provider');

    // Listen to auth state changes
    _firebaseAuth.authStateChanges().listen((user) {
      AppLogger.info('AppStateNotifier: Auth state changed - User: ${user?.uid ?? 'null'}');
      if (user != null) {
        AppLogger.info('AppStateNotifier: User authenticated, loading profile');
        // Only load profile if we don't already have this user or if status is not already authenticated
        if (state.user?.uid != user.uid || state.authStatus != AuthStatus.authenticated) {
          _loadUserProfile(user);
        }
      } else {
        AppLogger.info('AppStateNotifier: User not authenticated');
        // Only update state if we're not in the middle of a loading operation
        if (state.authStatus != AuthStatus.loading) {
          state = state.copyWith(authStatus: AuthStatus.unauthenticated);
        }
      }
    });

    // Initialize with default values and then load from storage asynchronously
    Future.microtask(() => _initializeFromStorage());

    // Check current user on initialization
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      AppLogger.info('AppStateNotifier: Found existing user on init: ${currentUser.uid}');
      // Don't call _loadUserProfile here to avoid race condition
      // The authStateChanges listener will handle it
      return AppState(
        isInitialized: false, // Will be set to true after loading from storage
        isOnboardingCompleted: false,
        currentLanguage: 'ar',
        currentTheme: 'system',
        isFirstLaunch: true,
        isGuestUser: false,
        hasSeenIntroWizard: false,
        user: currentUser,
        authStatus: AuthStatus.authenticated,
      );
    }

    AppLogger.info('AppStateNotifier: No existing user found');
    return const AppState(
      isInitialized: false, // Will be set to true after loading from storage
      isOnboardingCompleted: false,
      currentLanguage: 'ar',
      currentTheme: 'system',
      isFirstLaunch: true,
      isGuestUser: false,
      hasSeenIntroWizard: false,
      authStatus: AuthStatus.unauthenticated,
    );
  }

  /// Initialize services asynchronously
  Future<void> _initializeServices() async {
    try {
      _storageService = await ref.read(localStorageServiceProvider.future);
    } catch (e) {
      AppLogger.error('Failed to initialize services: $e');
    }
  }

  /// Initialize app state from storage
  Future<void> _initializeFromStorage() async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.initializeFromStorage();

      if (result.success) {
        final appStateData = result.data as AppStateData;

        // Determine if this is first launch
        final isFirstLaunch = !appStateData.hasSeenIntroWizard && !appStateData.isOnboardingCompleted;

        // Update state with loaded values
        state = state.copyWith(
          isInitialized: true,
          isOnboardingCompleted: appStateData.isOnboardingCompleted,
          hasSeenIntroWizard: appStateData.hasSeenIntroWizard,
          isGuestUser: appStateData.isGuestUser,
          currentLanguage: appStateData.currentLanguage,
          currentTheme: appStateData.currentTheme,
          isFirstLaunch: isFirstLaunch,
        );
      } else {
        // If loading fails, use defaults but mark as initialized
        state = state.copyWith(isInitialized: true);
        AppLogger.error('AppStateNotifier: Failed to initialize from storage: ${result.error}');
      }
    } catch (e) {
      // If loading fails, use defaults but mark as initialized
      state = state.copyWith(isInitialized: true);
      AppLogger.error('AppStateNotifier: Error during initialization: $e');
    }
  }

  /// Initialize authentication state
  Future<void> initializeAuth() async {
    state = state.copyWith(authStatus: AuthStatus.loading);

    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      await _loadUserProfile(currentUser);
    } else {
      state = state.copyWith(authStatus: AuthStatus.unauthenticated);
    }
  }

  /// Public initialize method for manual initialization if needed
  Future<void> initialize() async {
    await _initializeFromStorage();
  }

  /// Load user profile from storage or Firestore
  Future<void> _loadUserProfile(User user) async {
    try {
      AppLogger.info('AppStateNotifier: Loading user profile for ${user.uid}');

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.loadCompleteUserData(user);

      if (result.success) {
        final appStateData = result.data as AppStateData;

        AppLogger.info('AppStateNotifier: Setting auth state to authenticated');
        AppLogger.info('AppStateNotifier: Profile onboardingCompleted status: ${appStateData.userProfile?.onboardingCompleted}');

        // Clear guest user status for authenticated non-anonymous users
        if (!user.isAnonymous) {
          AppLogger.info('AppStateNotifier: User is not anonymous, clearing guest user status');
          try {
            // Clear the is_guest_user flag from storage
            await _storageService.remove('is_guest_user');

            // Update app state to reflect user is no longer a guest
            await setGuestUser(false);

            AppLogger.info('AppStateNotifier: Guest user status cleared successfully');
          } catch (e) {
            AppLogger.warning('AppStateNotifier: Failed to clear guest user status: $e');
            // Don't throw error as this shouldn't break the login flow
          }
        }

        state = state.copyWith(
          user: user,
          userProfile: appStateData.userProfile,
          notificationSettingsData: appStateData.notificationSettingsData,
          planPreferences: appStateData.planPreferences,
          authStatus: AuthStatus.authenticated,
          authErrorMessage: null,
        );

        // Sync local app state if user has completed onboarding
        if (appStateData.userProfile?.onboardingCompleted == true) {
          AppLogger.info('AppStateNotifier: User has completed onboarding, syncing local app state');
          try {
            AppLogger.info('AppStateNotifier: Current app state onboarding completed: ${state.isOnboardingCompleted}');

            if (!state.isOnboardingCompleted) {
              AppLogger.info('AppStateNotifier: Marking onboarding as completed in app state');
              await completeOnboarding();
            }
          } catch (e) {
            AppLogger.warning('AppStateNotifier: Failed to sync local app state: $e');
            // Don't throw error as this shouldn't break the login flow
          }

          // Sync Firestore data to local storage after successful login
          // await _syncFirestoreDataToLocalStorage();
        } else {
          AppLogger.info('AppStateNotifier: User has NOT completed onboarding, skipping local app state sync');
        }

        AppLogger.info('AppStateNotifier: Auth state updated - Status: ${state.authStatus}');
      } else {
        AppLogger.warning('AppStateNotifier: Failed to load user profile: ${result.error}');
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      AppLogger.warning('AppStateNotifier: Error loading user profile: $e');
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: e.toString(),
      );
    }
  }

  /// Complete onboarding
  Future<void> completeOnboarding() async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.completeOnboarding();

      if (result.success) {
        // Initialize default notification settings for new users
        if (state.notificationSettingsData == null) {
          await initializeDefaultNotificationSettings();
        }

        state = state.copyWith(
          isOnboardingCompleted: true,
          isFirstLaunch: false,
        );
      } else {
        AppLogger.error('AppStateNotifier: Failed to complete onboarding: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error completing onboarding: $e');
    }
  }

  /// Set user as guest
  Future<void> setGuestUser(bool isGuest) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.setGuestUser(isGuest: isGuest);

      if (result.success) {
        state = state.copyWith(isGuestUser: isGuest);
      } else {
        AppLogger.error('AppStateNotifier: Failed to set guest user: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error setting guest user: $e');
    }
  }

  /// Mark intro wizard as completed
  Future<void> completeIntroWizard() async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.completeIntroWizard();

      if (result.success) {
        state = state.copyWith(
          hasSeenIntroWizard: true,
          isFirstLaunch: false,
        );
      } else {
        AppLogger.error('AppStateNotifier: Failed to complete intro wizard: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error completing intro wizard: $e');
    }
  }

  /// Mark app as initialized (called after splash screen)
  Future<void> markAsInitialized() async {
    state = state.copyWith(isInitialized: true);
  }

  /// Change language
  Future<void> changeLanguage(String languageCode) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.changeLanguage(languageCode);

      if (result.success) {
        state = state.copyWith(currentLanguage: languageCode);
      } else {
        AppLogger.error('AppStateNotifier: Failed to change language: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error changing language: $e');
    }
  }

  /// Change theme
  Future<void> changeTheme(String theme) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.changeTheme(theme);

      if (result.success) {
        state = state.copyWith(currentTheme: theme);
      } else {
        AppLogger.error('AppStateNotifier: Failed to change theme: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error changing theme: $e');
    }
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(authStatus: AuthStatus.loading);

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.signInWithEmailAndPassword(email, password);

      if (result.success) {
        final appStateData = result.data as AppStateData;
        _updateStateFromAppData(appStateData, AuthStatus.authenticated);
      } else {
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(authStatus: AuthStatus.loading);

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.signUpWithEmailAndPassword(email, password);

      if (result.success) {
        final appStateData = result.data as AppStateData;
        _updateStateFromAppData(appStateData, AuthStatus.authenticated);
      } else {
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Sign in as guest
  Future<void> signInAsGuest() async {
    try {
      state = state.copyWith(authStatus: AuthStatus.loading);

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.signInAsGuest();

      if (result.success) {
        final appStateData = result.data as AppStateData;
        _updateStateFromAppData(appStateData, AuthStatus.authenticated);
      } else {
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'An unexpected error occurred: $e',
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.signOut();

      if (result.success) {
        state = state.copyWith(
          authStatus: AuthStatus.unauthenticated,
          user: null,
          userProfile: null,
          notificationSettingsData: null,
          planPreferences: null,
          authErrorMessage: null,
          isOnboardingCompleted: false,
          isGuestUser: false,
        );
      } else {
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'Failed to sign out: $e',
      );
    }
  }

  /// Reset app state (for logout)
  Future<void> reset() async {
    await signOut();
  }
  /// Update user profile using new Firestore structure
  Future<void> updateUserProfile(UserProfile updatedProfile, {bool isOnboardingCompletion = false}) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.updateUserProfile(updatedProfile, isOnboardingCompletion: isOnboardingCompletion);

      if (result.success) {
        // Update local state
        state = state.copyWith(userProfile: updatedProfile);

        // Extract and store onboarding completion status separately for quick access
        await _storageService.setOnboardingCompleted(updatedProfile.onboardingCompleted);

        AppLogger.info('AppStateNotifier: User profile updated successfully using new structure (onboarding: $isOnboardingCompletion)');
      } else {
        AppLogger.error('AppStateNotifier: Failed to update profile: ${result.error}');
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Failed to update profile: $e');
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'Failed to update profile',
      );
    }
  }



  /// Update comprehensive notification settings data
  Future<void> updateNotificationSettingsData(NotificationSettingsData updatedSettings) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.updateNotificationSettings(updatedSettings);

      if (result.success) {
        // Update local state
        state = state.copyWith(notificationSettingsData: updatedSettings);
        AppLogger.info('AppStateNotifier: Comprehensive notification settings updated successfully');
      } else {
        AppLogger.error('AppStateNotifier: Failed to update comprehensive notification settings: ${result.error}');
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Failed to update comprehensive notification settings: $e');
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'Failed to update notification settings',
      );
    }
  }

  /// Initialize default notification settings for new users
  Future<void> initializeDefaultNotificationSettings() async {
    try {
      AppLogger.info('AppStateNotifier: Initializing default notification settings');

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.initializeDefaultNotificationSettings();

      if (result.success) {
        final defaultSettings = result.data as NotificationSettingsData;
        // Update state
        state = state.copyWith(notificationSettingsData: defaultSettings);
        AppLogger.info('AppStateNotifier: Default notification settings initialized');
      } else {
        AppLogger.error('AppStateNotifier: Failed to initialize default notification settings: ${result.error}');
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Failed to initialize default notification settings: $e');
    }
  }

  /// Update plan preferences
  Future<void> updatePlanPreferences(PlanPreferences updatedPreferences) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.updatePlanPreferences(updatedPreferences);

      if (result.success) {
        // Update local state
        state = state.copyWith(planPreferences: updatedPreferences);
        AppLogger.info('AppStateNotifier: Plan preferences updated successfully');
      } else {
        AppLogger.error('AppStateNotifier: Failed to update plan preferences: ${result.error}');
        state = state.copyWith(
          authStatus: AuthStatus.error,
          authErrorMessage: result.error,
        );
      }
    } catch (e) {
      AppLogger.error('AppStateNotifier: Failed to update plan preferences: $e');
      state = state.copyWith(
        authStatus: AuthStatus.error,
        authErrorMessage: 'Failed to update plan preferences',
      );
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.resetPassword(email);

      if (!result.success) {
        throw result.error ?? 'Failed to reset password';
      }
    } catch (e) {
      throw e.toString();
    }
  }

  /// Clear error message
  void clearAuthError() {
    state = state.copyWith(authErrorMessage: null);
  }

  /// Helper method to update state from AppStateData
  void _updateStateFromAppData(AppStateData appStateData, AuthStatus authStatus) {
    final currentUser = _firebaseAuth.currentUser;
    state = state.copyWith(
      user: currentUser,
      userProfile: appStateData.userProfile,
      notificationSettingsData: appStateData.notificationSettingsData,
      planPreferences: appStateData.planPreferences,
      authStatus: authStatus,
      authErrorMessage: null,
    );
  }

  /// Sync Firestore data to local storage after login
  Future<void> _syncFirestoreDataToLocalStorage() async {
    try {
      AppLogger.info('AppStateNotifier: Syncing Firestore data to local storage');

      final appService = await ref.read(appServiceProvider.future);
      final result = await appService.syncFirestoreDataToLocalStorage();

      if (result.success) {
        AppLogger.info('AppStateNotifier: Firestore data sync to local storage completed');
      } else {
        AppLogger.warning('AppStateNotifier: Failed to sync Firestore data to local storage: ${result.error}');
      }
    } catch (e) {
      AppLogger.warning('AppStateNotifier: Failed to sync Firestore data to local storage: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }



  /// Manually sync Firestore data after login
  /// This can be called from login widgets to ensure data is synced
  /// Note: This is now mainly for manual refresh scenarios since automatic sync
  /// happens in _loadUserProfile during authentication
  Future<void> syncFirestoreData() async {
    try {
      final user = state.user;
      if (user == null) {
        AppLogger.warning('AppStateNotifier: Cannot sync Firestore data - no authenticated user');
        return;
      }

      AppLogger.info('AppStateNotifier: Manually syncing Firestore data for ${user.uid}');

      // Reload the user profile to get the latest data
      await _loadUserProfile(user);

      AppLogger.info('AppStateNotifier: Manual Firestore data sync completed');
    } catch (e) {
      AppLogger.error('AppStateNotifier: Error syncing Firestore data: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }
}

/// Provider for checking if app is ready
@riverpod
bool isAppReady(IsAppReadyRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isInitialized;
}

/// Provider for checking if onboarding is needed
@riverpod
bool needsOnboarding(NeedsOnboardingRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isInitialized && !appState.isOnboardingCompleted;
}

/// Provider for checking if welcome screen should be shown
@riverpod
bool needsWelcome(NeedsWelcomeRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isInitialized && appState.isFirstLaunch;
}

/// Provider for checking if user is guest
@riverpod
bool isGuestUser(IsGuestUserRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isGuestUser;
}

/// Provider for checking if intro wizard should be shown
@riverpod
bool needsIntroWizard(NeedsIntroWizardRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isInitialized && appState.isFirstLaunch && !appState.hasSeenIntroWizard;
}

/// Provider for current language
@riverpod
String currentLanguage(CurrentLanguageRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.currentLanguage;
}

/// Provider for current theme
@riverpod
String currentTheme(CurrentThemeRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.currentTheme;
}

/// Provider for first launch check
@riverpod
bool isFirstLaunch(IsFirstLaunchRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isFirstLaunch;
}

/// Provider for current user
@riverpod
User? currentUser(CurrentUserRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.user;
}

/// Provider for current user profile
@riverpod
UserProfile? currentUserProfile(CurrentUserProfileRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.userProfile;
}

/// Provider for current comprehensive notification settings data
@riverpod
NotificationSettingsData? currentNotificationSettingsData(CurrentNotificationSettingsDataRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.notificationSettingsData;
}

/// Provider for current plan preferences
@riverpod
PlanPreferences? currentPlanPreferences(CurrentPlanPreferencesRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.planPreferences;
}

/// Provider for authentication status
@riverpod
AuthStatus authStatus(AuthStatusRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.authStatus;
}

/// Provider for checking if user is authenticated
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final appState = ref.watch(appStateNotifierProvider);
  final isAuth = appState.authStatus == AuthStatus.authenticated;
  AppLogger.info('isAuthenticated: Status=${appState.authStatus}, User=${appState.user?.uid ?? 'null'}, Result=$isAuth');
  return isAuth;
}
