import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/features/user_profile/data/models/user_profile.dart';
import 'package:easydietai/features/onboarding/data/models/plan_preferences.dart';
import 'package:easydietai/features/meal_planning/data/services/meal_plan_service.dart';
import 'package:easydietai/features/shopping_list/data/services/shopping_list_sync_service.dart';
import 'package:easydietai/features/user_profile/data/models/notification_settings_models.dart';
import 'package:easydietai/features/water_tracking/data/services/water_intake_sync_service.dart';
import 'package:easydietai/features/weight_tracking/data/services/weight_tracking_sync_service.dart';
import 'package:easydietai/features/user_profile/data/services/user_data_service.dart';

/// Provider for app service
final appServiceProvider = FutureProvider<AppService>((ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final userDataService = ref.watch(userDataServiceProvider);
  final mealPlanService = await ref.watch(mealPlanServiceProvider.future);
  final waterIntakeSyncService = await ref.watch(waterIntakeSyncServiceProvider.future);
  final weightTrackingSyncService = await ref.watch(weightTrackingSyncServiceProvider.future);
  final shoppingListSyncService = ref.watch(shoppingListSyncServiceProvider);

  return FirebaseAppService(
    localStorageService,
    userDataService,
    mealPlanService,
    waterIntakeSyncService,
    weightTrackingSyncService,
    shoppingListSyncService,
  );
});

/// Result class for app operations
class AppOperationResult {
  const AppOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// App state data model for service operations
class AppStateData {
  const AppStateData({
    this.userProfile,
    this.notificationSettingsData,
    this.planPreferences,
    this.isOnboardingCompleted = false,
    this.hasSeenIntroWizard = false,
    this.isGuestUser = false,
    this.currentLanguage = 'ar',
    this.currentTheme = 'system',
  });

  final UserProfile? userProfile;
  final NotificationSettingsData? notificationSettingsData;
  final PlanPreferences? planPreferences;
  final bool isOnboardingCompleted;
  final bool hasSeenIntroWizard;
  final bool isGuestUser;
  final String currentLanguage;
  final String currentTheme;

  AppStateData copyWith({
    UserProfile? userProfile,
    NotificationSettingsData? notificationSettingsData,
    PlanPreferences? planPreferences,
    bool? isOnboardingCompleted,
    bool? hasSeenIntroWizard,
    bool? isGuestUser,
    String? currentLanguage,
    String? currentTheme,
  }) {
    return AppStateData(
      userProfile: userProfile ?? this.userProfile,
      notificationSettingsData: notificationSettingsData ?? this.notificationSettingsData,
      planPreferences: planPreferences ?? this.planPreferences,
      isOnboardingCompleted: isOnboardingCompleted ?? this.isOnboardingCompleted,
      hasSeenIntroWizard: hasSeenIntroWizard ?? this.hasSeenIntroWizard,
      isGuestUser: isGuestUser ?? this.isGuestUser,
      currentLanguage: currentLanguage ?? this.currentLanguage,
      currentTheme: currentTheme ?? this.currentTheme,
    );
  }
}

/// Abstract app service interface
abstract class AppService {
  // Initialization operations
  Future<AppOperationResult> initializeFromStorage();
  Future<AppOperationResult> loadCompleteUserData(User user);
  
  // Authentication operations
  Future<AppOperationResult> signInWithEmailAndPassword(String email, String password);
  Future<AppOperationResult> signUpWithEmailAndPassword(String email, String password);
  Future<AppOperationResult> signInAsGuest();
  Future<AppOperationResult> signOut();
  
  // Profile management operations
  Future<AppOperationResult> updateUserProfile(UserProfile updatedProfile, {bool isOnboardingCompletion = false});
  Future<AppOperationResult> updateNotificationSettings(NotificationSettingsData updatedSettings);
  Future<AppOperationResult> updatePlanPreferences(PlanPreferences updatedPreferences);
  
  // App state operations
  Future<AppOperationResult> completeOnboarding();
  Future<AppOperationResult> setGuestUser({required bool isGuest});
  Future<AppOperationResult> completeIntroWizard();
  Future<AppOperationResult> changeLanguage(String languageCode);
  Future<AppOperationResult> changeTheme(String theme);
  
  // Data synchronization operations
  Future<AppOperationResult> syncFirestoreDataToLocalStorage();
  Future<AppOperationResult> syncAllFeatureData();
  Future<AppOperationResult> clearUserData();
  
  // Utility operations
  Future<AppOperationResult> resetPassword(String email);
  Future<AppOperationResult> initializeDefaultNotificationSettings();
  
  // Data extraction operations (deprecated - will be removed)
  UserProfile extractCoreUserProfile(UserProfile firestoreProfile);
  PlanPreferences? parsePlanPreferences(UserProfile firestoreProfile);
  NotificationSettingsData? extractNotificationSettingsFromProfile(UserProfile firestoreProfile);
  Future<NotificationSettingsData?> extractNotificationSettingsFromFirestore();
}

/// Firebase implementation of app service
class FirebaseAppService implements AppService {
  FirebaseAppService(
    this._localStorageService,
    this._userDataService,
    this._mealPlanService,
    this._waterIntakeSyncService,
    this._weightTrackingSyncService,
    this._shoppingListSyncService,
  );

  final LocalStorageService _localStorageService;
  final UserDataService _userDataService;
  final MealPlanService _mealPlanService;
  final WaterIntakeSyncService _waterIntakeSyncService;
  final WeightTrackingSyncService _weightTrackingSyncService;
  final ShoppingListSyncService _shoppingListSyncService;

  @override
  Future<AppOperationResult> initializeFromStorage() async {
    try {
      // Load stored values
      final isOnboardingCompleted = _localStorageService.getBool('onboarding_completed') ?? false;
      final hasSeenIntroWizard = _localStorageService.getBool('has_seen_intro_wizard') ?? false;
      final isGuestUser = _localStorageService.getBool('is_guest_user') ?? false;
      final currentLanguage = _localStorageService.getString('language') ?? 'ar';
      final currentTheme = _localStorageService.getString('theme') ?? 'system';

      final appStateData = AppStateData(
        isOnboardingCompleted: isOnboardingCompleted,
        hasSeenIntroWizard: hasSeenIntroWizard,
        isGuestUser: isGuestUser,
        currentLanguage: currentLanguage,
        currentTheme: currentTheme,
      );

      return AppOperationResult(success: true, data: appStateData);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to initialize from storage: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> loadCompleteUserData(User user) async {
    try {
      UserProfile? userProfile;
      NotificationSettingsData? notificationSettingsData;
      PlanPreferences? planPreferences;

      // First, try to load from Firestore (most up-to-date)
      try {
        final completeUserData = await _userDataService.getCompleteUserData(user.uid);

        if (completeUserData != null) {
          try {
            // Extract the separate components
            userProfile = completeUserData.userProfile;
            planPreferences = completeUserData.planPreferences;
            notificationSettingsData = completeUserData.notificationSettingsData;

            // Cache the separate models locally
            if (userProfile != null) {
              await _localStorageService.saveUserProfile(userProfile.toJson());
              await _localStorageService.setOnboardingCompleted(userProfile.onboardingCompleted);
            }

            if (planPreferences != null) {
              await _localStorageService.savePlanPreferences(planPreferences.toJson());
            }

            if (notificationSettingsData != null) {
              await _localStorageService.saveNotificationSettings(notificationSettingsData.toJson());
            }

            // Sync all feature data
            await syncAllFeatureData();
          } catch (e) {
            // Critical error during user data processing - sign out user and show error
            await FirebaseAuth.instance.signOut();
            return AppOperationResult(
              success: false,
              error: 'حدث خطأ في تحميل بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.',
              data: AppStateData(
                isGuestUser: true,
                currentLanguage: 'ar',
                currentTheme: 'system',
              ),
            );
          }
        }
      } catch (firestoreError) {
        // Continue to try local storage
      }

      // If no Firestore profile, try local storage
      if (userProfile == null) {
        final cachedProfile = _localStorageService.loadUserProfile();
        final cachedPlanPreferences = _localStorageService.loadPlanPreferences();

        if (cachedProfile != null) {
          userProfile = UserProfile.fromJson(cachedProfile);
          // Try to sync local profile to Firestore (in background)
          Future.microtask(() async {
            try {
              await _userDataService.saveUserProfileWithoutPreferences(userProfile!);
              if (planPreferences != null) {
                await _userDataService.savePlanPreferences(planPreferences.toJson());
              }
            } catch (e) {
              // Don't throw error as this is a background operation
            }
          });
        }

        // Load cached plan preferences if available (independent of profile)
        if (cachedPlanPreferences != null) {
          try {
            planPreferences = PlanPreferences.fromJson(cachedPlanPreferences);
          } catch (e) {
            // Handle parsing error
          }
        }
      }

      // If still no profile, create a new one
      if (userProfile == null) {
        userProfile = UserProfile(
          id: user.uid,
          email: user.email ?? '',
          displayName: user.displayName ?? '',
          photoUrl: user.photoURL,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Cache the new profile locally
        await _localStorageService.saveUserProfile(userProfile.toJson());
        await _localStorageService.setOnboardingCompleted(userProfile.onboardingCompleted);

        // Save new profile to Firestore (in background)
        Future.microtask(() async {
          try {
            await _userDataService.saveUserProfileWithoutPreferences(userProfile!);
            if (planPreferences != null) {
              await _userDataService.savePlanPreferences(planPreferences.toJson());
            }
          } catch (e) {
            // Don't throw error as this is a background operation
          }
        });
      }

      // Load cached notification settings if available
      if (notificationSettingsData == null) {
        final cachedNotificationSettings = _localStorageService.loadNotificationSettings();
        if (cachedNotificationSettings != null) {
          notificationSettingsData = NotificationSettingsData.fromJson(cachedNotificationSettings);
        }
      }

      final appStateData = AppStateData(
        userProfile: userProfile,
        notificationSettingsData: notificationSettingsData,
        planPreferences: planPreferences,
      );

      return AppOperationResult(success: true, data: appStateData);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to load user profile: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> signInWithEmailAndPassword(String email, String password) async {
    try {
      final firebaseAuth = FirebaseAuth.instance;
      final credential = await firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        return await loadCompleteUserData(credential.user!);
      }

      return const AppOperationResult(
        success: false,
        error: 'Authentication failed - no user returned',
      );
    } on FirebaseAuthException catch (e) {
      return AppOperationResult(
        success: false,
        error: _getErrorMessage(e.code),
      );
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> signUpWithEmailAndPassword(String email, String password) async {
    try {
      final firebaseAuth = FirebaseAuth.instance;
      final credential = await firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        return await loadCompleteUserData(credential.user!);
      }

      return const AppOperationResult(
        success: false,
        error: 'Registration failed - no user returned',
      );
    } on FirebaseAuthException catch (e) {
      return AppOperationResult(
        success: false,
        error: _getErrorMessage(e.code),
      );
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> signInAsGuest() async {
    try {
      final firebaseAuth = FirebaseAuth.instance;
      final credential = await firebaseAuth.signInAnonymously();

      if (credential.user != null) {
        return await loadCompleteUserData(credential.user!);
      }

      return const AppOperationResult(
        success: false,
        error: 'Guest sign-in failed - no user returned',
      );
    } on FirebaseAuthException catch (e) {
      return AppOperationResult(
        success: false,
        error: _getErrorMessage(e.code),
      );
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> signOut() async {
    try {
      final firebaseAuth = FirebaseAuth.instance;
      await firebaseAuth.signOut();

      final result = await clearUserData();
      if (!result.success) {
        return result;
      }

      return const AppOperationResult(success: true);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to sign out: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> updateUserProfile(UserProfile updatedProfile, {bool isOnboardingCompletion = false}) async {
    try {
      // Cache updated profile locally
      await _localStorageService.saveUserProfile(updatedProfile.toJson());
      await _localStorageService.setOnboardingCompleted(updatedProfile.onboardingCompleted);

      // Save to Firestore using UserDataService
      await _userDataService.saveUserProfileWithoutPreferences(updatedProfile);

      return AppOperationResult(success: true, data: updatedProfile);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to update profile: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> updateNotificationSettings(NotificationSettingsData updatedSettings) async {
    try {
      // Cache updated settings locally
      await _localStorageService.saveNotificationSettings(updatedSettings.toJson());

      // Save to Firestore using UserDataService
      await _userDataService.saveNotificationSettings(updatedSettings.toJson());

      return AppOperationResult(success: true, data: updatedSettings);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to update notification settings: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> updatePlanPreferences(PlanPreferences updatedPreferences) async {
    try {
      // Cache updated preferences locally
      await _localStorageService.savePlanPreferences(updatedPreferences.toJson());

      // Save to Firestore using UserDataService
      await _userDataService.savePlanPreferences(updatedPreferences.toJson());

      return AppOperationResult(success: true, data: updatedPreferences);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to update plan preferences: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> completeOnboarding() async {
    try {
      await _localStorageService.setOnboardingCompleted(true);
      return const AppOperationResult(success: true);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to complete onboarding: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> setGuestUser({required bool isGuest}) async {
    try {
      await _localStorageService.setBool('is_guest_user', isGuest);
      return AppOperationResult(success: true, data: isGuest);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to set guest user status: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> completeIntroWizard() async {
    try {
      await _localStorageService.setBool('has_seen_intro_wizard', true);
      return const AppOperationResult(success: true);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to complete intro wizard: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> changeLanguage(String languageCode) async {
    try {
      await _localStorageService.setLanguage(languageCode);
      return AppOperationResult(success: true, data: languageCode);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to change language: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> changeTheme(String theme) async {
    try {
      await _localStorageService.setTheme(theme);
      return AppOperationResult(success: true, data: theme);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to change theme: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> syncFirestoreDataToLocalStorage() async {
    try {
      // Load plan preferences from Firestore using UserDataService
      final planPreferences = await _userDataService.getPlanPreferences();
      if (planPreferences != null) {
        await _localStorageService.setObject('plan_preferences', planPreferences);
      }

      // Sync shopping list data from Firestore
      await _shoppingListSyncService.syncShoppingListDataFromFirestore();

      return const AppOperationResult(success: true);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to sync Firestore data to local storage: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> syncAllFeatureData() async {
    try {
      // Load current meal plan from Firestore days collection using meal plan service
      final mealSyncSuccess = await _mealPlanService.syncMealPlanFromFirestore();

      // Load current date's water intake data from Firestore
      await _waterIntakeSyncService.validateAndUpdateDateHash();
      await _waterIntakeSyncService.loadTodaysWaterIntakeWithSync();

      // Load weight tracking data from Firestore
      await _weightTrackingSyncService.syncWeightTrackingDataFromFirestore();

      // Load shopping list data from Firestore
      await _shoppingListSyncService.syncShoppingListDataFromFirestore();

      return AppOperationResult(success: true, data: mealSyncSuccess);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to sync feature data: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> clearUserData() async {
    try {
      await _localStorageService.clearUserData();
      return const AppOperationResult(success: true);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to clear user data: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> resetPassword(String email) async {
    try {
      final firebaseAuth = FirebaseAuth.instance;
      await firebaseAuth.sendPasswordResetEmail(email: email);
      return const AppOperationResult(success: true);
    } on FirebaseAuthException catch (e) {
      return AppOperationResult(
        success: false,
        error: _getErrorMessage(e.code),
      );
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to reset password: $e',
      );
    }
  }

  @override
  Future<AppOperationResult> initializeDefaultNotificationSettings() async {
    try {
      final defaultSettings = NotificationSettingsFactory.createDefault();

      // Save to local storage
      await _localStorageService.saveNotificationSettings(defaultSettings.toJson());

      // Save to Firestore if user is authenticated using UserDataService
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        await _userDataService.saveNotificationSettings(defaultSettings.toJson());
      }

      return AppOperationResult(success: true, data: defaultSettings);
    } catch (e) {
      return AppOperationResult(
        success: false,
        error: 'Failed to initialize default notification settings: $e',
      );
    }
  }

  @override
  UserProfile extractCoreUserProfile(UserProfile firestoreProfile) {
    return UserProfile(
      id: firestoreProfile.id,
      email: firestoreProfile.email,
      displayName: firestoreProfile.displayName,
      photoUrl: firestoreProfile.photoUrl,
      phoneNumber: firestoreProfile.phoneNumber,
      preferredLanguage: firestoreProfile.preferredLanguage,
      unitSystem: firestoreProfile.unitSystem,
      isPremium: firestoreProfile.isPremium,
      onboardingCompleted: firestoreProfile.onboardingCompleted,
      createdAt: firestoreProfile.createdAt,
      updatedAt: firestoreProfile.updatedAt,
      lastLoginAt: firestoreProfile.lastLoginAt,
      premiumExpiryDate: firestoreProfile.premiumExpiryDate,
    );
  }

  @override
  PlanPreferences? parsePlanPreferences(UserProfile firestoreProfile) {
    // The old UserProfile model had these fields, but they're not in the new one
    // We need to extract them from the raw Firestore data if available
    // For now, return null - this will be improved when we update the Firestore service
    return null;
  }

  @override
  Future<NotificationSettingsData?> extractNotificationSettingsFromFirestore() async {
    try {
      // Get notification settings from Firestore user document using UserDataService
      final firestoreNotificationData = await _userDataService.getNotificationSettings();

      if (firestoreNotificationData != null && firestoreNotificationData.isNotEmpty) {
        // Found existing notification settings in Firestore
        return NotificationSettingsData.fromJson(firestoreNotificationData);
      } else {
        // No notification settings found, create defaults
        final defaultSettings = NotificationSettingsFactory.createDefault();

        // Save defaults to Firestore using UserDataService
        await _userDataService.saveNotificationSettings(defaultSettings.toJson());

        return defaultSettings;
      }
    } catch (e) {
      // Return defaults as fallback
      return NotificationSettingsFactory.createDefault();
    }
  }

  /// Extract notification settings from the merged Firestore UserProfile
  @override
  NotificationSettingsData? extractNotificationSettingsFromProfile(UserProfile firestoreProfile) {
    // The old UserProfile model had notification settings fields, but they're not in the new one
    // We need to extract them from the raw Firestore data if available
    // For now, return null - this will be improved when we update the Firestore service
    return null;
  }



  /// Get user-friendly error message
  String _getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}
