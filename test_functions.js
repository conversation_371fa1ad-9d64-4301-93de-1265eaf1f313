const axios = require('axios');

// Test Firebase Functions locally - Public endpoint (with preferences)
async function testMealPlanGeneration() {
  try {
    console.log('Testing public meal plan generation...');

    const response = await axios.post('http://127.0.0.1:5001/easydiet-app/us-central1/api/generate-meal-plan', {
      preferences: {
        calorieGoal: 2000,
        mealsPerDay: 3,
        dietType: 'balanced',
        proteinGoal: 150,
        carbsGoal: 250,
        fatGoal: 67,
        dietaryRestrictions: [],
        allergies: [],
        cuisinePreferences: ['عربي']
      },
      duration: 3
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 30000
    });

    console.log('Public endpoint success! Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response) {
      console.log('Public endpoint error:', error.response.status, error.response.data);
    } else {
      console.log('Public endpoint error:', error.message);
    }
  }
}

// Test authenticated meal plan generation (preferences from user profile)
async function testAuthenticatedMealPlanGeneration() {
  try {
    console.log('Testing authenticated meal plan generation...');

    const response = await axios.post('http://127.0.0.1:5001/easydiet-app/us-central1/api/meal-plans/generate', {
      duration: 3
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 30000
    });

    console.log('Authenticated endpoint success! Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (error.response) {
      console.log('Authenticated endpoint error:', error.response.status, error.response.data);
    } else {
      console.log('Authenticated endpoint error:', error.message);
    }
  }
}

// Test health endpoint
async function testHealth() {
  try {
    console.log('Testing health endpoint...');
    
    const response = await axios.get('http://127.0.0.1:5001/easydiet-app/us-central1/api/health');
    console.log('Health check:', response.data);
  } catch (error) {
    console.log('Health check failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testHealth();
  await testMealPlanGeneration();
  await testAuthenticatedMealPlanGeneration();
}

runTests();
