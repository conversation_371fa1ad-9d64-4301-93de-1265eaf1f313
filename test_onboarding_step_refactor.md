# Testing Onboarding Step Refactoring

## Test Scenarios

### 1. Unauthenticated User Flow
**Steps:**
1. Start app without authentication
2. Complete onboarding steps 1-5
3. Reach completion step (step 6) 
4. Trigger meal generation without authentication
5. Verify step 6 is saved to SharedPreferences (not FlutterSecureStorage)
6. Navigate to authentication page

**Expected Result:**
- Step saved using `localStorageService.saveCurrentOnboardingStep(6)`
- Data stored in SharedPreferences with key `current_onboarding_step`

### 2. Post-Authentication Resume Flow  
**Steps:**
1. Complete authentication after step 1
2. Verify step retrieval from SharedPreferences
3. Navigate to onboarding step 6
4. Complete onboarding successfully
5. Verify step is cleared after completion

**Expected Result:**
- Step retrieved using `localStorageService.loadCurrentOnboardingStep()`
- Navigation to `/onboarding?step=6`
- Step cleared after successful completion

### 3. Logout Data Cleanup
**Steps:**
1. Complete onboarding with saved step
2. Perform logout
3. Verify all user data is cleared including onboarding step

**Expected Result:**
- Both `storageService.clearUserData()` and `localStorageService.clearUserData()` called
- Onboarding step removed from SharedPreferences
- No duplicate clearing attempts

### 4. Error Handling
**Steps:**
1. Test with corrupted step data
2. Test with missing step data
3. Verify graceful fallbacks

**Expected Result:**
- No crashes with invalid data
- Proper fallback to step 1 when no saved step exists

## Verification Commands

```bash
# Check for any remaining references to secure storage methods
grep -r "storeCurrentOnboardingStep\|getCurrentOnboardingStep\|clearCurrentOnboardingStep" lib/ --exclude-dir=.dart_tool

# Verify local storage methods are used
grep -r "saveCurrentOnboardingStep\|loadCurrentOnboardingStep" lib/

# Run tests to ensure no regressions
flutter test

# Build to verify compilation
flutter build apk --debug
```

## Manual Testing Checklist

- [ ] Unauthenticated user can save onboarding step
- [ ] Authenticated user can resume from saved step  
- [ ] Step data persists across app restarts
- [ ] Logout properly clears all step data
- [ ] No secure storage references remain for onboarding steps
- [ ] App handles missing/corrupted step data gracefully
- [ ] Performance is maintained (no noticeable delays)

## Success Criteria

✅ **Functionality Maintained**: All onboarding flows work as before
✅ **Duplication Eliminated**: No duplicate storage methods
✅ **Consistent Storage**: All step data in SharedPreferences  
✅ **Proper Cleanup**: Complete data clearing on logout
✅ **No Regressions**: Existing features unaffected
