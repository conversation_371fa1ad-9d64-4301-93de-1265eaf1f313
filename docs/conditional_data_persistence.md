# Conditional Data Persistence Implementation

## Overview

The onboarding provider now implements conditional data persistence logic that automatically determines the appropriate storage strategy based on the user's authentication status.

## Implementation Details

### Core Method: `_saveData()`

The main `_saveData()` method in `OnboardingNotifier` now:

1. **Checks Authentication Status**: Uses `isAuthenticatedProvider` and `currentUserProvider` to determine if user is logged in
2. **Routes to Appropriate Flow**: Calls either `_saveDataForAuthenticatedUser()` or `_saveDataForGuestUser()`
3. **Handles Errors Gracefully**: Falls back to local storage if any errors occur
4. **Provides Debug Logging**: Comprehensive logging for troubleshooting

### Authenticated User Flow

When user is authenticated (`isAuthenticated = true` and `currentUser != null`):

1. **Firestore First**: Attempts to save data to Firestore using `FirestoreService`
   - Plan preferences saved to `users/{userId}/plan_preferences`
   - Notification settings saved to `users/{userId}/notification_settings`
2. **Local Storage Backup**: Always saves to local storage as backup, even if Firestore succeeds
3. **Error Handling**: If Firestore fails, continues with local storage; if both fail, throws exception

### Guest User Flow

When user is not authenticated (guest mode):

1. **Local Storage Only**: Saves data only to local storage
2. **Future Sync Ready**: Data format is consistent with Firestore format for easy sync when user logs in
3. **Error Handling**: If local storage fails, throws exception

### Data Structure

The implementation maintains consistent snake_case field names across both storage methods:

```json
{
  "plan_preferences": {
    "height": 175.0,
    "weight": 70.0,
    "activity_level": "moderate",
    "daily_calorie_goal": 2000,
    // ... other plan preferences
  },
  "notification_settings": {
    "meal_reminders": true,
    "water_reminders": true,
    "workout_reminders": false,
    "progress_updates": true
  }
}
```

### Key Methods

#### `_saveDataForAuthenticatedUser()`
- Attempts Firestore save first
- Falls back to local storage if Firestore fails
- Always saves to local storage as backup

#### `_saveDataForGuestUser()`
- Saves only to local storage
- Prepares data for future sync when user authenticates

#### `_saveToLocalStorage()`
- Helper method for consistent local storage operations
- Handles both plan preferences and notification settings
- Uses `LocalStorageService` for persistence

### Error Handling Strategy

1. **Firestore Errors**: Logged but don't prevent local storage save
2. **Local Storage Errors**: Always logged; throws exception if it's the only storage method
3. **Complete Failure**: Only throws if both Firestore and local storage fail for authenticated users
4. **Fallback Logic**: Main `_saveData()` method has additional fallback to guest user flow

### Debug Logging

Comprehensive logging throughout the flow:
- Authentication status detection
- Data preparation and structure
- Firestore save attempts and results
- Local storage operations
- Error conditions and fallbacks

### Usage

The conditional persistence is automatically triggered whenever:
- User completes onboarding steps
- User saves changes in edit mode
- Any onboarding data is updated

No changes required in calling code - the logic is transparent to the UI layer.

### Benefits

1. **Seamless Experience**: Users don't need to worry about authentication status
2. **Data Safety**: Always maintains local backup for offline access
3. **Cloud Sync**: Authenticated users get automatic cloud storage
4. **Offline Support**: Guest users can use the app fully offline
5. **Easy Migration**: Guest data can be easily synced when user logs in
6. **Robust Error Handling**: Graceful degradation when services are unavailable

### Testing

The implementation has been tested with:
- Authenticated user scenarios
- Guest user scenarios  
- Firestore service failures
- Local storage failures
- Mixed success/failure scenarios
- Navigation and edit form integration

All existing tests continue to pass, demonstrating backward compatibility.
