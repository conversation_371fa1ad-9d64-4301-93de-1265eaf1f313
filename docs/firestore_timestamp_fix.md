# Firestore Timestamp Conversion Fix

## Problem Description

The application was experiencing a runtime error when retrieving user profiles from Firestore:

```
FirestoreService: Error getting user profile: TypeError: Instance of 'Timestamp': type 'Timestamp' is not a subtype of type 'String'
```

### Root Cause

The issue occurred because:

1. **Firestore Storage**: Firestore stores DateTime fields as `Timestamp` objects
2. **Model Expectation**: The `UserProfile.fromJson()` method expects DateTime fields to be in string format (ISO 8601)
3. **Type Mismatch**: When Firestore data was directly passed to `UserProfile.fromJson()`, the `Timestamp` objects caused type conversion errors

### Affected Fields

The `UserProfile` model contains several DateTime fields that were affected:
- `createdAt` (required)
- `updatedAt` (required)
- `lastLoginAt` (nullable)
- `premiumExpiryDate` (nullable)

## Solution Implemented

### 1. Added Timestamp Conversion Method

Added a private helper method `_convertFirestoreTimestamps()` to the `FirestoreService` class:

```dart
/// Convert Firestore Timestamp objects to DateTime-compatible format for UserProfile
Map<String, dynamic> _convertFirestoreTimestamps(Map<String, dynamic> data) {
  final convertedData = Map<String, dynamic>.from(data);
  
  // List of DateTime fields in UserProfile that need conversion
  final dateTimeFields = [
    'createdAt',
    'created_at',
    'updatedAt', 
    'updated_at',
    'lastLoginAt',
    'last_login_at',
    'premiumExpiryDate',
    'premium_expiry_date',
  ];
  
  for (final field in dateTimeFields) {
    if (convertedData.containsKey(field) && convertedData[field] != null) {
      final value = convertedData[field];
      if (value is Timestamp) {
        // Convert Firestore Timestamp to ISO 8601 string format
        convertedData[field] = value.toDate().toIso8601String();
      }
      // If it's already a String or DateTime, leave it as-is
    }
  }
  
  return convertedData;
}
```

### 2. Updated getUserProfile Method

Modified the `getUserProfile()` method to use the conversion:

```dart
// Before (causing error):
return UserProfile.fromJson(mergedData);

// After (with conversion):
final convertedData = _convertFirestoreTimestamps(mergedData);
return UserProfile.fromJson(convertedData);
```

### 3. Updated watchUserProfile Method

Also updated the `watchUserProfile()` stream method:

```dart
// Before:
return UserProfile.fromJson(mergedData);

// After:
final convertedData = _convertFirestoreTimestamps(mergedData);
return UserProfile.fromJson(convertedData);
```

## Technical Details

### Conversion Logic

The conversion method:

1. **Creates a copy** of the original data to avoid mutations
2. **Iterates through known DateTime fields** (both camelCase and snake_case variants)
3. **Checks for Timestamp objects** and converts them to ISO 8601 strings
4. **Preserves other data types** unchanged
5. **Handles null values** gracefully

### Field Name Variants

The method handles both naming conventions:
- `createdAt` / `created_at`
- `updatedAt` / `updated_at`
- `lastLoginAt` / `last_login_at`
- `premiumExpiryDate` / `premium_expiry_date`

### Type Safety

The conversion:
- ✅ **Preserves existing strings**: If a field is already a string, it's left unchanged
- ✅ **Handles null values**: Null values are preserved as null
- ✅ **Only converts Timestamps**: Other data types are left unchanged
- ✅ **Maintains data integrity**: No data loss during conversion

## Benefits

### 1. **Error Resolution**
- ✅ Eliminates the `TypeError: Instance of 'Timestamp': type 'Timestamp' is not a subtype of type 'String'` error
- ✅ Allows successful user profile retrieval from Firestore

### 2. **Backward Compatibility**
- ✅ Works with existing data that might already be in string format
- ✅ Handles mixed data scenarios gracefully
- ✅ No breaking changes to existing code

### 3. **Future-Proof**
- ✅ Handles both camelCase and snake_case field names
- ✅ Easy to extend for additional DateTime fields
- ✅ Consistent conversion logic across all user profile operations

### 4. **Performance**
- ✅ Minimal overhead (only processes known DateTime fields)
- ✅ Efficient conversion using built-in Timestamp methods
- ✅ No unnecessary data copying for non-DateTime fields

## Testing

### Verification Methods

1. **Existing Tests**: All existing navigation and user profile tests continue to pass
2. **Runtime Testing**: User profile retrieval now works without errors
3. **Type Safety**: Converted data successfully creates UserProfile objects

### Test Results

```
✅ Plan Settings Navigation Tests: PASSED
✅ User Profile Retrieval: No more Timestamp errors
✅ Compilation: No errors, only minor linting suggestions
```

## Usage

The fix is automatically applied whenever:
- `getUserProfile()` is called
- `watchUserProfile()` stream emits data
- User profile data is retrieved from Firestore

No changes required in calling code - the conversion is transparent to the rest of the application.

## Related Files

- **Primary Fix**: `lib/core/services/firestore_service.dart`
- **Model Definition**: `lib/shared/models/user_profile.dart`
- **Usage Examples**: Various user profile and authentication providers

## Future Considerations

If additional DateTime fields are added to the UserProfile model:

1. Add the field name (both camelCase and snake_case) to the `dateTimeFields` list
2. The conversion will automatically handle the new fields
3. No other changes required

This fix ensures robust handling of Firestore Timestamp objects and provides a foundation for consistent DateTime handling across the application.
