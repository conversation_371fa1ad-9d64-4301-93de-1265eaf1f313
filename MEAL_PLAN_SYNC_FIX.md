# Meal Plan Sync Fix Documentation

## Issue Description
The meal plan synchronization was failing with two main problems:
1. Local storage being saved as `{"days": []}` despite having valid meal plan data in Firestore
2. AuthNotifier error: "Converting object to an encodable object failed: Instance of 'Timestamp'"

## Root Cause
The issue had two parts:

### Part 1: Data Model JSON Deserialization
Two DateTime fields were missing proper Firestore Timestamp converters:
1. `DayMealPlan.date` - Missing `@TimestampOrStringConverter()` annotation
2. `GeneratedMeal.consumedAt` - Using non-nullable converter for a nullable field

### Part 2: Firestore Timestamp Serialization
When syncing data from Firestore to local storage, the AuthNotifier was trying to JSON encode data containing Firestore Timestamp objects, which are not JSON-serializable.

## Files Modified

### 1. `lib/features/meal_planning/data/models/meal_plan_request.dart`

#### Added New Converter Class
```dart
/// Custom JSON converter to handle nullable DateTime fields with Firestore Timestamp and ISO String formats
class NullableTimestampOrStringConverter implements JsonConverter<DateTime?, dynamic> {
  const NullableTimestampOrStringConverter();

  @override
  DateTime? fromJson(dynamic json) {
    if (json == null) {
      return null;
    }
    if (json is Timestamp) {
      return json.toDate();
    } else if (json is String) {
      return DateTime.parse(json);
    } else if (json is Map<String, dynamic>) {
      if (json.containsKey('seconds') && json.containsKey('nanoseconds')) {
        final seconds = json['seconds'] as int;
        final nanoseconds = json['nanoseconds'] as int;
        return DateTime.fromMillisecondsSinceEpoch(
          seconds * 1000 + (nanoseconds / 1000000).round(),
        );
      }
    }
    throw ArgumentError('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime? dateTime) {
    return dateTime?.toIso8601String();
  }
}
```

#### Updated Model Fields
```dart
// DayMealPlan - Added missing converter
@TimestampOrStringConverter() required DateTime date,

// GeneratedMeal - Changed to nullable converter
@JsonKey(name: 'consumed_at') @NullableTimestampOrStringConverter() DateTime? consumedAt,
```

### 2. `lib/shared/providers/auth_provider.dart`

#### Added Timestamp Conversion Helper
```dart
/// Convert Firestore Timestamp objects to ISO strings for JSON serialization
Map<String, dynamic> _convertTimestampsToStrings(Map<String, dynamic> data) {
  final result = <String, dynamic>{};

  for (final entry in data.entries) {
    final key = entry.key;
    final value = entry.value;

    if (value is Timestamp) {
      // Convert Timestamp to ISO string
      result[key] = value.toDate().toIso8601String();
    } else if (value is Map<String, dynamic>) {
      // Recursively convert nested maps
      result[key] = _convertTimestampsToStrings(value);
    } else if (value is List) {
      // Handle lists that might contain maps with timestamps
      result[key] = value.map((item) {
        if (item is Map<String, dynamic>) {
          return _convertTimestampsToStrings(item);
        } else if (item is Timestamp) {
          return item.toDate().toIso8601String();
        }
        return item;
      }).toList();
    } else {
      // Keep other values as-is
      result[key] = value;
    }
  }

  return result;
}
```

#### Updated Sync Method
```dart
// Load plan preferences from Firestore
final planPreferences = await _firestoreService.getPlanPreferences();
if (planPreferences != null) {
  // Convert any Timestamp objects to ISO strings for JSON serialization
  final cleanedPreferences = _convertTimestampsToStrings(planPreferences);
  await _storageService.setObject('plan_preferences', cleanedPreferences);
  AppLogger.info('AuthNotifier: Plan preferences synced to local storage');
}
```

## Testing
Created comprehensive tests in `test/meal_plan_sync_test.dart` to verify:
- Firestore Timestamp handling
- ISO string backward compatibility
- Nullable DateTime field handling
- Complete serialization/deserialization cycle
- Timestamp conversion helper with nested data structures

All tests pass successfully.

## Expected Behavior After Fix
1. Firestore documents with Timestamp objects can be properly deserialized
2. Local storage contains actual meal plan data instead of empty days array
3. Sync operations complete successfully without JSON conversion errors
4. Backward compatibility maintained for ISO string dates
5. AuthNotifier can sync Firestore data to local storage without Timestamp serialization errors
6. Plan preferences and notification settings sync correctly from Firestore
7. Meal plan reconstruction handles both camelCase and snake_case field names
8. Flexible date parsing supports both Firestore Timestamp and ISO string formats
9. Robust meal parsing using compatibility methods handles missing fields gracefully

## Verification Steps
1. Run the app
2. Authenticate a user with existing meal plan data in Firestore
3. Navigate to home page (triggers sync)
4. Verify local storage contains proper meal plan data
5. Check that meal plans display correctly in the UI

## Code Generation
After making model changes, regenerated code with:
```bash
dart run build_runner build --delete-conflicting-outputs
```

## Impact
- ✅ Fixes meal plan sync from Firestore to local storage
- ✅ Resolves AuthNotifier Timestamp serialization errors
- ✅ Maintains backward compatibility with existing data
- ✅ Proper handling of nullable DateTime fields
- ✅ Enables proper sync of plan preferences and notification settings
- ✅ No breaking changes to existing functionality
- ✅ Comprehensive test coverage added
